# 直播间文档模板

## 项目的背景

通过直播的形式，利用主播边讲课边带动VIP会员的销售。

## 项目的术语定义

直播间：h5直播间、原生直播间

直播间类型：常规直播、模考直播、vip用户直播、考前辅导直播等

场次：每次直播都需要建一个场次，需要设定直播时间，非直播间时间放预热时候或者回放

回放：直播结束后，回放视频生成就开始播放直播回放

弹幕消息：通过mqtt通道接收服务端实时广播消息，完成弹幕、商品弹窗等消息接收

运营位：后台设置直播间运营位，可售卖的VIP商品、课程商品、直接打开链接

预录播：设定特定时间播放特定视频，以直播形式

## 项目的部署

驾考宝典直播间：[https://laofuzi.kakamobi.com/jiakaobaodian-zhibojian/](https://laofuzi.kakamobi.com/jiakaobaodian-zhibojian/)

驾考宝典直播间-测试：[https://laofuzi-ttt.kakamobi.com/jiakaobaodian-zhibojian/](https://ttt.kakamobi.com/laofuzi.ttt.kakamobi.com/jiakaobaodian-zhibojian/)

驾考宝典直播间-站外：[https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-zhibojian/](https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-zhibojian/)

git：[https://git.mucang.cn/jiakaobaodian-webfront/jiakaobaodian-zhibojian](https://git.mucang.cn/jiakaobaodian-webfront/jiakaobaodian-zhibojian)

## 项目的运行环境

运行在驾考宝典APP、微信浏览器

## 项目的技术选型

使用vue2+vue-cli4 全家桶

## 项目的逻辑视图

1、**Mqtt 聊天室消息**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069564028.jpg!1000x0)

2、**VIP售卖逻辑**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069642262.jpg!1000x0)![](https://web-resource.mc-cdn.cn/web/md-image/1741069613564.jpg!1000x0)![](https://web-resource.mc-cdn.cn/web/md-image/1741069605580.jpg!1000x0)

3、**返回按钮监听**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069666530.jpg!1000x0)

4、**运营位显示逻辑**

![](https://web-resource.mc-cdn.cn/web/md-image/1741069704267.jpg!1000x0)

## 项目的参考资料

[《直播间mqtt消息类型》](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWd6LRawjiA1LAONqJZd1wyK0)

[《直播间使用运营位购买模块整理》](https://alidocs.dingtalk.com/i/nodes/KGZLxjv9VG67YpzLikbGA2z9W6EDybno)