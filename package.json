{"name": "zhibojian-talkroom", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service build --mode=development --watch --no-clean", "bundler:dev": "vue-cli-service build --mode=dev", "bundler:test": "vue-cli-service build --mode=test", "bundler": "vue-cli-service build", "lint": "vue-cli-service lint", "report": "vue-cli-service build --report --static", "pretend": "@simplex/simple-node"}, "dependencies": {"@jiakaobaodian/jiakaobaodian-utils": "^1.1.8", "@simplex/simple-base": "6.4.9", "@simplex/simple-base-sso": "3.0.2", "@simplex/simple-core": "4.0.22", "@simplex/simple-mcprotocol": "3.6.1", "@simplex/simple-mock": "1.0.1", "@simplex/simple-oort": "4.3.9", "@simplex/simple-pay": "^1.0.4", "core-js": "^3.6.5", "lodash-es": "^4.17.21", "lottie-web": "^5.8.1", "stickyfilljs": "^2.1.0", "swiper": "^5.4.5", "vconsole": "~3.1.0", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-virtual-scroller": "^1.0.10", "vuex": "^3.6.2", "html2canvas": "^1.4.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "glob": "^7.1.7", "html-webpack-plugin": "^4.5.2", "less": "^4.1.1", "less-loader": "^5.0.0", "postcss-pxtorem": "^5.1.1", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.8.0", "zip-webpack-plugin": "^3.0.0"}, "browserslist": ["iOS >= 8", "Android > 4"], "projectConfig": {"static": ["static"], "bundle": {"zip": true, "dir": "../"}, "style": {"remUnit": 100, "baseWidth": 750}}}