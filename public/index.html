<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,user-scalable=no,viewport-fit=cover"
        />
        <meta http-equiv="X-UA-Compatible" content="edge" />
        <meta name="renderer" content="webkit" />
        <meta name="format-detection" content="telephone=no, email=no" />
        <script
            async
            src="https://share-m.kakamobi.com/activity.kakamobi.com/qichebaojiazhijia-base-common/source/resource/js/perf.min.js"
        ></script>
        <title></title>
    </head>

    <body>
        <script>
            function addClass(elem, str) {
                var classNames = elem.className ? elem.className.split(' ') : []
                if (classNames.indexOf(str) === -1) {
                    classNames.push(str)
                    elem.className = classNames.join(' ')
                }
            }
            function removeClass(elem, str) {
                var classNames = elem.className ? elem.className.split(' ') : []
                var index = classNames.indexOf(str)
                if (index !== -1) {
                    classNames.splice(index, 1)
                    elem.className = classNames.join(' ')
                }
            }
            var ua = window.navigator.userAgent.toLowerCase()
            var isAndroid = ua.indexOf('android') > -1
            var isTablet = /(?:ipad|playbook)/.test(ua) || (ua.indexOf('android') > -1 && !/(?:mobile)/.test(ua)) || (ua.indexOf('android') > -1 && document.documentElement.clientWidth >= 480)
            var SetBaseWidth = function () {

                var isLandscape = Math.abs(window.orientation) == 90
                var clientWidth = document.documentElement.clientWidth
                var clientHeight = document.documentElement.clientHeight
                // 高度小于700当作半截弹窗
                var isDialog = clientHeight < 700

                if (isTablet && clientWidth/clientHeight > 76/100 && isDialog) {
                    clientWidth = isLandscape ? Math.min(clientWidth, clientHeight, 480) : Math.min(clientWidth, 480)
                    addClass(document.body, 'width-limit')
                } else {
                    clientWidth = isLandscape ? Math.min(clientWidth, clientHeight) : clientWidth
                    removeClass(document.body, 'width-limit')
                }
                var baseFontSize = 100 * (clientWidth / <%= BASE_WIDTH %>);

                document.documentElement.style.fontSize = baseFontSize + 'px';

            }
            SetBaseWidth();
            window.addEventListener('resize', function () {
                SetBaseWidth()
            }, false);
            setTimeout(function() {
                SetBaseWidth();
            }, 500);
        </script>
        <style>
            body.width-limit {
                background-color: #fff;
                max-width: 7.5rem;
            }
        </style>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>
</html>
