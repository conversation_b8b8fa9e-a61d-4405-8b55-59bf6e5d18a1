<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,user-scalable=no,viewport-fit=cover"
        />
        <meta http-equiv="X-UA-Compatible" content="edge" />
        <meta name="renderer" content="webkit" />
        <meta name="format-detection" content="telephone=no, email=no" />
        <script
            async
            src="https://share-m.kakamobi.com/activity.kakamobi.com/qichebaojiazhijia-base-common/source/resource/js/perf.min.js"
        ></script>
        <title>加载中</title>
    </head>

    <body>
        <script>
            function hasClass(elem, str) {
                var classNames = elem.className ? elem.className.split(' ') : []
                return classNames.indexOf(str) > -1
            }
            function addClass(elem, str) {
                var classNames = elem.className ? elem.className.split(' ') : []
                if (classNames.indexOf(str) === -1) {
                    classNames.push(str)
                    elem.className = classNames.join(' ')
                }
            }
            function removeClass(elem, str) {
                var classNames = elem.className ? elem.className.split(' ') : []
                var index = classNames.indexOf(str)
                if (index !== -1) {
                    classNames.splice(index, 1)
                    elem.className = classNames.join(' ')
                }
            }
            var ua = window.navigator.userAgent.toLowerCase()
            var isAndroid = ua.indexOf('android') > -1
            var isTablet = /(?:ipad|playbook)/.test(ua) || (ua.indexOf('android') > -1 && !/(?:mobile)/.test(ua)) || (ua.indexOf('android') > -1 && document.documentElement.clientWidth >= 480)
            var SetBaseWidth = function () {
                var isLandscape = Math.abs(window.orientation) == 90
                var clientWidth = document.documentElement.clientWidth
                var clientHeight = document.documentElement.clientHeight
                if (!hasClass(document.body, 'vertical')) {
                    if(isTablet) {
                        addClass(document.body, 'tablet')
                        addClass(document.body, 'portrait')
                    } else {
                        if(isLandscape) {
                            addClass(document.body, 'landscape')
                        } else {
                            addClass(document.body, 'portrait')
                        }
                    }
                }
                // if (isTablet && clientWidth/clientHeight > 76/100) {
                //     clientWidth = isLandscape ? Math.min(clientWidth, clientHeight, 375) : Math.min(clientWidth, 375)
                //     addClass(document.body, 'width-limit')
                // } else {
                //     clientWidth = isLandscape ? Math.min(clientWidth, clientHeight) : clientWidth
                //     removeClass(document.body, 'width-limit')
                // }
                var shrinkRatio = 1
                if(76/100 < clientWidth/clientHeight) {
                    shrinkRatio = Math.min(clientWidth/clientHeight, 1)
                }
                clientWidth = isLandscape ? Math.min(clientWidth, clientHeight, 480 * shrinkRatio) : Math.min(clientWidth, 480 * shrinkRatio)
                var baseFontSize = 100 * (clientWidth / <%= BASE_WIDTH %>);

                document.documentElement.style.fontSize = baseFontSize + 'px';

            }

            SetBaseWidth();
            if (isTablet) {
                var width = document.documentElement.clientWidth
                window.addEventListener('resize', function () {
                    setTimeout(function() {
                        if (width !== document.documentElement.clientWidth) {
                            width = document.documentElement.clientWidth
                            SetBaseWidth()
                        }
                    }, 250)
                }, false);
            }
        </script>
        <style>
            /* body.width-limit:not(.landscape) {
                background-color: #000;
                max-width: 7.5rem;
            } */
        </style>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>
</html>
