@rem: 32rem;

/**
varable
 */
.ovh() {
    text-overflow: ellipsis;
    white-space  : nowrap;
    overflow     : hidden;
}

.ovhMulti(@num) {
    overflow          : hidden;
    text-overflow     : ellipsis;
    display           : -webkit-box;
    -webkit-line-clamp: @num;
    -webkit-box-orient: vertical;
}

.xCenter() {
    transform: translate3d(-50%, 0, 0);
}

.yCenter() {
    transform: translate3d(0, -50%, 0);
}

.xyCenter() {
    transform: translate3d(-50%, -50%, 0);
}

//background top to bottom
.bgTB(@1, @2) {
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, @1), color-stop(100, @2));
    background-image: -webkit-linear-gradient(top, @1 0%, @2 100%);
    background-image: linear-gradient(to bottom, @1 0%, @2 100%);
}

.bgTBHalf(@1, @2) {
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, @1), color-stop(60, @2));
    background-image: -webkit-linear-gradient(top, @1 0%, @2 60%);
    background-image: linear-gradient(to bottom, @1 0%, @2 60%);
}

.bgLR(@1, @2) {
    background-image: -webkit-gradient(linear, left top, right top, color-stop(0, @1), color-stop(100, @2));
    background-image: -webkit-linear-gradient(left, @1 0%, @2 100%);
    background-image: linear-gradient(to right, @1 0%, @2 100%);
}

.rotate(@deg) {
    -webkit-transform: rotate(@deg);
    transform        : rotate(@deg);
}

/**
animate css
 */
/*animate*/
.animated.infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count        : infinite;
}

.animated.hinge {
    -webkit-animation-duration: 2s;
    animation-duration        : 2s;
}

@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform        : rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform        : rotate(360deg);
    }
}

@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform        : rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform        : rotate(360deg);
    }
}

/**
base css
 */
* {
    box-sizing        : border-box;
    -webkit-box-sizing: border-box;
    margin            : 0;
    padding           : 0;
}

html {
    min-height: 100%;
    font-size : 16px;
}

body {
    min-width                  : 320px;
    // max-width                  : 1280px;
    margin                     : 0 auto !important;
    font-weight                : normal;
    font-size                  : 28px;
    font-family                : -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", "Helvetica", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
    line-height                : 1.5;
    color                      : #333;
    word-wrap                  : break-word;
    -webkit-text-size-adjust   : none;
    -webkit-tap-highlight-color: transparent;
}

a:link,
a:visited {
    color          : #4F4F4F;
    text-decoration: none;
}

img {
    vertical-align: middle;
    width         : 100%;
}

.clearfix:after {
    content: "";
    display: table;
    clear  : both;
}

select {
    -webkit-appearance: none;
    appearance        : none;
    background        : url(../images/slct-down.png) no-repeat center right #fff;
    background-size   : 32px auto;
    border-radius     : 0;
}

input {
    border-radius: 0;
}

textarea:focus,
input:focus,
select:focus,
button {
    outline: none;
    border: 0;
}

ol {
    padding-inline-start: 40px;
}

.none {
    display: none;
}

::-webkit-scrollbar {
    width : 0 !important;
    height: 0 !important;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin            : 0;
}

input::-webkit-input-placeholder {
    color: #999;
}

input[type=number],
input[type=text],
input[type=tel],
input[type=email] {
    outline           : none;
    -webkit-appearance: none;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.ft12 {
    font-size: 24px;
}
