<template>
    <popup
        class="model-captcha"
        :position="'center'"
        :show.sync="visible"
        :closeOnBlank="false"
        :showClose="false"
    >
        <div class="captcha" id="captcha" ref="captcha"></div>
    </popup>
</template>

<script>
import {once} from 'lodash-es'
import popup from './dialog'
import {loadScript} from '../utils/tools'

let loadSdk = once(function() {
    return new Promise(resolve => {
        loadScript('https://cstaticdun.126.net/load.min.js?t=' + new Date().getTime()).then(() => {
            resolve()
        })
    })
})

export default {
    components: {popup},
    data() {
        return {
            visible: false,
        }
    },
    props: {
        show: <PERSON><PERSON>an,
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                this.verify()
            }
        },
    },
    methods: {
        close() {
            this.$emit('update:show', false)
        },
        async verify() {
            await loadSdk()
            const captcha = this.$refs.captcha

            window.initNECaptcha(
                {
                    captchaId: '6f92317b6e7d4f4faa77a360d65826c5',
                    element: captcha,
                    mode: 'embed',
                    width: '320px',
                    onVerify: (err, data) => {
                        setTimeout(() => {
                            if (!err && data !== undefined) {
                                this.close()
                                this.$emit('validateDone', data.validate)
                            }
                        }, 600)
                    },
                },
                function onload(instance) {
                    console.log(instance, 'instanceinstance')
                },
                function onerror(err) {
                    console.log(err, 'errer')
                }
            )
        },
    },
}
</script>
<style lang="less" scoped>
.captcha {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: white;
}
</style>
