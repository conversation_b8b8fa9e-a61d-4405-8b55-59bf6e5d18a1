<template>
    <div>
        <popup
            class="input-popup"
            :position="'bottom'"
            @closed="closed"
            :isInDialog="isInDialog"
            :show.sync="visible"
        >
            <div class="header">领奖信息</div>
            <div class="content" v-if="obtainPrizeInfo.prizeName">
                <div class="bann">
                    <div class="info">
                        <div>奖品名称：<span>{{ obtainPrizeInfo.prizeName }}</span
                        ></div>
                        <div>中奖时间：{{ formatDate(winPrizeTime) }}</div>
                    </div>
                    <div class="image"><img :src="obtainPrizeInfo.prizeImg" /></div>
                </div>
                <div class="desc" v-if="!isSubmit">
                    以上奖品属于实物奖品，会采取快递寄送的方式送达，请您务必填写真实准确的地址信息，提交后无法更改，请谨慎填写！
                </div>
                <div class="form">
                    <div class="line">
                        <label>收件人</label>
                        <input
                            :disabled="isSubmit"
                            class="input"
                            v-model="name"
                            maxlength="6"
                            placeholder="请填写收件人姓名"
                            type="text"
                        />
                    </div>
                    <div class="line">
                        <label>手机号</label>
                        <input
                            :disabled="isSubmit"
                            class="input"
                            v-model="phone"
                            maxlength="11"
                            @focus="clearPlaceholder('phone')"
                            :placeholder="phoneMask || '请填写手机号'"
                            type="text"
                        />
                    </div>
                    <div class="line">
                        <label>所在地区</label>
                        <div
                            @click.stop="openAddressSelect"
                            class="input address"
                            :class="{holder: !addressText, disabled: isSubmit}"
                            :value="addressText"
                        >
                            {{ addressText || '请选择所在地区' }}
                        </div>
                    </div>
                    <div class="line">
                        <label>详细地址</label>
                        <input
                            :disabled="isSubmit"
                            class="input"
                            v-model="street"
                            @focus="clearPlaceholder('street')"
                            :placeholder="streetMask || '请填写详细地址'"
                            type="text"
                        />
                    </div>
                </div>
                <div class="btn" v-if="!isSubmit" @click="submit">提交</div>
                <div class="btn settle-down" v-else>以上信息已提交</div>
                <div v-if="!isSubmit" class="agreement">
                    <template>
                        <input
                            class="regular-checkbox"
                            id="agreement"
                            v-model="readed"
                            type="checkbox"
                        />
                        <label class="regular-label" for="agreement"></label>
                    </template>
                    <label for="agreement"
                        >已阅读并同意
                        <span @click.prevent="openAgreement1">《个人信息保护声明》</span
                        >以及<span @click.prevent="openAgreement2">《隐私政策》</span>
                    </label>
                </div>
                <div v-else class="desc">
                    工作人员收到您的领奖信息后，会在第一时间安排发出。奖品的到货时间一般为5-10个工作日，请您耐心等待。如有疑问，可联系客服寻求帮助。
                </div>
            </div>
        </popup>
        <popup
            class="success-popup"
            :position="'center'"
            :show.sync="successVisible"
            @closed="successClosed"
        >
            <div class="wrap">
                <div class="title">提交成功</div>
                <div class="desc">工作人员收到您的领奖信息后，会在第一时间安排发出。奖品的到货时间一般为5-10个工作日，请您耐心等待。如有疑问，可联系客服寻求帮助。</div>
                <div class="btn" @click="successClosed">我知道了</div>
            </div>
        </popup>

        <addressSelect v-if="showAddressSelect" @complete="complete" @cancel="cancel" />
    </div>
</template>

<script>
import {toast, webOpen, formatDate, trackEvent} from '../utils/tools'
import {webClose} from '../utils/jump'
import popup from '../components/dialog'
import addressSelect from './addressSelect.vue'
import {fillInAddress} from '../server/active.js'

export default {
    components: {popup, addressSelect},
    data() {
        return {
            visible: false,
            name: '',
            phone: '',
            phoneMask: '',
            street: '',
            streetMask: '',
            readed: false,
            isSubmit: false,
            showAddressSelect: false,
            successVisible: false,
            districtVal: {
                provinceVal: '',
                cityVal: '',
                areaVal: '',
            },
        }
    },
    model: {
        prop: 'show',
        event: 'setShow',
    },
    props: {
        show: Boolean,
        isInDialog: Boolean,
        winRecordPrizeId: Number,
        winPrizeTime: Number,
        obtainPrizeInfo: Object,
        inputData: Object,
        mode: {
            type: String,
            default: 'add',
        },
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                if (this.mode === 'edit') {
                    this.name = this.inputData.addressee
                    this.phone = ''
                    this.phoneMask = this.inputData.phone
                    this.districtVal = this.inputData.districtVal
                    this.street = ''
                    this.streetMask = this.inputData.addressDetail
                }

                let fragmentName1 = '领奖信息弹窗'
                let actionType = '出现'

                // 埋点梳理-驾考宝典-v8.36
                // 精品课直播间页_领奖信息弹窗_出现
                trackEvent({fragmentName1, actionType})
            }
        },
    },
    computed: {
        addressText() {
            if (
                this.districtVal.provinceVal.name &&
                this.districtVal.cityVal.name &&
                this.districtVal.areaVal.name
            ) {
                return (
                    this.districtVal.provinceVal.name +
                    this.districtVal.cityVal.name +
                    this.districtVal.areaVal.name
                )
            } else {
                return ''
            }
        },
    },
    methods: {
        closed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.close()
            }
        },
        successClosed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.successVisible = false
                this.close()
            }
        },
        async submit() {
            if (!this.readed) {
                toast('请您先同意《个人信息保护声明》以及《隐私政策》协议')
                return
            }
            if (!/^[\u4e00-\u9fa5]{2,6}$/.test(this.name)) {
                toast('请输入中文姓名')
                return
            }

            if (!/(1[3-9]\d|999)\d{8}/.test(this.phone) && !this.phoneMask) {
                toast('请填写正确的手机号')
                return
            }

            if (!this.addressText) {
                toast('请选择完整地区')
                return
            }

            if (!this.street && !this.streetMask) {
                toast('请填写详细地址')
                return
            }
            const resData = await fillInAddress({
                addressProvince: this.districtVal.provinceVal.code,
                addressCity: this.districtVal.cityVal.code,
                addressArea: this.districtVal.areaVal.code,
                winPrizeId: this.winRecordPrizeId,
                // addressArea: this.addressText,
                addressDetail: this.street,
                addressee: this.name,
                phone: this.phone,
            })
            if (resData.value) {
                this.isSubmit = true
                this.successVisible = true
            } else {
                toast('提交失败，请稍后重试')
            }
            let fragmentName1 = '领奖信息弹窗'
            let actionType = '点击'
            let actionName = '提交'

            // 埋点梳理-驾考宝典-v8.36
            // 精品课直播间页_领奖信息弹窗_点击提交
            trackEvent({fragmentName1, actionType, actionName})
        },
        clearPlaceholder(type) {
            this[type + 'Mask'] = ''
        },
        close() {
            this.$emit('update:show', false)
            if (this.isSubmit) {
                this.$emit('submitSuccess')
            }
        },
        formatDate(date) {
            if (date) {
                return formatDate(date, 'yyyy.MM.dd hh:mm')
            } else {
                return ''
            }
        },
        cancel() {
            this.showAddressSelect = false
        },
        complete(districtVal) {
            this.showAddressSelect = false
            this.districtVal = districtVal
        },
        openAddressSelect() {
            if (this.isSubmit) {
                return
            }
            this.showAddressSelect = true
        },
        openAgreement1() {
            webOpen({
                url:
                    'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdUserProtect',
                titleBar: true,
                title: '个人信息保护声明',
            })
        },
        openAgreement2() {
            webOpen({
                url:
                    'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdPrivateAgreement',
                titleBar: true,
                title: '隐私政策',
            })
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.input-popup {
    /deep/ .dialog {
        border-radius: 20px 20px 0 0;
        background: #fff;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 40px);
    }
    .header {
        font-weight: bold;
        .fontSizeWithElder(40px);
        text-align: center;
        padding: 40px 0 20px;
    }
    .content {
        padding-bottom: 30px;
        .bann {
            margin: 10px auto 0;
            background: url(../assets/images/award-bg.png) no-repeat center;
            background-size: 690px 156px;
            width: 690px;
            height: 156px;
            position: relative;
        }
        .image {
            width: 184px;
            height: 184px;
            overflow: hidden;
            position: absolute;
            right: 20px;
            bottom: 0;
        }
        .info {
            padding: 26px 0 0 30px;
            .fontSizeWithElder(24px);
            color: #fff;
            line-height: 1.8;
            margin-right: 200px;
            > div {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            span {
                .fontSizeWithElder(36px);
                font-weight: bold;
            }
        }
        .desc {
            margin: 20px 30px 0;
            color: #fa0431;
            .fontSizeWithElder(26px);
        }
        .form {
            padding: 0 30px;

            .line {
                border-bottom: 1px solid #efefef;
                display: flex;
                align-items: stretch;
                height: 108px;
                label {
                    display: flex;
                    align-items: center;
                    width: 180px;
                }
                .input {
                    flex: 1;
                    &.address {
                        background: url(../assets/images/corner-r.png) no-repeat right center;
                        background-size: 48px 50px;
                        display: flex;
                        align-items: center;
                    }
                    &.holder {
                        color: #999;
                    }
                    &:disabled,
                    &.disabled {
                        color: #999;
                        opacity: 1;
                        -webkit-text-fill-color: #999;
                    }
                }
                input {
                    background: none;
                    border: none;
                    outline: none;
                }
            }
        }
        .btn {
            height: 84px;
            background: #fa0430;
            border-radius: 5px;
            margin: 30px 30px 0;
            text-align: center;
            line-height: 84px;
            .fontSizeWithElder(32px);
            color: #fff;
            &.settle-down {
                background: rgba(250, 4, 48, 0.3);
            }
        }
    }
    .agreement {
        margin-top: 20px;
        padding: 10px 10px;
        .fontSizeWithElder(24px);
        color: #a0a0a0;
        display: flex;
        align-items: center;
        justify-content: center;
        .regular-checkbox {
            display: none;
        }
        .regular-checkbox + .regular-label {
            width: 30px;
            height: 30px;
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
            display: block;
            margin-right: 6px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
        span {
            color: #42bcff;
        }
    }
}
.success-popup {
    /deep/ .dialog {
        width: 580px;
        border-radius: 20px;
        background: #fff;
    }
    .wrap {
        padding: 220px 40px 60px;
        background: url(../assets/images/success-icon.png) no-repeat center 50px / 148px 148px;
    }
    .title {
        font-size: 34px;
        color: #333333;
        font-weight: bold;
        text-align: center;
    }
    .desc {
        margin-top: 12px;
        font-size: 26px;
        color: #666666;
    }
    .btn {
        margin: 30px auto 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        font-size: 32px;
        background: #04a5ff;
        border-radius: 46px;
        width: 260px;
        height: 88px;
    }
}
</style>
