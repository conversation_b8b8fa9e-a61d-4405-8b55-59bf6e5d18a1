<template>
    <div>
        <popup
            class="activity-win-popup"
            :position="'center'"
            @closed="closed"
            :show.sync="visible"
            :showClose="false"
        >
            <div class="dialog-close" @click="closeSelf"></div>
            <div class="dialog-center">
                <div class="red-content" v-if="showRed" :class="{ani: showRedAni}" @animationend="aniEnd2">
                    <div class="red1">
                        <div class="text">
                            <img :src="roomDetail.teacherHeadImg" />{{ roomDetail.teacherName }}老师
                        </div>
                        <div class="sue">{{ redPacketWord }}</div>
                    </div>
                    <div class="red2"></div>
                    <div class="red3" v-if="!showRedAni" @click="openRed"></div>
                </div>
            </div>
            <div class="dialog-center">
                <div class="content"  v-if="showShipList && shipList.length" :class="{'prize-list': shipList.length > 1}" @animationend="aniEnd">
                    <div class="scroll_view">
                        <div class="item" v-for="prize in shipList" :key="prize.presentShipId">
                            <div class="p2">《{{ prize.name }}》</div>
                            <div class="image">
                                <img v-if="prize.imageUrl" :src="prize.imageUrl" />
                            </div>
                        </div>
                    </div>
                    <div class="bh" v-if="showBotAni">
                        <transition appear name="fade" mode="out-in">
                            <div v-if="showContentAni">
                                <template v-if="shipList.length === 1 && shipList[0].expiredTime > 0">
                                    <div class="p3">
                                        {{ formatDate(shipList[0].expiredTime) }}前领取<br />
                                        点击直播间下方...按钮可查看我的奖品
                                    </div>
                                </template>
                                <div class="btn" v-if="shipList.length > 1" @click="openAwardList">一键领取</div>
                                <div class="btn" v-else @click="goUse(shipList[0])">{{ prize.category === 5 ? '立即领取' : '去使用' }}</div>
                            </div>
                        </transition>
                    </div>
                </div>
            </div>
        </popup>
        <iframePopup :show.sync="goodAddressPopupVisible" :src="addressPack" />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {formatDate, getUrl, toast, webOpen} from '../utils/tools'
import {getPromotionActivityInfo} from '../server/active'
import popup from '../components/dialog'
import iframePopup from '../components/iframePopup'

export default {
    components: {
        popup,
        iframePopup,
    },
    data() {
        return {
            visible: false,
            addressPack: '',
            goodAddressPopupVisible: false,
            showBotAni: false,
            showContentAni: false,
            showRedAni: false,
            showRed: true,
            showShipList: false,
            redPacketWord: '',
        }
    },
    props: {
        show: Boolean,
        shipList: Array,
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                this.reset()
                this.getActivityInfo()
            }
        },
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig']),
        prize() {
            if (this.shipList.length) {
                return this.shipList[0]
            } else {
                return {}
            }
        },
    },
    model: {
        prop: 'show',
        event: 'setShow',
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            this.$emit('update:show', false)
        },
        reset() {
            this.showBotAni = false
            this.showContentAni = false
            this.showRedAni = false
            this.showRed = true
            this.showShipList = false
        },
        formatDate(date) {
            if (date) {
                return formatDate(date, 'yyyy年MM月dd日hh点')
            } else {
                return ''
            }
        },
        async goUse(item) {
            let url = item.actionUrl
            if (item.category === 5) {
                this.addressPack = getUrl('https://laofuzi.kakamobi.com/coupon/good-address.html', {
                    presentShipId: item.presentShipId,
                })
                this.goodAddressPopupVisible = true
            } else {
                webOpen({
                    url,
                    titleBar: true,
                })
            }
            this.close()
        },
        async getActivityInfo() {
            let info = await getPromotionActivityInfo({
                activityId: this.roomDetail.promotionActivityId,
            })
            this.redPacketWord = info.redPacketWord
        },
        openAwardList() {
            this.$EventBus.$emit('openAwardList')
            this.close()
        },
        aniEnd(e) {
            if (e.animationName === 'enlarge') {
                this.showBotAni = true
            } else if (e.animationName === 'rise') {
                this.showContentAni = true
            }
        },
        aniEnd2(e) {
            if (e.animationName === 'rise2' || e.animationName === 'fall2') {
                this.showRed = false
            }
        },
        closeSelf() {
            if (this.showRed) {
                toast('奖品已发放至您的账户，可进入我的奖品页查看详情')
            }
            this.close()
        },
        openRed() {
            this.showRedAni = true
            this.showShipList = true
        },
    },
}
</script>

<style>
@keyframes enlarge {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    30% {
        transform: scale(0);
        opacity: 0;
    }
    40% {
        transform: scale(0.5);
        opacity: 0.4;
    }

    75% {
        transform: scale(1.2);
        opacity: 1;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}
@keyframes rise {
    0% {
        transform: translateY(100%);
    }

    100% {
        transform: translateY(0);
    }
}
@keyframes rise2 {
    0% {
        transform: translateY(0);
        opacity: 1;
    }

    100% {
        transform: translateY(-180px);
        opacity: 0.4;
    }
}
@keyframes fall2 {
    0% {
        transform: translateY(0);
        opacity: 1;
    }

    100% {
        transform: translateY(180px);
        opacity: 0.4;
    }
}
</style>
<style lang="less" scoped>
@import '../assets/styles/variables';
@keyframes mymove {
    0% {
        transform: scale(0.8);
    }
    40% {
        transform: scale(1);
    }
    60% {
        transform: scale(0.98);
    }
    100% {
        transform: scale(0.8);
    }
}

.fade-enter-active {
    transition: all 0.3s ease;
}
.fade-leave-active {
    transition: all 0.3s ease;
}
.fade-enter {
    opacity: 0;
}
.fade-leave-to {
    opacity: 0;
}
.activity-win-popup {
    /deep/ .dialog {
        width: 540px;
        height: 634px;
    }
    .dialog-close {
        position: absolute;
        z-index: 2;
        right: -50px;
        top: -40px;
        width: 56px;
        height: 56px;
        background: url(../assets/images/close3.png) no-repeat;
        background-size: 56px 56px;
    }
    .dialog-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .bh {
        background: url(../assets/images/win-prize-bg2.png) no-repeat center center / 100%;
        width: 516px;
        height: 276px;
        position: absolute;
        bottom: 12px;
        left: 12px;
        padding-top: 50px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // transform: translateY(100%);
        animation: rise 0.15s ease-out;
    }
    .prize-list.content {
        padding-left: 40px;
        padding-right: 40px;
        .scroll_view {
            overflow-x: auto;
            display: flex;
        }
        .p2 {
            font-size: 26px;
        }
        .image {
            width: auto;
        }
        .item {
            width: 300px;
            flex-shrink: 0;
            margin-right: 16px;
        }
    }
    .content {
        background: url(../assets/images/win-prize-bg.png) no-repeat center center / 100%;
        width: 540px;
        height: 634px;
        padding: 60px 0 50px;
        border-radius: 40px;
        animation: enlarge 0.35s ease-out;
        text-align: center;
        overflow: hidden;
        position: relative;
        .scroll_view {
            padding-top: 50px;
        }
        .p2 {
            font-size: 30px;
            color: #834f06;
            font-weight: 500;

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .p3 {
            font-size: 24px;
            color: rgba(255,255,255,0.7);
            line-height: 1.5;
        }
        .image {
            width: 400px;
            height: 240px;
            padding: 16px 16px 0;
            border-radius: 24px;
            background: #fff;
            border: 1px solid rgba(240,222,186,0.6);
            margin: 10px auto 0;
            overflow: hidden;
            img {
                width: 100%;
                height: auto;
            }
        }
        .item {
            width: 440px;
            margin: 0 auto;
        }
    }
    .btn {
        margin: 8px auto 0;
        width: 428px;
        height: 88px;
        font-size: 38px;
        background: url(../assets/images/win-prize-btn.png) no-repeat center center / 100%;
        font-weight: bold;
        color: #fc381d;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .red-content {
        width: 512px;
        height: 612px;

        .text {
            margin-top: 50px;
            font-size: 32px;
            color: #f6ca92;
            height: 110px;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
                margin-right: 12px;
                width: 56px;
                border-radius: 100%;
                border: 1px solid #fcc694;
            }
        }
        .sue {
            text-align: center;
            color: #FAE7B8;
            font-size: 60px;
            text-shadow: 0px 4px 6px 0px rgba(155,2,2,0.38);
            margin: 0 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .red1 {
            position: absolute;
            width: 512px;
            height: 442px;
            background: url(../assets/images/red1.png) no-repeat center center / 100%;
            left: 0;
            top: 0;
            transform: translateZ(0);
        }
        .red2 {
            position: absolute;
            width: 512px;
            height: 280px;
            background: url(../assets/images/red2.png) no-repeat center center / 100%;
            left: 0;
            bottom: 0;
            transform: translateZ(0);
        }
        .red3 {
            position: absolute;
            width: 200px;
            height: 180px;
            background: url(../assets/images/red3.png) no-repeat center center / 100%;
            left: 156px;
            top: 340px;
            transform: translateZ(0);
            animation: mymove 0.8s infinite;
            animation-timing-function: ease-out;
        }
        &.ani {
            .red1 {
                animation: rise2 0.25s ease-out;
            }
            .red2 {
                animation: fall2 0.25s ease-out;
            }
        }
    }
}
</style>
