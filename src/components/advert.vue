<template>
    <div>
        <div class="motion" v-if="isShow" @click="clickShowcaseGoods">
            <div class="nopay" v-if="!(advertDetail.bought && !advertDetail.upgrade) && nopayExpiredTime > 0 && timer">
                <div class="text"></div>
                <div class="text2" v-if="advertDetail.promoteOrderingTxt">- {{ advertDetail.promoteOrderingTxt }} -</div>
                <p class="time" v-html="remainderTextHtml"></p>
                <div class="text3">后自动取消订单</div>
                <div class="btn">立即支付</div>
            </div>
            <motion
                v-else
                :advertDetail="advertDetail"
                :remainSotckInfo="advertRemainSotckInfo"
            />
        </div>
        <payPopup
            :key="advertDetail.icon + 'payPopup'"
            :advertDetail="advertDetail"
            :motionDetailProp="motionDetail"
            :remainSotckInfo="advertRemainSotckInfo"
            :show.sync="payPopupVisible"
            :fragmentName1="fragmentName1"
            :fullScreen="fullScreen"
        />
    </div>
</template>

<script>
import {mapState, mapGetters} from 'vuex'
import {
    isAndroid,
    isTablet,
    formatRemainTime,
    trackEvent,
} from '../utils/tools'
import {getNopayExpiredTime} from '../utils/payHelper'
import goodsMixin from '../utils/goodsMixin'
import payPopup from './payPopup'
import motion from './motion'
let fragmentNameNormal = '右方商品图标'
let fragmentNameNoplay = '大挂件未支付卡片'

export default {
    mixins: [goodsMixin],
    components: {motion, payPopup},
    data() {
        return {
            fullScreen: false,
            fragmentName1: fragmentNameNormal,
            payPopupVisible: false,
            advertRemainSotckInfo: {},
            motionDetail: {},
            timer: null,
            remainderText: '',
            nopayExpiredTime: -1,
        }
    },
    props: {
        advertDetail: Object,
    },
    watch: {
        isShow(val) {
            this.$store.commit('updateBizConfig', {
                motionVisible: val,
            })
            this.$EventBus.$emit('motionSizeChange')
        },
        remainSotckInfo: {
            handler() {
                this.generateRemainSotckInfo()
            },
            deep: true,
            immediate: true,
        },
        payPopupVisible(val) {
            if (!val && !this.nopayExpiredTime) {
                this.getTime()
            }
        },
    },
    computed: {
        ...mapState(['remainSotckInfo', 'bizConfig']),
        ...mapGetters(['checkAgreement', 'readed']),
        isShow() {
            let k = this.advertDetail.img
            return k && this.nopayExpiredTime !== -1
        },
        remainderTextHtml() {
            let t = this.remainderText.replace(/\d/g, '<span>$&</span>').replace(/:/g, '<i>$&</i>')
            return t
        },
    },
    created() {
        this.$EventBus.$on('updateNopayExpiredTime', () => {
            if (!this.payPopupVisible && !this.nopayExpiredTime) {
                this.getTime()
            }
        })
        this.getTime()
    },
    async mounted() {
        this.$EventBus.$on('openMotion', (config) => {
            let {report, fragmentName1} = config
            if (fragmentName1) {
                this.fragmentName1 = fragmentName1
            }
            this.popupMotion(this.advertDetail, this.motionDetail, report)
        })

        // 安卓pad旋转屏幕时，容器宽度变化会引起slide重叠现象
        // 重新渲染一下swiper 可以解决问题
        // https://jira.mucang.cn/browse/JKBD-17066?filter=11606
        if (isAndroid && isTablet) {
            this.$EventBus.$on(['orientationChange', 'resize'], () => {
                setTimeout(() => {
                    // TODO  Pad
                    // this.swiperOptions.width = 262
                    // this.swiperKey = +new Date()
                }, 100)
            })
        }

        this.motionDetail = await this.getAdvertDetail(this.advertDetail)
    },
    methods: {
        async getTime() {
            if (this.advertDetail.advertType === 'training') {
                this.nopayExpiredTime = 0
                return
            }
            const nopayExpiredTime = await getNopayExpiredTime(this.advertDetail.goodsKey)
            if (nopayExpiredTime && nopayExpiredTime > +new Date()) {
                this.nopayExpiredTime = nopayExpiredTime
                this.startTimer(nopayExpiredTime)
            } else if (this.nopayExpiredTime === -1) {
                this.nopayExpiredTime = 0
            }
        },
        startTimer(endTime) {
            clearInterval(this.timer)
            this.timer = null

            let date = +new Date()
            let remainder = endTime - date
            if (remainder < 0) {
                return
            }
            this.$store.commit('updateBizConfig', {
                nopayMotionVisible: true,
            })
            this.timer = setInterval(() => {
                let date = +new Date()
                let remainder = endTime - date
                this.remainderText = formatRemainTime(remainder, 'mm:ss')
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    this.timer = null
                    this.$store.commit('updateBizConfig', {
                        nopayMotionVisible: false,
                    })
                }
            }, 16)

            let fragmentName1 = fragmentNameNoplay
            let actionType = '出现'

            // 精品课直播间页_大挂件未支付卡片_出现
            trackEvent({fragmentName1, actionType})
        },
        generateRemainSotckInfo() {
            if (this.remainSotckInfo.dataList && this.remainSotckInfo.dataList.length) {
                let group = 1
                let {sellRateEnable, sellRateNum} = this.remainSotckInfo.dataList[group - 1]
                let applyScene = this.remainSotckInfo.applyScene
                this.advertRemainSotckInfo = {
                    sellRateEnable,
                    sellRateNum,
                    applyScene,
                }
            }
        },
        async clickShowcaseGoods() {
            if (this.bizConfig.nopayMotionVisible) {
                this.fragmentName1 = fragmentNameNoplay
            } else {
                this.fragmentName1 = fragmentNameNormal
            }
            this.popupMotion(this.advertDetail, this.motionDetail)
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.motion {
    width: 212px;
}

.nopay {
    background: url(../assets/images/nopay-bg.png) no-repeat center top / 100% auto;
    width: 196px;
    height: 350px;
    padding-top: 1px;
    position: relative;
    .text {
        margin: 92px auto 0;
        background: url(../assets/images/nopay-text.png) no-repeat center / 100% auto;
        width: 144px;
        height: 78px;
    }
    .text2 {
        margin-top: 6px;
        color: #fff;
        font-size: 16px;
        text-align: center;
    }
    .text3 {
        margin-top: 10px;
        color: #fff;
        font-size: 18px;
        text-align: center;
    }
    .time {
        margin-top: 8px;
        display: flex;
        justify-content: center;
        color: #fff;
        /deep/ span {
            background: url(../assets/images/nopay-bg2.png) no-repeat center / 100% auto;
            width: 36px;
            height: 48px;
            font-size: 34px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 2px;
        }
        /deep/ i {
            font-size: 28px;
            margin: 0 2px;
        }
    }
    .btn {
        position: absolute;
        left: 0;
        bottom: 0;
        background: url(../assets/images/nopay-btn.png) no-repeat center / 100% auto;
        width: 196px;
        height: 48px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffe5d3;
        font-size: 26px;
    }
}
</style>
