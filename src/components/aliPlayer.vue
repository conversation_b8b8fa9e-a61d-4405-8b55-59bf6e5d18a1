<template>
    <div
        class="player-home"
        @click.capture="playToggle"
        :class="[
            bizConfig.playStatus === 1 ? 'live' : 'vod',
            {
                'with-advert-img': bizConfig.motionVisible,
                'notshow-fullscreen': !isShowFullScreen,
                'with-quick-comment': bizConfig.quickCommentVisible,
                'warmup': bizConfig.playStatus === 3,
                'contain-mode': videoContainMode
            },
        ]"
    >
        <div id="J_prismPlayer" class="prism-player" :key="videoKey"></div>
        <div class="por-no" v-if="showPor">备案号：{{ roomDetail.porNo }}</div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {addClass, removeClass, loadScript, loadStyle, getMapfromArray} from '../utils/tools'
import videoMixin from '../utils/videoMixin'

let fullscreenBtn = null
let danmuBtn = null
let player
let commonOptions = {
    id: 'J_prismPlayer',
    extraInfo: {
        poster:
            'https://jiakao-web.mc-cdn.cn/jiakao-web/2021/12/13/17/e044a4219e2a4cf09b56cd6a82631466.png',
    },
    width: '100%',
    height: '100%',
    autoplay: true,
    // showBarTime: 999999999,
    controlBarVisibility: 'click',
    language: 'zh-cn',
}
let liveSkinLayout = [
    {name: 'bigPlayButton', align: 'blabs'},
    // {name: 'errorDisplay', align: 'tlabs', x: 0, y: 0},
    {name: 'H5Loading', align: 'cc'},
    {
        name: 'controlBar',
        align: 'blabs',
        x: 0,
        y: 0,
        children: [
            {name: 'liveDisplay', align: 'tlabs', x: 15, y: 6},
            {name: 'setting', align: 'tr', x: 15, y: 12},
        ],
    },
]
let vodSkinLayout = [
    {name: 'bigPlayButton', align: 'blabs'},
    // {name: 'errorDisplay', align: 'tlabs', x: 0, y: 0},
    {name: 'H5Loading', align: 'cc'},
    {name: 'thumbnail'},
    {
        name: 'controlBar',
        align: 'blabs',
        x: 0,
        y: 0,
        children: [
            {name: 'progress', align: 'blabs', x: 0, y: 44},
            {name: 'playButton', align: 'tl', x: 15, y: 12},
            {name: 'timeDisplay', align: 'tl', x: 10, y: 7},
            {name: 'setting', align: 'tr', x: 15, y: 12},
        ],
    },
]

let sdkLoading = false
let sdkReady = false
let sdkCallbacks = []
let eventRegister = false
let retry = 1
const getSdk = () => {
    if (sdkReady) return Promise.resolve()
    if (sdkLoading) {
        return new Promise(resolve => {
            sdkCallbacks.push(() => {
                resolve()
            })
        })
    }
    sdkLoading = true
    loadScript('https://g.alicdn.com/de/prismplayer/2.12.1/aliplayer-min.js').then(() => {
        sdkLoading = false
        sdkReady = true
        sdkCallbacks.forEach(cb => {
            cb && cb()
        })
    })
    return new Promise(resolve => {
        sdkCallbacks.push(() => {
            resolve()
        })
    })
}

function insertBefore(newNode, referenceNode) {
    try {
        referenceNode.parentNode.insertBefore(newNode, referenceNode)
    } catch (error) {}
}

export default {
    mixins: [videoMixin],
    props: {
        isShowFullScreen: Boolean,
    },
    data() {
        return {
            videoKey: 0,
        }
    },
    watch: {
        isLandscape() {
            this.updateLandscapeIcon()
        },
        isShowDanmu() {
            this.updateDanmuIcon()
        },
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'isLandscape', 'isShowDanmu']),
        liveSource() {
            let sourceMap = this.pullStreamUrls.map(item => {
                return {key: item.definition, value: item.url}
            })
            return JSON.stringify(getMapfromArray(sourceMap))
        },
        liveBackSource() {
            let backSourceMap = this.pullStreamUrls.map(item => {
                return {key: item.definition, value: item.backupUrl}
            })
            return JSON.stringify(getMapfromArray(backSourceMap))
        },
    },
    created() {
        /* global Aliplayer */
        loadStyle('https://g.alicdn.com/de/prismplayer/2.12.1/skins/default/aliplayer-min.css')
        if (this.roomDetail.orientation === 1) {
            commonOptions.extraInfo.poster =
                'https://jiakao-web.mc-cdn.cn/jiakao-web/2023/03/21/16/6a639943cd2b454396a247d9b09d42fb.png'
        }
        if (this.roomDetail.cover) {
            commonOptions.extraInfo.poster = this.roomDetail.cover
        }
    },
    mounted() {
        pageSwitch.onPageShow(() => {
            player.play()
            console.log('play')
        })
        pageSwitch.onPageHide(() => {
            player.pause()
            console.log('pause')
        })
    },
    methods: {
        playToggle(e) {
            let tagName = e.target.tagName.toLowerCase()
            if (
                tagName === 'video' &&
                this.roomDetail.orientation === 1 &&
                this.bizConfig.playStatus === 2
            ) {
                if (player.getStatus() === 'playing') {
                    player.pause()
                }
            }
        },
        updateLandscapeIcon() {
            if (!fullscreenBtn) return
            if (this.isLandscape) {
                addClass(fullscreenBtn, 'fullscreen')
            } else {
                removeClass(fullscreenBtn, 'fullscreen')
            }
        },
        updateDanmuIcon() {
            if (!danmuBtn) return
            if (this.isShowDanmu) {
                addClass(danmuBtn, 'open')
            } else {
                removeClass(danmuBtn, 'open')
            }
        },
        playerReady() {
            setTimeout(() => {
                addClass(this.$el, 'ready')
            }, 200)
            let hand = this.$el.querySelector('.prism-setting-btn')
            const parser = new DOMParser()
            const htmlString =
                '<div style="width: 24px; height: 24px; float: right; margin-right: 15px; margin-top: 12px;"></div>'
            const domElement = parser.parseFromString(htmlString, 'text/html').body.firstChild
            fullscreenBtn = domElement.cloneNode()
            danmuBtn = domElement.cloneNode()
            fullscreenBtn.className = 'custom-fullscreen-btn'
            danmuBtn.className = 'custom-danmu-btn'
            insertBefore(danmuBtn, hand)
            insertBefore(fullscreenBtn, hand)
            this.updateLandscapeIcon()
            this.updateDanmuIcon()
            fullscreenBtn.addEventListener('click', () => {
                this.$EventBus.$emit('toggleRrientation')
            })
            danmuBtn.addEventListener('click', () => {
                this.$EventBus.$emit('toggleDanmu')
            })
        },
        dispose() {
            return new Promise(resolve => {
                if (player) {
                    player.dispose()
                    this.empty()
                }
                setTimeout(() => {
                    resolve()
                }, 0)
            })
        },
        empty() {
            // let contentEl = document.getElementById('J_prismPlayer')
            // contentEl.innerHtml = ''
            // while (contentEl.firstChild) {
            //     contentEl.removeChild(contentEl.firstChild)
            // }
            // this.$el.removeChild(contentEl)
            // let newCont = document.createElement('div')
            // newCont.id = 'J_prismPlayer'
            // this.$el.appendChild(newCont)
            this.videoKey = +new Date()
        },
        startLive(options = {}) {
            Promise.all([getSdk(), this.dispose()]).then(() => {
                let opt = {
                    ...commonOptions,
                    liveRetry: 1,
                    isLive: true,
                    useHls2: true,
                    skinLayout: liveSkinLayout,
                    source: this.liveSource,
                    rtsFallbackSource: this.liveBackSource,
                    ...options,
                }
                console.log('startLive', opt)
                this.$EventBus.$emit('startPlay', true)
                player = new Aliplayer(opt)
                player.on('ready', e => {
                    this.playerReady()
                    try {
                        let display = this.$el.querySelector('.prism-live-display')
                        display.innerText = '直播'
                        display.style.top = 'unset'
                        display.style.bottom = '0px'
                    } catch (error) {}
                })
                this.aliPlayerEvents()
            })
        },
        startVod(options) {
            Promise.all([getSdk(), this.dispose()]).then(() => {
                let opt = {
                    ...commonOptions,
                    skinLayout: vodSkinLayout,
                    ...options,
                }
                console.log('startVod', opt)
                this.$EventBus.$emit('startPlay', false)
                player = new Aliplayer(opt)
                player.on('ready', e => {
                    this.playerReady()
                    try {
                        const progress = this.$el.querySelector('.prism-progress')
                        progress.style.width = 'auto'
                        progress.style.left = '24px'
                        progress.style.right = '24px'
                    } catch (error) {}
                })
                this.aliPlayerEvents()
            })
        },
        startPlayback() {
            this.startVod({
                source: this.playbackSources[this.playbackIndex],
            })
        },
        startWarmup() {
            this.startVod({
                source: this.roomDetail.warmUpVideo,
                rePlay: true,
                skinLayout: false,
            })
        },
        aliPlayerEvents() {
            if (eventRegister) return
            eventRegister = true
            player.on('ready', e => {
                console.log('EVENT ready', e.paramData)
            })
            player.on('play', e => {
                console.log('EVENT play', e.paramData)
            })
            player.on('pause', e => {
                console.log('EVENT pause', e.paramData)
            })
            player.on('canplay', e => {
                console.log('EVENT canplay', e.paramData)
            })
            player.on('playing', e => {
                console.log('EVENT playing', e.paramData)
            })
            player.on('ended', e => {
                console.log('EVENT ended', e.paramData)
                if (this.playbackIndex >= 0) {
                    if (this.playbackSources.length - 1 > this.playbackIndex) {
                        this.playbackIndex += 1
                        player.loadByUrl(this.playbackSources[this.playbackIndex])
                    } else {
                        this.playbackIndex = 0
                        player.pause()
                        player.loadByUrl(this.playbackSources[this.playbackIndex])
                        player.pause()
                    }
                }
            })
            player.on('liveStreamStop', e => {
                console.log('EVENT liveStreamStop', e.paramData)
            })
            player.on('onM3u8Retry', e => {
                console.log('EVENT onM3u8Retry', e.paramData)
            })
            player.on('waiting', e => {
                console.log('EVENT waiting', e.paramData)
            })
            player.on('error', e => {
                if (retry) {
                    try {
                        let sources = this.pullStreamUrls[0]
                        let url = sources.backupUrl
                        console.log('retry', url)
                        retry--
                        player.loadByUrl(url, 0, true)
                    } catch (error) {}
                }
                console.log('EVENT error', e.paramData)
            })
            player.on('rtsFallback', e => {
                console.log('EVENT rtsFallback', e.paramData)
            })
        },
    },
}
</script>
<style lang="less">
.landscape .player-home.with-advert-img {
    padding-bottom: 10%;
    .por-no {
        margin-bottom: 10%;
    }
}
.player-home {
    height: 100%;
    position: relative;
    .por-no {
        font-size: 20px;
        position: absolute;
        left: 20px;
        bottom: 10px;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0px 0 1px rgba(0, 0, 0, 0.3);
        pointer-events: none;
        z-index: 1;
    }
}
body {
    .prism-player .prism-controlbar .prism-controlbar-bg {
        background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.666) 0,
            rgba(0, 0, 0, 0.028) 86.1%,
            rgba(0, 0, 0, 0) 100%
        );
    }
    .prism-controlbar {
        display: none;
    }
    .ready .prism-controlbar {
        display: block;
    }
    /* 音轨 */
    .prism-setting-audio {
        display: none;
    }
    /* 字幕 */
    .prism-setting-cc {
        display: none;
    }
    .vod .prism-setting-quality {
        display: none;
    }
    .prism-player .prism-liveshift-progress,
    .prism-player .prism-progress {
        background-color: rgba(255, 255, 255, 0.3);
    }
    .prism-player .prism-big-play-btn {
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%);
        z-index: 1;
    }
    .prism-player .prism-time-display {
        color: #eeeeee;
    }
    .prism-player .prism-controlbar {
        box-sizing: content-box;
        z-index: 1;
    }
    .notshow-fullscreen .custom-fullscreen-btn {
        display: none;
    }
    &.vertical {
        .prism-player {
            background: #1b1d22;
        }
        .contain-mode {
            .prism-player video {
                object-fit: contain !important;
            }
        }
        .player-home:not(.warmup) {
            .prism-player video {
                object-fit: cover;
            }
        }
        .player-home .por-no {
            left: 30px;
            bottom: 24px;
        }
        .live .prism-controlbar {
            pointer-events: none;
            visibility: hidden;
        }
        .prism-player .prism-controlbar .prism-controlbar-bg {
            display: none;
        }
        .prism-big-play-btn,
        .loading-center {
            transform: translate(-45%, -50%);
        }
        .vod {
            .prism-big-play-btn.pause {
                display: block !important;
            }
            .prism-play-btn,
            .prism-setting-btn {
                display: none;
            }
            .prism-progress-loaded,
            .prism-progress-played,
            .prism-progress-marker {
                height: 12px !important;
            }
            .prism-controlbar {
                height: 46px;
                margin-bottom: 136px;
                display: flex !important;
                align-items: center;
                padding-bottom: calc(constant(safe-area-inset-bottom) - 50px);
                padding-bottom: calc(env(safe-area-inset-bottom) - 50px);
            }
            .prism-progress {
                height: 12px !important;
                position: relative !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                margin-left: 20px;
                flex: 1;
            }
            .prism-time-display {
                height: auto;
                line-height: initial;
                margin-right: 20px !important;
                margin-top: 20px !important;
                margin-top: 0 !important;
            }
            &.with-quick-comment .prism-controlbar {
                margin-bottom: 226px;
            }
        }
    }
    .prism-player .custom-danmu-btn {
        display: none;
        background: url(../assets/images/<EMAIL>) no-repeat center;
        background-size: cover;
        &.open {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    .prism-player .custom-fullscreen-btn {
        background: url(../assets/images/<EMAIL>) no-repeat center;
        background-size: cover;
        &.fullscreen {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    .minimize {
        .player-home {
            pointer-events: none;
        }
        .prism-player .prism-controlbar {
            display: none;
        }
    }
}
</style>
