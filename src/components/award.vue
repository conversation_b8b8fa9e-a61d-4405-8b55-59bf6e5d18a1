<template>
    <div>
        <div class="motion" @click="openAwardPopup">
            <img src="../assets/images/<EMAIL>" />
        </div>
        <awardPopup
            :liveId="liveId"
            :lotteryInfo="lotteryInfo"
            :show.sync="awardVisible"
        ></awardPopup>
    </div>
</template>

<script>
import {getAuthToken, goLogin} from '../utils/tools'
import awardPopup from './awardPopup'

export default {
    components: {awardPopup},
    data() {
        return {
            awardVisible: false,
        }
    },
    props: {
        liveId: [Number, String],
        lotteryInfo: Object,
    },
    methods: {
        openAwardPopup() {
            this.$EventBus.$emit('setOrientation', 'portrait', async () => {
                const authToken = await getAuthToken()
                if (authToken) {
                    this.awardVisible = true
                } else {
                    goLogin({refresh: true})
                }
            })
        },
    },
}
</script>

<style lang="less" scoped>
.motion {
    width: 180px;
    min-height: 1px;
    margin-top: 10px;
    img {
        width: 100%;
    }
}
.landscape /deep/ {
    // landscape
    .motion {
        width: 146px;
    }
}
</style>
