<template>
    <popup class="award-popup" @closed="closed" :position="'bottom'" :show.sync="visible">
        <div class="wrap">
            <div class="header">
                <div class="tit">我的抽奖券</div>
                <div v-if="codes.length" class="icon-content" @click="copy">复制券号</div>
            </div>
            <div class="content">
                <div v-if="codes.length">
                    <ul class="code-ul">
                        <li class="code-li" v-for="(item, index) in codes" :key="item.code">
                            <p class="p2">
                                <b>抽奖券{{ index + 1 }} (第{{ item.num }}期):</b>
                                <i>{{ item.code }}</i>
                                <label
                                    v-if="item.presentName"
                                    :class="getAwardLvCls(item.presentName)"
                                ></label>
                            </p>
                        </li>
                    </ul>
                </div>
                <div v-else class="tickets-empry"></div>
            </div>
        </div>
    </popup>
</template>

<script>
import {toast, copyText} from '../utils/tools'
import {getLotteryCodes} from '../server/xueche66'
import popup from '../components/dialog'

export default {
    components: {
        popup,
    },
    data() {
        return {
            visible: false,
            codes: [],
        }
    },
    props: {
        show: Boolean,
        liveId: [Number, String],
        lotteryInfo: Object,
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                this.getLotteryCodes()
            }
        },
    },
    methods: {
        closed() {
            this.close()
        },
        getAwardLvCls(presentName) {
            switch (presentName) {
                case '一等奖':
                    return 'label1'
                case '二等奖':
                    return 'label2'
                case '三等奖':
                    return 'label3'
                case '四等奖':
                    return 'label4'
                default:
            }
        },
        async getLotteryCodes() {
            if (this.liveId) {
                let fullNum = function(arr, num) {
                    if (!arr || !arr.length) {
                        return arr
                    } else {
                        return arr.map(item => {
                            item.num = num
                            return item
                        })
                    }
                }
                const retData = await getLotteryCodes({
                    liveIds:
                        this.liveId +
                        (this.lotteryInfo.nextLiveId ? ',' + this.lotteryInfo.nextLiveId : ''),
                })

                let codes = []
                retData.codeInfo.forEach(item => {
                    let num
                    if (item.liveId === this.liveId) {
                        num = this.lotteryInfo.currentNum
                    } else if (item.liveId === this.lotteryInfo.nextLiveId) {
                        num = this.lotteryInfo.nextNum
                    }
                    if (num) {
                        let codeList = (fullNum(item.code1, num) || []).concat(
                            fullNum(item.sharedCodes, num) || []
                        )
                        codes.push(...codeList)
                    }
                })
                this.codes = codes
            }
        },
        close() {
            this.$emit('update:show', false)
        },
        copy() {
            let copyNumber = []
            this.codes.forEach(item => {
                copyNumber.push(item.code)
            })
            let ret = copyText(copyNumber.toString())
            if (ret) {
                toast('已复制券号到剪贴板，弹幕刷起来！')
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.award-popup {
    .wrap {
        display: flex;
        border-radius: 20px 20px 0 0;
        flex-direction: column;
        justify-content: space-between;
        background: #fff;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 40px);
    }
    .header {
        padding: 24px 80px 24px 24px;
        border-bottom: 1px solid #f2f2f2;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .tit {
            .fontSizeWithElder(32px);
            font-weight: bold;
            color: #333333;
        }

        .icon-content {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            border-radius: 40px;
            border: 1px solid #98d8fc;
            background: #e8f6fe;
            .fontSizeWithElder(24px);
            color: #25aff9;
            line-height: 1.1;
        }
    }
    .content {
        .tickets-empry {
            width: 550px;
            height: 300px;
            margin: auto;
            background: url('../assets/images/<EMAIL>');
            background-size: cover;
        }

        .code-ul {
            position: relative;
            max-height: 450px;
            padding: 0 30px 30px 30px;
            overflow-y: scroll;
            list-style: none;
        }

        .code-li {
            padding: 24px 0;
            border-bottom: 1px solid #f2f2f2;

            .p2 {
                padding-top: 16px;
                display: flex;
                align-items: center;

                b {
                    color: #666666;
                    .fontSizeWithElder(32px);
                    line-height: 44px;
                }

                i {
                    flex: 1;
                    .fontSizeWithElder(36px);
                    text-align: right;
                    line-height: 50px;
                    color: #000000;
                }

                label {
                    color: #a85b15;
                    .fontSizeWithElder(24px);
                    border-radius: 32px;
                    padding: 8px 16px 6px 16px;
                    background: linear-gradient(90deg, #ffd958 0%, #ffc910 100%);
                    background: linear-gradient(90deg, #f2b591 0%, #e79551 100%);
                }

                label {
                    width: 104px;
                    height: 40px;
                    background-repeat: no-repeat;
                    background-size: cover;
                }

                .label1 {
                    background-image: url(../assets/images/<EMAIL>);
                }

                .label2 {
                    background-image: url(../assets/images/<EMAIL>);
                }

                .label3 {
                    background-image: url(../assets/images/<EMAIL>);
                }

                .label4 {
                    background-image: url(../assets/images/<EMAIL>);
                }
            }
        }
    }
}
</style>
