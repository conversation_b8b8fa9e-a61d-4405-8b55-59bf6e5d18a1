<template>
    <div>
        <template v-if="isShow">
            <div class="motion" @click="openBuyLottery">
                <img src="../assets/images/<EMAIL>" />
            </div>
        </template>
        <buyLotteryPopup
            ref="buyLotteryPopup"
            :lotteryInfo="lotteryInfo"
            :show.sync="buyLotteryVisible"
        ></buyLotteryPopup>
    </div>
</template>

<script>
import {URLParams, toast, isVIVO, getAuthToken, goLogin} from '../utils/tools'
import buyLotteryPopup from './buyLotteryPopup'
import {getNextLotteryData} from '../server/xueche66'

export default {
    components: {buyLotteryPopup},
    data() {
        return {
            ref: URLParams._ref || 'app',
            cityCode: URLParams._userCity || '420100',
            buyLotteryVisible: false,
            locked: false,
        }
    },
    props: {
        liveId: [String, Number],
        lotteryInfo: Object,
    },
    computed: {
        isShow() {
            return (
                this.lotteryInfo.nextLiveId &&
                (!this.lotteryInfo.payStatus || !this.lotteryInfo.retainStatus)
            )
        },
    },
    methods: {
        openBuyLottery() {
            this.$EventBus.$emit('setOrientation', 'portrait', async () => {
                if (isVIVO()) {
                    const authToken = await getAuthToken()
                    if (authToken) {
                        this.goPay()
                    } else {
                        goLogin({refresh: true})
                    }
                } else {
                    this.goPay()
                }
            })
        },
        async goPay() {
            if (this.locked) return
            this.locked = true
            try {
                const resData = await getNextLotteryData({
                    cityCode: this.cityCode,
                    liveId: this.liveId,
                })
                let lotteryInfo = resData
                // 已购买，未留资
                if (lotteryInfo.payStatus && !lotteryInfo.retainStatus) {
                    this.$refs.buyLotteryPopup.gotoFromPage(lotteryInfo.nextOrderNumber)
                } else if (lotteryInfo.payStatus && lotteryInfo.retainStatus) {
                    // 已购买 已留资
                    toast('您已购买！')
                } else {
                    this.buyLotteryVisible = true
                }
                this.$emit('setLotteryInfo', lotteryInfo)
                this.locked = false
            } catch (error) {
                this.locked = false
            }
        },
    },
}
</script>

<style lang="less" scoped>
.motion {
    width: 180px;
    min-height: 1px;
    margin-top: 10px;
    img {
        width: 100%;
    }
}
.landscape /deep/ {
    // landscape
    .motion {
        width: 160px;
        bottom: 230px;
    }
}
</style>
