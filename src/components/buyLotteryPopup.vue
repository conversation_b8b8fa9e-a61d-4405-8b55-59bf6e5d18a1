<template>
    <div>
        <popup class="buy-lottery-popup" @closed="closed" :position="'bottom'" :show.sync="visible">
            <div class="content">
                <div class="image"></div>
                <div class="inner">
                    <div class="pay_list">
                        <div
                            :class="item.checked ? 'checked' : ''"
                            @click="checkPayType(index)"
                            v-for="(item, index) in payList"
                            :key="item.name"
                        >
                            {{ item.name }}
                        </div>
                    </div>
                    <div class="buy-area" @click="buy()">
                        <div class="tag"></div>
                        <div class="btn">确认支付</div>
                    </div>
                    <div class="agreement">
                        <template v-if="checkAgreement">
                            <input
                                class="regular-checkbox"
                                id="agreement"
                                v-model="readed"
                                type="checkbox"
                            />
                            <label class="regular-label" for="agreement"></label>
                        </template>
                        <label for="agreement"
                            >支付前请先阅读
                            <span @click.prevent="openRules">《活动规则》</span>
                        </label>
                    </div>
                </div>
            </div>
        </popup>
        <xcjConfirmRules
            :show.sync="xcjConfirmRulesVisible"
            @confirm="confirm"
            @openRules="openRules"
        />
        <popup
            class="rules-popup"
            :position="'center'"
            :show.sync="rulesVisible"
            :bottomCloseIcon="true"
        >
            <div class="content">
                <div class="title">驾考宝典·六六学车节 活动规则</div>
                <br />

                <p class="p1">一、活动时间</p>
                <p class="p2">
                    1、活动参与时间：2025年6月3日19:00:00~2025年6月30日17:00:00<br />
                    2、直播开奖时间：活动期间内每周一下午17:30开奖，每周一17:00-18:00在活动首页浮窗提供入口供用户观看开奖直播。
                </p>

                <p class="p1">二、参与条件</p>
                <p class="p2">
                    1、
                    本活动仅面向未报名驾校用户，未报名驾校用户成功支付6.6元后，并正确填写您的真实资料，即可获取抽奖券一张。<br />
                    2、已报名驾校用户无法参与活动，如误支付6.6元，支付的6.6元将在当期开奖直播结束后2个工作日内沿支付方式原路退回。
                </p>

                <p class="p1">三、直播抽奖规则说明</p>

                <p class="p2">
                    为确保每次抽奖活动的真实有效，我们将加入新浪微博热搜榜第3条关键词的后5位数热搜指数的结果作为标准，保证开奖结果公正，杜绝黑箱操作。
                    <br />中奖号码计算公式如下：<br />中奖号码=【(A值 +
                    B值)÷本期派发的抽奖券总数】（取余数）+10000001
                    <br />A值：在本期内随机取50个获得抽奖券的时间和；
                    <br />B值：新浪微博热搜榜第3条关键词的后5位数热搜指数。 <br />示例如下：
                    <br />1、本次派发奖券7351张； <br />2、A值取值
                </p>
                <img src="../assets/images/45.png" alt="" />

                <p class="p2">
                    3、B值取值：新浪微博热搜榜第3条关键词的后5位数热搜指数08471
                </p>

                <p class="p2">
                    4、中奖号码 =【（5,578,215,800 + 08471）÷ 7351】（余数=6133）+ 10000001
                </p>
                <p class="p2">所以，中奖号码是：10006134</p>

                <p class="p2">
                    新浪微博热搜榜地址：https://s.weibo.com/top/summary
                </p>

                <p class="p1">四、学车节活动奖项说明</p>

                <p class="p2">1、免费学车：3000元学车现金补贴</p>
                <p class="p2">
                    1）每期学车节将抽取1名用户，送出【3000元学车现金补贴】大奖，学车补贴以现金打款的方式赠与用户；<br />
                    2）中奖后，用户支付抽奖券的6.6元不再退回，即为6块6享受3000元学车现金补贴；<br />
                    3）用户学车班型仅限C1/C2班型，其他班型不参与此次活动。<br />
                </p>

                <p class="p2">2、兑奖说明</p>

                <p class="p2">
                    中奖后，驾考宝典工作人员，会在1个工作日内与获奖用户联系。中奖用户需要在10天内完成【报名入学、上传兑奖审核资料、并通过线上资料审核流程】，若因个人原因，未及时上传导致逾期未通过线上资料审核流程，则视为放弃获奖资格；如中奖用户放弃，则将取消对应奖项，并退还抽奖券支付的6.6元；
                </p>

                <p class="p2">
                    *以下情况视为不符合全包学车大奖兑奖要求，将自动取消对应奖项：<br />
                    ①已经取得并持有中国机动车有效驾驶证的用户； <br />
                    ②本人年龄不在C1/C2驾驶证报名范围内的用户； <br />
                    ③在本期学车节直播开奖前已报名驾校的用户；<br />
                </p>

                <p class="p1">五、领奖要求特别说明</p>

                <p class="p2">
                    1、参与活动的用户成功支付6.6元后需正确填写个人资料并提交，未提交资料的用户将无法进入抽奖环节。
                </p>

                <p class="p2">
                    2、兑奖截止时间:
                    用户在中奖【3000元学车现金补贴】后，需在10个自然日内提交报名资料，例如10月10号中奖，则兑奖截止时间为10月20号。
                </p>

                <p class="p2">
                    3、兑奖资格基础审核：兑奖时，用户需提供本人身份证照片、手机号、驾校合同、收据、发票、银行卡账户等以供核验（合同、收据、发票，都需加盖驾校公章），合同信息必须与购买抽奖券时填写的姓名、手机号一致，否则无兑奖资格。
                </p>

                <p class="p2">
                    4、如用户拒绝或不及时提供兑奖必要的基本信息、或用户在抽奖前留下的信息和兑奖信息不一致，如姓名、身份证信息、驾校报名合同、收据、发票的照片与中奖人员不符，导致未在10天内，完成兑奖资料提交及审核流程的，视为用户主动放弃领奖资格。
                </p>

                <p class="p2">
                    5、未报名驾校的中奖用户，若您在直播开奖时为未报名驾校状态，中奖后需在10天内报名驾校，并提供驾校报名资料；若您报名驾校时间，在本期参与活动时间之后（本期参与活动时间指:
                    本期支付6.6元并留资的时间)
                    至本期活动直播开奖前，则需在中奖后10天内提供驾校报名资料。提供的资料含：本人身份证照片、手机号、驾校合同、收据、发票、银行卡账户等（合同、收据、发票，都需加盖驾校公章），超过10日未向工作人员提供驾校报名资料，以供审核并通过的，视为放弃获奖资格。
                </p>

                <p class="p2">
                    6、每场直播抽奖活动均需重新领取当期的抽奖券，往期抽奖券在当期直播抽奖结束后自动注销。
                </p>

                <p class="p2">
                    7、【3000元学车现金补贴】一旦抽中，仅限获奖者本人使用，不可转让。报名的姓名需与领取抽奖券时填写的姓名一致，若姓名不一致，视为自动取消对应奖项。
                </p>

                <div class="p2">
                    8、【3000元学车现金补贴】不可重复领取。
                </div>
                <div class="p2">
                    9、未抽中【3000元学车现金补贴】的用户，系统将在当天直播抽奖结束后，自动退还参与活动所支付的6.6元。
                </div>
                <div class="p2">
                    10、如未中奖，支付抽奖券的6.6元，将在当期开奖直播结束后2个工作日内沿支付方式原路退回。
                </div>
                <div class="p2">
                    11、用户中奖后，3000元学车现金补贴由武汉木仓科技股份有限公司支付；如学员未完成学车，在驾考系统中申请退款的，则学员应在收到退费后3日内，联系工作人员退还3000元给武汉木仓科技股份有限公司。如学员在收到退费后，不予退还或逾期退还武汉木仓科技股份有限公司的，则应就逾期每日承担学费金额百分之十的违约金。
                </div>

                <p class="p1">六、学车节周边领取规则说明</p>

                <p class="p2">
                    1、已支付6.6元报名成功用户，可邀请有学车意向好友支付6.6元参与活动。邀请人可获得免费抽驾考宝典周边礼品的机会；被邀请好友支付6.6元并填写报名信息，获得抽免单券一张，6.6元未中奖可退。
                </p>

                <p class="p2">
                    2、已获得学车周边领取资格的用户，需在当期直播开奖前，添加官方发货企微号并登记收货信息，且确保所填写的手机号与参加活动留资的手机号一致，否则视为用户主动放弃领取学车周边，官方将不予发货。
                </p>

                <p class="p2">
                    3、已获得学车周边领取资格的用户，如在开奖前已退费6.6元，视为用户主动放弃领取学车周边，官方将不予发货。
                </p>

                <p class="p2">
                    4、已获得学车周边领取资格的用户，如未抽中【3000元学车现金补贴】，支付的6.6元仍在当期开奖直播结束后2个工作日内沿支付方式原路退回，已获得的学车周边仍可免费包邮赠送。
                </p>

                <p class="p2">
                    5、学车周边的发货时间: 将在每期直播开奖结束后7个工作日内，统一安排发货。
                </p>

                <p class="p2">
                    6、每人限领1次，即每个手机号仅限领取1份学车周边。对于以不正当方式参与活动的用户，包括但不限于重复更换手机号注册，已报名但反复参与，利用奖品套现，利用程序漏洞等，官方有权取消其兑奖资格及参与活动资格。
                </p>

                <p class="p1">七、驾校抵扣券规则说明</p>
                <p class="p2">1、 支付说明:</p>
                <p class="p2">
                    1) 在线支付的抵用券，购买完成后，可直接用于抵扣报名费;<br />
                    2) 支付的费用直接到驾校账户，平台不收取任何支付手续费;<br />
                    3)
                    为了保障用户权益，建议使用驾考宝典平台支付，若使用其他支付方式导致纠纷，驾考宝典平台不承担任何责任。
                </p>
                <p class="p2">2、使用说明:</p>
                <p class="p2">
                    1)
                    购买的报名抵扣券，仅限于购买时选择的驾校使用，具体使用形式，请定向咨询该驾校;<br />
                    2) 支付所产生的发票问题请询问驾校;<br />
                    3) 部分班型如因特殊情况不可提供，可与驾校协商一致后，更换班型;
                </p>
                <p class="p2">3、退款说明:</p>
                <p class="p2">
                    1) 支付完成的费用，在有效时间内，未到驾校核销的，可随时申请退款:<br />
                    2) 支付完成的费用，超过30天有效时间，未核销的，系统自动原路退回费用;<br />
                    3) 支付完成后，如已核销，可直接联系驾校，协商一致后，申请退款;<br />
                    4)
                    款项默认退回原支付方，特殊情况，驾校可通过网银或者支付宝等方式退回费用到支付方
                </p>
                <p class="p2">
                    4、如用户抽中【3000元学车现金补贴】，该奖项的兑换，不受报名抵扣券使用的影响。
                </p>
                <p class="p2">
                    5、售后支持·如有疑问，可联系客服电话4000561617帮助您协调处理。
                </p>

                <p class="p1">
                    八、此活动最终解释权归驾考宝典app所有，苹果公司未参与。
                </p>
            </div>
        </popup>
    </div>
</template>

<script>
import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {URLParams, webOpen, listenBuySuccess, toAwait} from '../utils/tools'
import {createAppOrder} from '../server/xueche66'
import {find, throttle} from 'lodash-es'
import popup from '../components/dialog'
import xcjConfirmRules from './xcjConfirmRules'

export default {
    components: {
        popup,
        xcjConfirmRules,
    },
    data() {
        return {
            xcjConfirmRulesVisible: false,
            rulesVisible: false,
            visible: false,
            ref: URLParams._ref || 'live',
            payList: [
                {
                    channelName: 'weixin_mobile',
                    type: 2,
                    name: '微信支付',
                    checked: 1,
                },
                {
                    channelName: 'alipay_mobile',
                    type: 1,
                    name: '支付宝支付',
                    checked: 0,
                },
            ],
            checkAgreement: true,
            readed: false,
        }
    },
    props: {
        show: Boolean,
        lotteryInfo: Object,
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    methods: {
        confirm() {
            this.readed = true
            this.buy()
        },
        openRules() {
            this.rulesVisible = true
        },
        closed() {
            this.close()
        },
        close() {
            this.$emit('update:show', false)
        },
        checkPayType(index) {
            if (this.payList[index].checked) return false
            this.payList.forEach(item => {
                item.checked = 0
            })
            this.payList[index].checked = 1
        },
        buy(type) {
            if (this.checkAgreement && !this.readed) {
                this.xcjConfirmRulesVisible = true
                return
            }
            let pay
            if (type) {
                pay = find(this.payList, {type})
            } else {
                pay = find(this.payList, {checked: 1})
            }
            let payType = pay.type
            let payChannel = pay.channelName

            this.createAppOrder({
                payType,
                payChannel,
            })
        },
        gotoFromPage(orderNumber) {
            let activityName =
                this.lotteryInfo.activityType === 3
                    ? 'jiakaobaodian-xcsd2023'
                    : 'jiakaobaodian-66-new'
            // let activityName = URLParams.xueche66ActivityName || 'jiakaobaodian-xcsd2023'
            let url = `https://share-m.kakamobi.com/activity.kakamobi.com/${activityName}/?_ref=${this.ref}&sourcePage=zhibojian#/form?activityId=${this.lotteryInfo.nextLiveId}&orderNumber=${orderNumber}`
            setTimeout(() => {
                webOpen({
                    url: url,
                    titleBar: true,
                    title: '加载中',
                })
            }, 0)
            setTimeout(() => {
                let isTriggered = false
                pageSwitch.onPageShow(() => {
                    if (!isTriggered) {
                        isTriggered = true
                        this.$EventBus.$emit('updateLotteryData')
                    }
                })
            }, 300)
        },
        saveOrderNumber(orderNumber) {
            if (orderNumber) {
                localStorage.setItem('66_order', JSON.stringify({orderNumber: orderNumber}))
            }
        },
        createAppOrder: throttle(
            async function(config) {
                const retData = await createAppOrder({
                    channelCodes: this.lotteryInfo.channelCode,
                    liveId: this.lotteryInfo.nextLiveId,
                    pageName: stat.getPageName(),
                    payType: config.payType,
                    extraInfo: JSON.stringify({
                        _ref: this.ref,
                        pushCode: stat.getPushCode(),
                    }),
                    fragmentName1: config.fragmentName,
                    payPathType: 0,
                    pageData: JSON.stringify({
                        carStyle: URLParams.carStyle,
                        kemu: URLParams.kemu,
                        pageName: stat.getPageName(),
                        fragmentName1: config.fragmentName,
                        actionType: '点击',
                        citycode: URLParams.citycode,
                        sourceRef: this.ref,
                        payStatus: 2,
                        fromPathCode: URLParams.fromPathCode,
                        liveId: this.lotteryInfo.nextLiveId,
                        actionName: '去支付',
                        payPathType: 0,
                        pushCode: stat.getPushCode(),
                    }),
                    platformType: 'MOBILE',
                },
                {showErrorInfo: true})
                this.saveOrderNumber(retData.orderNumber)

                var extraData = {}
                let url =
                    'http://pay.nav.mucang.cn/pay?payType=vip&content=' +
                    encodeURIComponent(retData.content) +
                    '&orderNumber=' +
                    retData.orderNumber +
                    '&extraData=' +
                    JSON.stringify(extraData) +
                    '&payChannel=' +
                    config.payChannel

                window.location.href = url
                const loading = this.$pageLoading({
                    frozen: true,
                })
                let [err] = await toAwait(listenBuySuccess(retData.orderNumber, 'xueche'))
                loading.close()
                if (err) {
                    return
                }
                this.close()
                this.gotoFromPage(retData.orderNumber)
            },
            1000,
            {trailing: false}
        ),
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.buy-lottery-popup {
    /deep/ .dialog .close {
        background: url(../assets/images/<EMAIL>) no-repeat;
        background-size: 50px 50px;
    }
    .content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .image {
            background: url(../assets/images/<EMAIL>) no-repeat center center / 100%
                auto;
            height: 724px;
        }
        .inner {
            background: #fff;
            padding-bottom: calc(constant(safe-area-inset-bottom) - 30px);
            padding-bottom: calc(env(safe-area-inset-bottom) - 30px);
        }
    }
    .pay_list {
        display: flex;
        .fontSizeWithElder(28px);
        color: #333;
        padding: 25px 0 10px 45px;
        > div {
            margin-right: 30px;
            padding: 0 10px 0 72px;
            background: url(../assets/images/<EMAIL>) no-repeat 20px center;
            background-size: 36px auto;

            &.checked {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
    }
    .buy-area {
        position: relative;
        .btn {
            margin: 15px auto 0;
            padding-bottom: 40px;
            width: 738px;
            height: 174px;
            background: url(../assets/images/<EMAIL>) no-repeat;
            background-size: 100%;
            font-size: 40px;
            color: #fff;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tag {
            position: absolute;
            right: 40px;
            top: -30px;
            width: 260px;
            height: 80px;
            background: url(../assets/images/buy-lottery-tag.gif) no-repeat;
            background-size: 100%;
        }
    }

    .agreement {
        position: relative;
        margin-top: -15px;
        text-align: center;
        padding: 15px 65px 15px;
        .fontSizeWithElder(24px);
        color: #bb390b;
        display: flex;
        align-items: center;
        justify-content: center;
        .regular-checkbox {
            display: none;
        }
        .regular-checkbox + .regular-label {
            width: 32px;
            height: 32px;
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
            display: block;
            margin-right: 6px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
}
.rules-popup {
    .content {
        width: 620px;
        height: 858px;
        background: #fff;
        padding: 45px 48px 40px;
        border-radius: 30px;
        overflow: auto;
        color: #c43f0f;
        line-height: 40px;
    }
    .title {
        font-size: 32px;
        text-align: center;
    }
    .p1 {
        font-size: 30px;
        line-height: 50px;
        font-weight: bold;
    }
    .p2 {
        font-size: 24px;
    }
}
</style>
