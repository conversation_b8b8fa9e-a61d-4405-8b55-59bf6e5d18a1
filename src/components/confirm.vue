<template>
    <popup class="model-leave" :position="'center'" @closed="closed" :show.sync="visible">
        <div class="content">
            <h3 class="title">{{ title }}</h3>
            <div class="desc">{{ message }}</div>
            <slot></slot>
            <div class="btns">
                <div class="cancel" v-if="showCancelButton" @click="close">取消</div>
                <div class="confirm" v-if="showConfirmButton" @click="confirm">
                    {{ confirmText || '确定' }}
                </div>
            </div>
        </div>
    </popup>
</template>

<script>
import popup from './dialog'

export default {
    components: {popup},
    data() {
        return {
            visible: false,
        }
    },
    props: {
        title: String,
        message: String,
        show: Boolean,
        confirmText: String,
        showCancelButton: Boolean,
        showConfirmButton: Boolean,
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    mounted() {},
    methods: {
        // TODO 手动同步状态
        closed() {
            this.$emit('update:show', false)
        },
        close() {
            this.$emit('update:show', false)
            this.$emit('cancel')
        },
        confirm() {
            this.close()
            this.$emit('confirm')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';

.model-leave {
    /deep/ .dialog {
        width: 580px;
    }
    .content {
        background-color: #fff;
        border-radius: 20px;
        padding: 30px 20px 60px;
        text-align: center;
    }
    .title {
        font-size: 36px;
    }
    .desc {
        font-size: 32px;
        margin-top: 10px;
    }
    .btns {
        display: flex;
        padding: 0 10px;
        justify-content: space-around;
        margin-top: 32px;
        .cancel {
            flex: 1;
            margin: 0 20px;
            height: 88px;
            border: 1px solid #1dacf9;
            border-radius: 46px;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
            color: #1dacf9;
            line-height: 88px;
        }
        .confirm {
            flex: 1;
            margin: 0 20px;
            height: 88px;
            background: linear-gradient(135deg, #67cef8, #1e74fa);
            line-height: 88px;
            border-radius: 46px;
            font-size: 32px;
            text-align: center;
            color: #ffffff;
        }
    }
}
</style>
