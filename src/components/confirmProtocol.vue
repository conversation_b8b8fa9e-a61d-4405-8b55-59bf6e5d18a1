<template>
    <popup class="model-leave" :position="'center'" @closed="closed" :show.sync="visible">
        <div class="conf-wrap content">
            <div class="conf-t title">敬请确认</div>
            <div class="conf-c desc">
                {{type === 'training' ? '我已阅读并同意' : '开通前请先阅读'}}
                <span class="proto" @click="openAgreement">《{{ agreementName }}》</span>
            </div>
            <div class="btns">
                <div class="conf-ok confirm" @click="confirm"><span>同意并继续</span></div>
                <div class="conf-cancel cancel" @click="close"><span>不同意</span></div>
            </div>
        </div>
    </popup>
</template>

<script>
import popup from './dialog'
import {webOpen, URLParams} from '../utils/tools'
import {setReaded, setReaded2} from '../utils/agreementHelper'
import {agreementURL, agreementURL2} from '../utils/constant'
import store from '../store/index'

export default {
    components: {
        popup,
    },
    data() {
        return {
            visible: false,
        }
    },
    props: {
        show: Boolean,
        type: {
            type: String,
            default: 'vip',
        },
    },
    computed: {
        agreementName() {
            if (this.type === 'training') {
                return `驾考学习辅导服务协议`
            } else {
                return `${URLParams._product}会员协议`
            }
        },
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    methods: {
        // TODO 手动同步状态
        closed() {
            this.$emit('update:show', false)
        },
        close() {
            this.$emit('update:show', false)
            this.$emit('cancel')
        },
        confirm() {
            this.close()
            if (this.type === 'training') {
                store.commit('updatePayConfig', {readed2: true})
                setReaded2(true)
            } else {
                store.commit('updatePayConfig', {readed: true})
                setReaded(true)
            }
            this.$emit('confirm')
        },
        openAgreement() {
            if (this.type === 'training') {
                webOpen({
                    url: agreementURL2,
                    titleBar: true,
                    title: this.agreementName,
                })
            } else {
                webOpen({
                    url: agreementURL,
                    titleBar: true,
                    title: this.agreementName,
                })
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';

.model-leave {
    z-index: 7;
    /deep/ .dialog {
        width: 500px;
    }

    .content {
        background-color: #fff;
        border-radius: 20px;
        padding: 30px 20px 60px;
        text-align: center;
    }

    .title {
        font-size: 36px;
    }

    .desc {
        font-size: 28px;
        margin-top: 30px;
    }

    .proto {
        color: #cd5909;
    }

    .btns {
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        justify-content: space-around;
        margin-top: 10px;

        .cancel {
            flex: 1;
            margin: 0 20px;
            height: 88px;
            border: 1px solid #1dacf9;
            border-radius: 46px;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
            color: #1dacf9;
            line-height: 88px;
        }

        .confirm {
            flex: 1;
            margin: 20px 20px;
            height: 88px;
            background: linear-gradient(135deg, #67cef8, #1e74fa);
            line-height: 88px;
            border-radius: 46px;
            font-size: 32px;
            text-align: center;
            color: #ffffff;
        }
    }
}
</style>
