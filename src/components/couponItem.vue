<template>
    <div class="coupon-item">
        <div class="ofc" v-if="isIOS && !entityGoods" @click="goPayGuide">
            支付教程
        </div>
        <template v-else-if="goodsDetail.groupKey">
            <div class="ofc" v-if="couponUsable.priceCent" @click="goCoupon">
                已优惠{{ couponUsable.priceCent }}元
            </div>
            <div class="ofc" v-else @click="goCoupon">
                领取优惠券
            </div>
        </template>
    </div>
</template>

<script>
import {mapState, mapMutations} from 'vuex'
import {
    isIOS,
    webOpen,
} from '../utils/tools'
import {selectUserCoupon} from '../utils/coupon'

export default {
    data() {
        return {
            isIOS,
        }
    },
    props: {
        goodsDetail: Object,
        couponUsable: Object,
        entityGoods: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapState(['selectCoupons']),
    },
    methods: {
        ...mapMutations([
            'updateSelectCoupons',
        ]),
        goPayGuide() {
            webOpen({
                url: 'https://laofuzi.kakamobi.com/jkbd-vip/index/payguide.html?fromPageType=1',
                titleBar: false,
                title: '支付教程',
            })
        },
        async goCoupon() {
            const couponInfo = await selectUserCoupon(this.goodsDetail, this.couponUsable.couponCode)

            if (couponInfo) {
                this.updateSelectCoupons({[`${this.goodsDetail.groupKey}_selectCoupon`]: couponInfo})
            }
        },
    },
}
</script>
<style lang="less" scoped>
.coupon-item {
    .ofc {
        font-size: 22px;
    }
}
</style>