<template>
    <div v-if="remainderText !== '00:00'">
        <div v-show="showMotion" @click="openActive" class="motion">
            <div class="poster">
                <div class="bg">
                    ¥<span>{{ pushCoupon.couponPrice }}</span>
                </div>
            </div>
            <div class="timer">{{ remainderText }}</div>
        </div>

        <popup
            class="modal-box"
            :position="'center'"
            :bottomCloseIcon="true"
            :show.sync="showModal"
        >
            <div class="content">
                <div class="coupon">
                    <div class="sum">
                        ¥<span>{{ pushCoupon.couponPrice }}</span>
                    </div>
                    <div class="info">
                        <div class="name">直播间限时优惠券</div>
                        <div class="timer">
                            <span>{{ remainderText }}</span> 后过期
                        </div>
                    </div>
                </div>
                <div class="btn" @click="gopay">立即使用</div>
            </div>
        </popup>
    </div>
</template>

<script>
import {find} from 'lodash-es'
import {mapState} from 'vuex'
import {
    URLParams,
    formatRemainTime,
    trackEvent,
    formatPrice,
    getAuthToken,
} from '../utils/tools'
import {getCouponList} from '../server/goods'
import popup from '../components/dialog'

let localDataKey = 'jiaokaobaodian-zhibojian-coupon-popup'

export default {
    components: {popup},
    inject: ['waitPupopList'],
    data() {
        return {
            showMotion: false,
            showModal: false,
            remainderText: '00:00',
            timer: null,
        }
    },
    computed: {
        ...mapState(['pendantResource', 'pushCoupon']),
    },
    created() {
        this.getCouponList()
    },
    methods: {
        async getCouponList() {
            const authToken = await getAuthToken()
            if (!authToken) {
                return
            }
            let squirrelGoodsInfo = this.defaultVipGoodsDetail.squirrelGoodsInfo || {}
            getCouponList({
                tiku: URLParams.carStyle,
                sessionIds: this.defaultVipGoodsDetail.sessionIdList.join(','),
                groupKey: this.pendantResource.goodsKey,
                dataType: squirrelGoodsInfo.goodsDataType,
                dataCode: squirrelGoodsInfo.goodsDataCode,
            }).then(resData => {
                let coupon = find(resData.itemList, {
                    canUse: true,
                    // couponUniqKey: 'coupon-weizhifu-kemuall',
                })
                if (coupon) {
                    let _coupon = {
                        couponCode: coupon.couponCode,
                        priceCent: formatPrice(coupon.priceCent),
                    }

                    // todo 使用优惠券 _coupon

                    let isOpen = localStorage.getItem(localDataKey)
                    if (!isOpen) {
                        console.log('waitPupopList', 'coupon')
                        this.waitPupopList.push({
                            callback: () => {
                                this.showModal = true
                                localStorage.setItem(localDataKey, true)

                                let fragmentName1 = '优惠券弹窗'
                                let actionType = '出现'

                                // 埋点梳理-驾考宝典-1224
                                // 精品课直播间页_优惠券弹窗_出现
                                trackEvent({fragmentName1, actionType})
                            },
                        })
                    }

                    let couponPrice = _coupon.priceCent
                    let couponCode = _coupon.couponCode
                    this.$store.commit('setPushCoupon', {
                        couponCode,
                        couponPrice,
                    })
                    this.startTimer(coupon.validEndTime)
                    this.showMotion = true
                }
            })
        },
        startTimer(endTime) {
            clearInterval(this.timer)
            this.timer = null

            let date = +new Date()
            let remainder = endTime - date
            if (remainder < 0) {
                return
            }
            this.timer = setInterval(() => {
                let date = +new Date()
                let remainder = endTime - date
                this.remainderText = formatRemainTime(remainder, 'mm:ss')
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    this.timer = null
                    this.$store.commit('setPushCoupon', {})
                }
            }, 16)
        },
        openActive() {
            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                this.showModal = true
            })
        },
        gopay() {
            this.close()
            this.$EventBus.$emit('openDefaultMotion', '优惠券弹窗')

            let fragmentName1 = '优惠券弹窗'
            let actionName = '立即使用'
            let actionType = '点击'

            // 埋点梳理-驾考宝典-1224
            // 精品课直播间页_优惠券弹窗_点击立即使用
            trackEvent({fragmentName1, actionName, actionType, payPathType: -1})
        },
        close() {
            this.showModal = false
            this.showMotion = true
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.motion {
    margin-top: 20px;
    .poster {
        width: 100px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 100%;
        .bg {
            width: 100px;
            height: 100px;
            background: url(../assets/images/coupon-poster-bg.png) no-repeat center center;
            background-size: 80% auto;
            color: #fc045c;
            text-align: center;
            .fontSizeWithElder(20px);
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        span {
            .fontSizeWithElder(32px);
        }
    }
    .timer {
        margin: -24px 10px 0;
        height: 28px;
        background: rgba(0, 0, 0, 0.55);
        border-radius: 15px;
        .fontSizeWithElder(20px);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }
}
.modal-box {
    .content {
        width: 580px;
        height: 508px;
        background: url(../assets/images/coupon-popup-bg.png) no-repeat center center;
        background-size: 100% auto;
        padding-top: 148px;
    }
    .coupon {
        padding: 0 30px 0 24px;
        height: 176px;
        display: flex;
        align-items: center;
        .sum {
            color: #ff133b;
            text-align: center;
            .fontSizeWithElder(50px);
            font-weight: bold;
            width: 195px;
            span {
                padding-left: 5px;
                .fontSizeWithElder(80px);
            }
        }
        .name {
            .fontSizeWithElder(30px);
            font-weight: bold;
        }
        .info {
            padding-left: 30px;
        }
        .timer {
            .fontSizeWithElder(24px);
            color: #c77a13;
            font-weight: bold;
            span {
                .fontSizeWithElder(26px);
                color: #fd2044;
            }
        }
    }
    .btn {
        margin: 50px auto 0;
        width: 304px;
        height: 116px;
        background: url(../assets/images/coupon-popup-btn.png) no-repeat center center;
        background-size: 100% auto;
        .fontSizeWithElder(38px);
        font-weight: bold;
        color: #ed0026;
        padding-bottom: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
.landscape /deep/ {
    // landscape
    .motion {
        margin-left: 15px;
        .poster {
            width: 72px;
            height: 72px;
            .fontSizeWithElder(19px);
            .bg {
                width: 72px;
                height: 72px;
            }
            span {
                .fontSizeWithElder(26px);
            }
        }
        .timer {
            margin: -18px 10px 0;
            .fontSizeWithElder(16px);
            height: 20px;
        }
    }
}

.portrait,
.vertical {
    .motion {
        margin-left: 20px;
        width: 72px;
    }
}
</style>
