<template>
    <popup
        class="detainment-pupop"
        @closed="closed"
        :isInDialog="isInDialog"
        :position="'center'"
        :show.sync="visible"
    >
        <div :class="isInDialog ? 'in-wrap' : 'wrap'">
            <div class="content">
                <div class="title">真的要放弃{{ title }}吗？</div>
                <p class="desc" v-if="!isXueTang" @click="toCommunity">值不值，看看其他学员怎么说 ></p>
                <div class="image">
                    <img src="../assets/images/detainment-banner.png" />
                </div>
            </div>

            <div class="footer">
                <payList direction="vrt" theme="vrt1" :showPayForOther="true" v-if="isAndroid && isMucang" />
                <div class="line">
                    <div class="item cancel" @click="close">残忍放弃</div>
                    <div class="item confirm" @click="buy">¥{{ price }}立即开通</div>
                </div>
                <vipAgreement theme="blue"></vipAgreement>
            </div>
        </div>
    </popup>
</template>

<script>
import {isAndroid, webOpen, trackEvent, isMucang, isXueTang, setEmbeddedHeight} from '../utils/tools'
import {webClose} from '../utils/jump'
import popup from '../components/dialog'
import payList from '../components/payList'
import vipAgreement from '../components/vipAgreement'

export default {
    components: {popup, payList, vipAgreement},
    data() {
        return {
            isMucang,
            isXueTang,
            visible: false,
            isAndroid: isAndroid,
        }
    },
    props: {
        show: Boolean,
        title: String,
        price: [String, Number],
        groupKey: String,
        goodsUniqueKey: [String, Number],
        isInDialog: Boolean,
    },
    watch: {
        show(val) {
            this.visible = val
            if (this.show) {
                if (this.isInDialog) {
                    setEmbeddedHeight(440)
                }
                let fragmentName1 = '支付挽留弹窗'
                let actionType = '出现'

                // 埋点梳理-驾考宝典-0819
                // 精品课直播间页_支付挽留弹窗_出现
                trackEvent({
                    fragmentName1,
                    actionType,
                    groupKey: this.groupKey,
                    goodsUniqueKey: this.goodsUniqueKey,
                })
            }
        },
    },
    methods: {
        closed() {
            this.close()
        },
        toCommunity() {
            webOpen({
                url: 'http://saturn.nav.mucang.cn/tag/detail?tagId=37456',
            })
            let fragmentName1 = '支付挽留弹窗'
            let actionType = '点击'
            let actionName = '副标题'

            // 精品课直播间页_支付挽留弹窗_点击副标题
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                groupKey: this.groupKey,
                goodsUniqueKey: this.goodsUniqueKey,
            })
        },
        close() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.$emit('update:show', false)
            }
        },
        buy() {
            this.$emit('buy', '支付挽留弹窗')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.detainment-pupop {
    .wrap {
        border-radius: 20px;
        background-color: #fff;
        width: 640px;
        padding: 40px 30px 50px;
    }
    .in-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background: #fff;
        padding: 40px 30px 0;
    }
    .content {
        .title {
            .fontSizeWithElder(36px);
            color: #333333;
            text-align: center;
            padding-right: 15px;
        }
        .desc {
            .fontSizeWithElder(28px);
            color: #333333;
            text-align: center;
            padding: 10px 0;
        }
        .tags {
            > p {
                display: inline-block;
                width: calc(50% - 20px);
                text-align: center;
                margin: 10px;
                background: #fff4f4;
                border-radius: 8px;
                .fontSizeWithElder(26px);
                padding: 25px 0;
            }
        }
        .image {
            margin-top: 10px;
        }
    }
    .footer {
        .pay-list {
            padding: 0 10px;
        }
        .line {
            margin-top: 20px;
            padding: 10px 10px;
            display: flex;
        }
        .item {
            flex: 1;
            margin-right: 30px;
            height: 80px;
            line-height: 80px;
            border-radius: 40px;
            text-align: center;
            &:last-child {
                margin-right: 0;
            }
        }
        .cancel {
            box-sizing: border-box;
            background: #fff;
            color: #333;
            border: 1px solid #b2b2b2;
            .text {
                color: #666;
            }
        }
        .confirm {
            background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
            color: #fff;
            border: none;
            .del-price {
                color: #ffcfcd;
            }
        }
        /deep/ .agreement {
            padding: 10px 10px;
            .fontSizeWithElder(20px);
        }
    }
}
</style>
