<template>
    <transition :name="transitionName" :css="!!transitionName">
        <div class="home" v-if="show">
            <div class="mask" @click="closeOnBlank && close()" v-if="showMask && !isInDialog"></div>
            <div :class="[isInDialog ? 'in-dialog' : 'dialog', 'dialog-' + position]">
                <div
                    v-if="showClose"
                    @click="close"
                    class="close"
                    :class="bottomCloseIcon ? 'close-bc' : 'close-tr'"
                ></div>
                <slot></slot>
            </div>
        </div>
    </transition>
</template>

<script>
import pupopMixin from '../utils/pupopMixin'

export default {
    mixins: [pupopMixin],
    props: {
        show: Boolean,
        transitionName: {
            type: String,
            default: '',
        },
        showClose: {
            type: Boolean,
            default: true,
        },
        showMask: {
            type: Boolean,
            default: true,
        },
        position: {
            type: String,
            default: 'center',
        },
        bottomCloseIcon: {
            type: <PERSON>olean,
            default: false,
        },
        isInDialog: {
            type: Boolean,
            default: false,
        },
        closeOnBlank: {
            type: Boolean,
            default: true,
        },
    },
    watch: {
        show(val) {
            if (val) {
                // TODO 待验证
                document.body.appendChild(this.$el)
            }
        },
    },
    mounted() {},
    methods: {
        close() {
            this.$emit('update:show', false)
            this.$emit('closed')
        },
    },
    beforeDestroy() {
        if (this.$el && this.$el.parentNode) {
            this.$el.parentNode.removeChild(this.$el)
        }
    },
}
</script>

<style lang="less">
.slide-left-enter,
.slide-left-enter-active {
    animation: dialog-slide-left-in 0.3s ease;
}
.slide-left-leave,
.slide-left-leave-active {
    animation: dialog-slide-left-out 0.3s ease;
}
.slide-left-enter-active {
    animation: dialog-slide-left-in 0.3s ease;
}

@keyframes dialog-slide-left-in {
    0% {
        transform: translate(100%, 0);
    }
    100% {
        transform: translate(0, 0);
    }
}
@keyframes dialog-slide-left-out {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(100%, 0);
    }
}

.slide-top-enter,
.slide-top-enter-active {
    animation: dialog-slide-top-in 0.3s ease;
}
.slide-top-leave,
.slide-top-leave-active {
    animation: dialog-slide-top-out 0.3s ease;
}

@keyframes dialog-slide-top-in {
    0% {
        transform: translate(0, 100%);
    }
    100% {
        transform: translate(0, 0);
    }
}
@keyframes dialog-slide-top-out {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(0, 100%);
    }
}
</style>
<style lang="less" scoped>
@import '../assets/styles/variables';
.home {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 5;
    .mask {
        background-color: rgba(0, 0, 0, 0.6);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
    .dialog {
        z-index: 2;
        &.dialog-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        &.dialog-bottom {
            position: absolute;
            width: 100%;
            bottom: 0;
            left: 0;
        }
        &.dialog-top {
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
        }
    }
    .in-dialog {
        height: 100%;
        position: relative;
        z-index: 12;
    }
    .close {
        position: absolute;
        z-index: 2;
        &.close-tr {
            right: 14px;
            top: 14px;
            width: 50px;
            height: 50px;
            background: url(../assets/images/<EMAIL>) no-repeat;
            background-size: 50px 50px;
        }
        &.close-bc {
            left: 50%;
            bottom: -84px;
            transform: translateX(-50%);
            width: 56px;
            height: 56px;
            background: url(../assets/images/close3.png) no-repeat;
            background-size: 56px 56px;
        }
    }
}
</style>
