<template>
    <popup
        class="exam-answer-sheet-popup"
        :position="'bottom'"
        :show.sync="visible"
        @closed="closed"
        :isInDialog="isInDialog">
        <div class="wrap">
            <div class="header">答题卡</div>
            <template v-if="answerList.length">
                <div class="content scroll_view">
                    <div class="list">
                        <div class="item" :class="{right: item.answerStatus === 1, wrong: item.answerStatus === 2}" v-for="(item, index) in answerList" :key="index">
                            {{ index+1 }}
                        </div>
                    </div>
                </div>
            </template>
            <loading v-else bgColor="#666" />
        </div>
    </popup>
</template>

<script>
// import {MCProtocol} from '@simplex/simple-base'
import {trackEvent} from '../utils/tools'
import popup from './dialog'
import loading from './loading'
import {webClose} from '../utils/jump'
import {getAnswerSheet} from '../server/active'

export default {
    components: {
        popup,
        loading,
    },
    data() {
        return {
            visible: false,
            answerList: [],
        }
    },
    props: {
        show: Boolean,
        isInDialog: Boolean,
        sessionId: [Number, String],
        activityId: [Number, String],
    },
    watch: {
        async show(val) {
            this.visible = val
            if (val) {
                let resData = await getAnswerSheet({
                    sessionId: this.sessionId,
                    activityId: this.activityId,
                })
                this.answerList = resData.itemList

                // 精品课直播间页_答题进度_出现
                trackEvent({
                    fragmentName1: '答题进度',
                    actionType: '出现',
                })
            }
        },
    },
    methods: {
        closed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.close()
            }
        },
        close() {
            this.$emit('update:show', false)
        },
    },
}
</script>

<style lang="less" scoped>
.exam-answer-sheet-popup {
    /deep/ .dialog {
        height: 980px;
    }
    .wrap {
        display: flex;
        overflow: hidden;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        background: #fff;
        position: relative;
    }
    .scroll_view {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    .header {
        font-size: 36px;
        text-align: center;
        padding: 40px 30px 20px;
        font-weight: bold;
    }
    .content {
        padding: 0 20px;
        flex: 1;
    }
    .list {
        display: flex;
        flex-wrap: wrap;
        .item {
            margin: 10px 19px 20px;
            width: 80px;
            height: 80px;
            border: 1px solid #e8e8e8;
            border-radius: 50%;
            color: #a0a0a0;
            font-size: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            &.right {
                border-color: #dcf2fc;
                background: #dcf2fc;
                color: #0ca6f9;
            }
            &.wrong {
                border-color: #ffe2df;
                background: #ffe2df;
                color: #ff4a40;
            }
        }
    }
}
</style>
