<template>
    <popup
        class="exam-answer-sheet-popup"
        :position="'bottom'"
        :show.sync="visible"
        @closed="closed"
        :isInDialog="isInDialog">
        <div class="wrap">
            <div class="header">排行榜</div>
            <template v-if="rankList.length">
                <div class="content scroll_view">
                    <div class="line th">
                        <div class="t1">名次</div>
                        <div class="t2">用户</div>
                        <div class="t3">答题数</div>
                        <div class="t4">得分</div>
                    </div>
                    <div class="line tr" v-for="item in rankList" :key="item.rank">
                        <div class="t1 num">
                            <img src="../assets/images/icon-rank1.png" v-if="item.rank === 1" />
                            <img src="../assets/images/icon-rank2.png" v-else-if="item.rank === 2" />
                            <img src="../assets/images/icon-rank3.png" v-else-if="item.rank === 3" />
                            <template v-else>{{ item.rank }}</template>
                        </div>
                        <div class="t2">
                            <div class="user"><img :src="item.avatar" />{{ item.userNameMask }}</div>
                        </div>
                        <div class="t3">{{ item.answerCount }}</div>
                        <div class="t4">{{ item.score }}</div>
                    </div>
                </div>
                <div class="footer">* 排行榜数据仅供参考，最终排名数据以中奖名单为准</div>
            </template>
            <loading v-else-if="loading" bgColor="#666" />
            <div v-else class="no-data">暂无数据</div>
        </div>
    </popup>
</template>

<script>
// import {MCProtocol} from '@simplex/simple-base'
import {trackEvent, toAwait} from '../utils/tools'
import popup from './dialog'
import loading from './loading'
import {webClose} from '../utils/jump'
import {getRankList} from '../server/active'

export default {
    components: {
        popup,
        loading,
    },
    data() {
        return {
            visible: false,
            rankList: [],
            loading: true,
        }
    },
    props: {
        show: Boolean,
        isInDialog: Boolean,
        sessionId: [Number, String],
        activityId: [Number, String],
    },
    watch: {
        async show(val) {
            this.visible = val
            if (val) {
                this.loading = true
                let [err, resData] = await toAwait(getRankList({
                    sessionId: this.sessionId,
                    activityId: this.activityId,
                }))
                this.loading = false
                if (err) {
                    return
                }
                this.rankList = resData.itemList

                // 精品课直播间页_实时排名_出现
                trackEvent({
                    fragmentName1: '实时排名',
                    actionType: '出现',
                })
            }
        },
    },
    methods: {
        closed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.close()
            }
        },
        close() {
            this.$emit('update:show', false)
        },
    },
}
</script>

<style lang="less" scoped>
.exam-answer-sheet-popup {
    /deep/ .dialog {
        height: 980px;
    }
    .wrap {
        display: flex;
        overflow: hidden;
        flex-direction: column;
        height: 100%;
        background: #fff;
        position: relative;
    }
    .scroll_view {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    .header {
        font-size: 36px;
        text-align: center;
        padding: 40px 30px 20px;
        font-weight: bold;
    }
    .content {
        padding: 0 30px;
        flex: 1;
    }
    .line {
        display: flex;
        text-align: center;
        align-items: center;
        &.th {
            color: #a0a0a0;
            font-size: 26px;
            height: 60px;
        }
        &.tr {
            font-size: 28px;
            color: #333333;
            height: 104px;
        }
        .t1 {
            width: 104px;
        }
        .t2 {
            flex: 1;
            overflow: hidden;
        }
        .t3 {
            width: 128px;
        }
        .t4 {
            width: 128px;
        }
        .num {
            img {
                width: 38px;
            }
        }
        .user {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            img {
                width: 68px;
                border-radius: 50%;
                margin-right: 10px;
            }
        }
        .pt {
            font-weight: bold;
        }
    }
    .footer {
        height: 60px;
        background: #f1fbff;
        font-size: 24px;
        color: #04a5ff;
        text-align: center;
        line-height: 60px;
    }
    .no-data {
        background: url(../assets/images/no-data.png) no-repeat center 40px;
        background-size: 440px 334px;
        font-size: 28px;
        text-align: center;
        padding-top: 402px;
        padding-bottom: 80px;
    }
}
</style>
