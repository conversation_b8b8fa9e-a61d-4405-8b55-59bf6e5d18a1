<template>
    <div class="exam-report">
        <div class="box pb40">
            <div class="overview" :class="{side: examDetail.badges && examDetail.badges.length}">
                <div class="comp">
                    <div class="sk">
                        <div class="score">
                            <span>{{ examDetail.score }}</span>分
                        </div>
                        <div class="title">驾考宝典直播间万人模考大赛</div>
                        <div class="time">{{ formatDate(examDetail.examStartTime) }}</div>
                    </div>
                    <div class="badges" :class="'num' + Math.min(3, examDetail.badgesRes.length)" v-if="examDetail.badgesRes && examDetail.badgesRes.length">
                        <div class="item" v-for="item in examDetail.badgesRes" :key="item.code" @click="toBadge(item)">
                            <div class="image"><img :src="item.icon"></div>
                            <div class="name">{{ item.name }}</div>
                        </div>
                    </div>
                </div>
                <div class="info">
                    <div class="item rank">
                        <div class="num">
                            <img src="../assets/images/exam-icon-rank1.png" v-if="examDetail.rawRank === 1" />
                            <img src="../assets/images/exam-icon-rank2.png" v-else-if="examDetail.rawRank === 2" />
                            <img src="../assets/images/exam-icon-rank3.png" v-else-if="examDetail.rawRank === 3" />
                            <template v-else>{{ examDetail.rank }}</template>
                        </div>
                        <div class="name">我的排名</div>
                    </div>
                    <div class="item">
                        <div class="num">{{ examDetail.highestScore }}</div>
                        <div class="name">本场最高分</div>
                    </div>
                    <div class="item">
                        <div class="num">{{ examDetail.greaterNum }}%</div>
                        <div class="name">已击败考生</div>
                    </div>
                </div>
            </div>
            <div class="overview-btns" v-if="!shareMode">
                <div class="btn1" @click="viewErr">查看错题</div>
                <div class="btn2" @click="viewHistory">回顾试卷</div>
            </div>
        </div>
        <div class="box mt20 pb40">
            <div class="tit">本次考试情况</div>
            <div class="box2 mt30">
                <div class="tit2">答题情况</div>
                <div class="tiku-info">
                    <div class="item">
                        <div class="num">{{ examDetail.unAnsweredCount }}</div>
                        <div class="name">未答</div>
                    </div>
                    <div class="item">
                        <div class="num">{{ examDetail.rightCount }}</div>
                        <div class="name">答对</div>
                    </div>
                    <div class="item">
                        <div class="num">{{ examDetail.wrongCount }}</div>
                        <div class="name">答错</div>
                    </div>
                    <div class="item duration">
                        <div class="num">{{ secToTime(examDetail.takeTime) }}</div>
                        <div class="name">耗时</div>
                    </div>
                </div>
            </div>
            <div class="box2 mt30">
                <div class="tit2">题型正确率</div>
                <div class="tiku-info">
                    <div class="item">
                        <div class="num">{{Math.floor(examDetail.singleQuestionRightRate*100)}}%</div>
                        <div class="name">单选</div>
                    </div>
                    <div class="item">
                        <div class="num">{{Math.floor(examDetail.judgeQuestionRightRate*100)}}%</div>
                        <div class="name">判断</div>
                    </div>
                    <div class="item" v-if="kemu !== 1">
                        <div class="num">{{Math.floor(examDetail.multiQuestionRightRate*100)}}%</div>
                        <div class="name">多选</div>
                    </div>
                </div>
            </div>
            <div class="box2 mt20">
                <div class="tit2">考点掌握情况</div>
                <div class="tiku-info">
                    <div class="item">
                        <div class="num">{{ quesitonTagsList.length }}</div>
                        <div class="name">总考点</div>
                    </div>
                    <div class="item" @click="unskilledMethod('mastered')">
                        <div class="num">{{ quesitonTagsList.length-hasErrorKnowledge.length }}</div>
                        <div class="name">掌握较好 <span v-if="!shareMode" class="icon"></span></div>
                    </div>
                    <div class="item" @click="unskilledMethod('unmastered')">
                        <div class="num">{{hasErrorKnowledge.length}}</div>
                        <div class="name">未熟练掌握 <span v-if="!shareMode" class="icon"></span></div>
                    </div>
                </div>
            </div>
            <div class="box3 mt20" v-if="hasErrorKnowledge.length">
                <div class="tit2">弱项考点</div>
                <div class="kaodian-info">
                    <div
                        class="item"
                        :class="{static: shareMode}"
                        v-for="item in hasErrorKnowledge.slice(0,10)"
                        :key="item.tagId"
                        @click="gotoKnowledgeDetail(item.tagId)">
                        <div class="name">{{ item.name }}</div>
                        <div class="num">{{ item.count }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {URLParams, toast, trackEvent, formatDate, secToTime, webOpen} from '../utils/tools'

export default {
    data() {
        return {
            kemu: +URLParams.kemuNum,
        }
    },
    props: {
        examDetail: Object,
        quesitonTagsList: Array,
        hasErrorKnowledge: Array,
        shareMode: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        toBadge(item) {
            let {code, active} = item
            if (!active) return
            webOpen({
                url: `https://laofuzi.kakamobi.com/achievement/index.html?animationCode=${code}`,
            })

            // 精品课直播间页_答题报告弹窗_点击徽章
            trackEvent({
                fragmentName1: '答题报告弹窗',
                actionType: '点击',
                actionName: '徽章',
            })
        },
        formatDate(date) {
            if (date) {
                return formatDate(date, 'yyyy.MM.dd hh:mm')
            } else {
                return ''
            }
        },
        secToTime(time) {
            if (time) {
                return secToTime(time / 1000, true)
            } else {
                return ''
            }
        },
        viewErr() {
            if (!this.examDetail.exam) {
                return
            }
            if (this.examDetail.wrongQuestionIds.length === 0) {
                toast('很棒，本次无错题')
                return
            }
            let errQuestion = []
            this.examDetail.examAnswerList && this.examDetail.examAnswerList.forEach((res) => {
                if (this.examDetail.wrongQuestionIds.indexOf(res.questionId + '') !== -1) {
                    errQuestion.push({id: res.questionId, idx: res.answer})
                }
            })
            errQuestion = JSON.stringify(errQuestion)
            webOpen({
                url: 'http://jiakao.nav.mucang.cn/review_exam?title=答错题目&questionList=' + errQuestion,
            })

            // 精品课直播间页_答题报告弹窗_点击查看错题
            trackEvent({
                fragmentName1: '答题报告弹窗',
                actionType: '点击',
                actionName: '查看错题',
            })
        },
        viewHistory() {
            let questionList = []
            this.examDetail.allQuestionIds.forEach((res) => {
                let tag = false
                let itemQuestion = {
                    answer: '',
                }
                this.examDetail.examAnswerList && this.examDetail.examAnswerList.forEach((item) => {
                    if (res === item.questionId) {
                        tag = true
                        itemQuestion = item
                    }
                })
                if (tag) {
                    questionList.push({id: res, idx: itemQuestion.answer})
                } else {
                    questionList.push({id: res, idx: ''})
                }
            })
            questionList = JSON.stringify(questionList)
            webOpen({
                url: 'http://jiakao.nav.mucang.cn/review_exam?title=回顾试卷&questionList=' + questionList,
            })

            // 精品课直播间页_答题报告弹窗_点击回顾试卷
            trackEvent({
                fragmentName1: '答题报告弹窗',
                actionType: '点击',
                actionName: '回顾试卷',
            })
        },
        unskilledMethod(type) {
            webOpen({
                url: 'http://jiakao.nav.mucang.cn/knowledge-list?tab=' + type,
            })
        },
        gotoKnowledgeDetail(id) {
            webOpen({
                url: 'http://jiakao.nav.mucang.cn/knowledge-detail?knowledgeId=' + id + '&showRelatedQuestions=true',
            })
        },
    },
}
</script>

<style lang="less" scoped>
.mt20 {
    margin-top: 20px !important;
}
.mt30 {
    margin-top: 30px !important;
}
.pb40 {
    padding-bottom: 40px !important;
}
.exam-report {
    .box {
        background: #ffffff;
        border-radius: 16px;
        .tit {
            font-size: 32px;
            font-weight: bold;
            padding: 30px 0 0 30px;
        }
    }
    .overview {
        .comp {
            display: flex;
        }
        .sk {
            flex: 1;
        }
        .score {
            font-size: 36px;
            color: #04a5ff;
            line-height: 1;
            flex: 1;
            text-align: center;
            padding-left: 30px;
            padding-top: 30px;
            span {
                font-size: 104px;
                font-weight: bold;
            }
        }
        .title {
            font-size: 28px;
            font-weight: 500;
            text-align: center;
        }
        .time {
            font-size: 26px;
            text-align: center;
            color: #666;
        }
        .info {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            background: #f4f9fd;
            border-radius: 16px;
            height: 148px;
            margin: 20px 30px 0;
            justify-content: space-evenly;
            .num {
                font-size: 32px;
                font-weight: 500;
                img {
                    width: 48px;
                }
                // &.right {
                //     color: #04a5ff;
                // }
                // &.wrong {
                //     color: #fa0431;
                // }
            }
            .name {
                color: #6e6e6e;
                font-size: 26px;
                margin-top: 8px;
            }
        }
        .badges {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            width: 234px;
            padding-top: 16px;
            .item {
                padding: 0 4px;
                text-align: center;
                .image {
                    margin: 0 auto;
                }
                .name {
                    min-height: 40px;
                    white-space: nowrap;
                }
            }
            &.num1 .item {
                .image {
                    width: 136px;
                }
                flex: 1;
                font-size: 26px;
            }
            &.num2 {
                margin-right: 16px;
                .item {
                    .image {
                        width: 90px;
                    }
                    width: 50%;
                    font-size: 22px;
                }
            }
            &.num3 {
                margin-right: 20px;
                align-items: baseline;
                .item {
                    .image {
                        width: 72px;
                    }
                    width: 50%;
                    font-size: 20px;
                }
            }
        }
        &.side {
            .score {
                text-align: left;
                padding-left: 40px;
            }
            .title {
                text-align: left;
                padding-left: 40px;
            }
            .time {
                text-align: left;
                padding-left: 40px;
            }
        }
    }
    .overview-btns {
        display: flex;
        padding: 40px 40px 10px;
        justify-content: space-around;
        .btn1 {
            flex: 1;
            margin: 0 30px;
            height: 68px;
            border: 1px solid #333;
            border-radius: 43px;
            font-size: 28px;
            font-weight: 500;
            text-align: center;
            color: #333;
            line-height: 68px;
        }
        .btn2 {
            flex: 1;
            margin: 0 30px;
            height: 68px;
            background: linear-gradient(135deg, #67cef8, #1e74fa);
            line-height: 68px;
            border-radius: 43px;
            font-size: 28px;
            text-align: center;
            color: #ffffff;
        }
    }
    .box2 {
        margin: 20px 30px 0;
        border: 1px solid #f3f3f6;
        border-radius: 16px;
        overflow: hidden;
        .tit2 {
            font-size: 30px;
            font-weight: bold;
            padding-left: 30px;
            height: 80px;
            line-height: 80px;
            background: linear-gradient(90deg, #f3f6ff 2%, #fffeff);
        }
    }
    .tiku-info {
        display: flex;
        text-align: center;
        .item {
            flex: 1;
            height: 144px;
            padding-top: 20px;
        }
        .num {
            font-size: 40px;
            font-weight: bold;
        }
        .name {
            color: #666;
            font-size: 26px;
            margin-top: 8px;
            .icon {
                display: inline-block;
                width: 20px;
                height: 20px;
                background: url(../assets/images/right_small.png) no-repeat
                    center;
                background-size: 100%;
                vertical-align: middle;
            }
        }
    }
    .box3 {
        margin: 20px 30px 0;
        border: 1px solid #f3f3f6;
        border-radius: 16px;
        overflow: hidden;
        .tit2 {
            font-size: 30px;
            font-weight: bold;
            padding-left: 30px;
            height: 80px;
            line-height: 80px;
            background: linear-gradient(90deg, #fff3e8 2%, #fffeff);
        }
    }
    .kaodian-info {
        .item {
            height: 88px;
            padding: 0 40px 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            &:not(:first-child) {
                border-top: 1px solid #f3f3f6;
            }
            &:not(.static)::after {
                content: '';
                position: absolute;
                top: 29px;
                right: 10px;
                width: 30px;
                height: 30px;
                background: url(../assets/images/right_icon.png) no-repeat center;
                background-size: 100%;
            }
        }
        .num {
            color: #999;
            font-size: 28px;
        }
        .name {
            color: #333;
            font-size: 28px;
        }
    }
}
</style>
