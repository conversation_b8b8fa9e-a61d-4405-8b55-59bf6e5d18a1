<template>
    <div>
        <popup
            class="question-report-popup"
            :position="'bottom'"
            :show.sync="visible"
            @closed="closed"
            :isInDialog="isInDialog">
            <div class="wrap">
                <div class="header">直播答题报告</div>
                <template v-if="examDetail.exam">
                    <div class="content scroll_view">
                        <examReport :examDetail="examDetail" :quesitonTagsList="quesitonTagsList" :hasErrorKnowledge="hasErrorKnowledge" />
                    </div>
                    <div class="footer">
                        <span class="btn-icon"></span>
                        <div class="btn" @click="share">
                            {{ shareCommunity ? '分享到驾考宝典社区' : '分享我的答题报告' }}
                        </div>
                    </div>
                </template>
                <loading v-else-if="loading" bgColor="#666" />
                <div v-else class="no-data">暂无数据</div>
            </div>
        </popup>
        <div class="share-bg" v-if="visible" ref="share-img">
            <div class="share-header">
                <div class="logo2">
                    <img src="../assets/images/logo.png" />
                </div>
            </div>
            <examReport :examDetail="examDetail" :quesitonTagsList="quesitonTagsList" :hasErrorKnowledge="hasErrorKnowledge" :shareMode="true" />
            <div class="share-bottom-qita">
                <div class="left">
                    <div class="title">找驾校 考驾照 就用驾考宝典</div>
                    <div class="desc">扫码进入直播间一起刷题吧!</div>
                </div>
                <div class="right">
                    <div class="dg">
                        <img src="../assets/images/QRcode.png" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {find} from 'lodash-es'
import html2canvas from 'html2canvas'
import {URLParams, webOpen, trackEvent} from '../utils/tools'
import popup from './dialog'
import loading from './loading'
import examReport from './examReport'
import {webClose} from '../utils/jump'
import {getExamDetail, addSharePresent, getBaseInfo, getBadgeData} from '../server/active'

MCProtocol.register('Vip.getQuestionTags', function(config) {
    return {
        allIds: config.allIds,
        rightIds: config.rightIds,
        kemu: +URLParams.kemuNum,
        callback: config.callback,
    }
})

MCProtocol.register('jiakao-global.web.previewSharedContent', function(config) {
    return config
})

// 'livehouse.luban.mucang.cn/notify/updateActivityAward'
MCProtocol.register('livehouse.notify.updateActivityAward', function(config) {
    return config
})

function groupBy(list, key) {
    const obj = {}
    list.map((item) => {
        if (!obj.hasOwnProperty(item[key])) {
            obj[item[key]] = []
        }
        obj[item[key]].push(item)
    })
    return obj
}

function sortByCount(list) {
    // 先按相同的count分组一起并按id来排序
    if (!list) {
        return []
    }
    const sortNewArray = []
    const lastSortHasRrror = []
    const groupByCountObject = groupBy(list, 'count')
    for (const key in groupByCountObject) {
        groupByCountObject[key] = groupByCountObject[key].sort((a, b) => {
            return +b.tagId - +a.tagId
        })
        sortNewArray.push({count: Number(key), childen: groupByCountObject[key]})
    }
    // 先转成数组，后按count大小来进行排序
    const arrayObj = sortNewArray.sort((a, b) => {
        return b.count - a.count
    })
    arrayObj.forEach((res) => {
        lastSortHasRrror.push(...res.childen)
    })
    return lastSortHasRrror
}

const Features = (window.mucang && window.mucang.features) || []
const hasSaveFile = Features.indexOf('core.luban.mucang.cn/native/saveFile') > -1
const hasPublishResult = Features.indexOf('saturn.luban.mucang.cn/publishResult') > -1

MCProtocol.register('Core.Native.saveFile', function(config) {
    config.type = config.type || 1
    return config
})

MCProtocol.register('saturn.publishResult', function(config) {
    return config
})

export default {
    components: {
        popup,
        loading,
        examReport,
    },
    data() {
        return {
            visible: false,
            examDetail: {
                exam: false,
            },
            loading: true,
            quesitonTagsList: [],
            hasErrorKnowledge: [],
            activityName: '',
            shareCommunity: hasSaveFile && hasPublishResult,
        }
    },
    props: {
        show: Boolean,
        isInDialog: Boolean,
        sessionId: [Number, String],
        activityId: [Number, String],
        openShareMenu: Boolean,
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                this.examDetail = {
                    exam: false,
                }
                this.loading = true
                // TODO async
                getExamDetail({
                    sessionId: this.sessionId,
                    activityId: this.activityId,
                }).then(async resData => {
                    resData.allQuestionIds = (resData.allQuestionIds || '').split(',').filter(item => item > 0)
                    resData.rightQuestionIds = (resData.rightQuestionIds || '').split(',').filter(item => item > 0)
                    resData.wrongQuestionIds = (resData.wrongQuestionIds || '').split(',').filter(item => item > 0)
                    resData.singleQuestionRightRate = resData.singleQuestionRightRate || 0
                    resData.judgeQuestionRightRate = resData.judgeQuestionRightRate || 0
                    resData.multiQuestionRightRate = resData.multiQuestionRightRate || 0
                    let allQuestionCount = resData.allQuestionIds.length
                    let rightCount = resData.rightQuestionIds.length
                    let wrongCount = resData.wrongQuestionIds.length
                    let unAnsweredCount = allQuestionCount - rightCount - wrongCount
                    let greaterNum
                    if (resData.rawRank === 1) {
                        greaterNum = 100
                    } else {
                        greaterNum = Math.floor((resData.userCount - resData.rawRank) / resData.userCount * 100)
                    }
                    this.examDetail = {
                        ...resData,
                        greaterNum,
                        unAnsweredCount,
                        rightCount,
                        wrongCount,
                        badgesRes: [],
                    }

                    const getQuestionTags = () => {
                        return new Promise((resolve) => {
                            MCProtocol.Vip.getQuestionTags({
                                allIds: resData.allQuestionIds.join(','),
                                rightIds: resData.rightQuestionIds.join(','),
                                callback: (ret) => {
                                    let data
                                    try {
                                        if (typeof ret.data === 'string') {
                                            data = JSON.parse(ret.data)
                                        } else {
                                            data = ret.data
                                        }
                                    } catch (e) {
                                        data = []
                                    }
                                    data = sortByCount(data)
                                    const hasErrorKnowledge = data && data.filter((res) => {
                                        return res.errorRate > 0
                                    })
                                    this.quesitonTagsList = data
                                    this.hasErrorKnowledge = hasErrorKnowledge || []
                                    resolve()
                                },
                            })
                        })
                    }
                    await Promise.all([this.getBadgeData(), getQuestionTags()])

                    if (this.openShareMenu) {
                        this.$nextTick(() => {
                            this.share()
                        })
                    }
                }).finally(() => {
                    this.loading = false
                })

                getBaseInfo({
                    activityId: this.activityId,
                }).then(resData => {
                    this.activityName = `第${resData.index}场 ${resData.activityName}`
                })

                // 精品课直播间页_答题报告弹窗_出现
                trackEvent({
                    fragmentName1: '答题报告弹窗',
                    actionType: '出现',
                })
            }
        },
    },
    methods: {
        closed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.close()
            }
        },
        close() {
            this.$emit('update:show', false)
        },
        async getBadgeData() {
            if (!this.examDetail.badges || this.examDetail.badges.length === 0) {
                return
            }
            let resData = await getBadgeData({
                code: this.examDetail.badges.map(item => item.badgeCode).join(','),
            })
            resData = resData.itemList || resData
            this.examDetail.badgesRes = resData.map(item => {
                let k = find(this.examDetail.badges, {badgeCode: item.code})
                return {
                    name: item.badgeName,
                    code: item.code,
                    icon: item.triggerImage,
                    active: k?.grant,
                }
            })
        },
        getJpeg(dom) {
            return new Promise((resolve) => {
                html2canvas(dom, {
                    width: dom?.scrollWidth,
                    height: dom?.scrollHeight,
                    windowWidth: dom?.scrollWidth,
                    windowHeight: dom?.scrollHeight,
                    x: 0,
                    allowTaint: false,
                    useCORS: true,
                    y: window.pageYOffset,
                }).then((canvas) => {
                    const jpeg = canvas.toDataURL('image/jpeg', 1.0)
                    // resolve(new Blob([jpeg]));
                    resolve(jpeg)
                })
            })
        },
        async share() {
            const shareImg = this.$refs['share-img']
            const imgSrc = await this.getJpeg(shareImg)
            const done = async () => {
                let present = await addSharePresent({
                    sessionId: this.sessionId,
                    activityId: this.activityId,
                })
                if (present) {
                    if (this.isInDialog) {
                        MCProtocol.livehouse.notify.updateActivityAward()
                    } else {
                        this.$EventBus.$emit('winingNotify')
                    }
                }
            }

            if (hasSaveFile && hasPublishResult) {
                const imgPath = await this.saveFile(imgSrc)
                MCProtocol['jiakao-global'].web.previewSharedContent({
                    image: imgSrc,
                    title: '我的报告',
                    shareName: '公共页_分享组件_点击分享到',
                    kemu: URLParams.kemuNum,
                })
                MCProtocol.saturn.publishResult({
                    callback: async (data) => {
                        console.log('publishResult', data)
                        done()
                    },
                })

                setTimeout(() => {
                    webOpen({
                        url: 'http://saturn.nav.mucang.cn/topic/publish?tagIds=87115&topicType=1024&content=' + '' + '&imgList=' + JSON.stringify([imgPath]),
                    })
                }, 99)
            } else {
                MCProtocol['jiakao-global'].web.previewSharedContent({
                    image: imgSrc,
                    title: '我的报告',
                    shareName: '公共页_分享组件_点击分享到',
                    kemu: URLParams.kemuNum,
                })
                done()
            }

            // 精品课直播间页_答题报告弹窗_分享入口_点击分享
            trackEvent({
                fragmentName1: '答题报告弹窗',
                fragmentName2: '分享入口',
                actionType: '点击',
                actionName: '分享',
            })
        },
        async saveFile(imgSrc) {
            return new Promise((resolve) => {
                MCProtocol.Core.Native.saveFile({
                    file: imgSrc.replace(/.+base64,/, ''),
                    callback(data) {
                        resolve(data.data && data.data.fileLocalPath)
                    },
                })
            })
        },
    },
}
</script>

<style lang="less" scoped>
.question-report-popup {
    /deep/ .dialog {
        height: 980px;
    }
    .wrap {
        display: flex;
        overflow: hidden;
        flex-direction: column;
        height: 100%;
        background: linear-gradient(180deg,#ddf1ff, #e2f7ff);
        position: relative;
    }
    .scroll_view {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    .header {
        font-size: 36px;
        text-align: center;
        padding: 40px 30px 20px;
        font-weight: bold;
    }
    .content {
        padding: 0 30px 160px;
        flex: 1;
    }
    .footer {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        background-color: #fff;
        padding-top: 80px;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 40px);
        background: linear-gradient(180deg,rgba(255,255,255,0.00) 1%, #ffffff 50%);
        .btn {
            margin: 0 auto 40px;
            width: 690px;
            height: 80px;
            background: linear-gradient(135deg, #67cef8, #1e74fa);
            line-height: 80px;
            border-radius: 56px;
            font-size: 30px;
            text-align: center;
            color: #ffffff;
            position: relative;
        }
        .btn-icon {
            position: absolute;
            top: -22px;
            right: 88px;
            width: 122px;
            height: 104px;
            background: url(../assets/images/exam-report-btn-icon.png) no-repeat center / 100% auto;
        }
    }
    .no-data {
        margin-top: 40px;
        background: url(../assets/images/no-data.png) no-repeat center 40px;
        background-size: 440px 334px;
        font-size: 28px;
        text-align: center;
        padding-top: 402px;
        padding-bottom: 80px;
    }
}
.share-bg {
    position: fixed;
    width: 100%;
    left: -9999px;
    top: 0;
    background: url(../assets/images/exam-report-bg.png) no-repeat center center / cover;
    padding: 30px 30px;
    .share-header {
        .logo {
            width: 402px;
            height: 90px;
            background: url(../assets/images/logo.png) no-repeat center center / cover;
            margin: 40px auto 0;
        }
        .logo2 {
            width: 402px;
            height: 90px;
            margin: 40px auto 30px;
        }
        .title {
            width: 560px;
            margin: 30px auto;
            font-size: 32px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: bold;
            height: 68px;
            line-height: 68px;
            text-align: center;
            background: url(../assets/images/exam-report-share-title.png) no-repeat center center / 558px 68px;
        }
    }

    .share-bottom-qita {
        padding: 40px 20px 0 20px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
            font-size: 32px;
            font-weight: bold;
            color: #333333;
            line-height: 58px;
            padding-top: 8px;
            text-align: center;
        }
        .desc {
            width: 436px;
            height: 56px;
            background: url(../assets/images/right_icon2.png) no-repeat 380px center #04a5ff;
            background-size: 42px 42px;
            border-radius: 32px;
            font-size: 28px;
            font-weight: 500;
            text-align: center;
            color: #ffffff;
            line-height: 56px;
            margin-top: 14px;
            padding-right: 40px;
        }
        .erweima {
            width: 176px;
            height: 176px;
            background: url(../assets/images/QRcode.png) no-repeat center;
            background-size: 100%;
            margin-left: 50px;
        }
        .dg {
            width: 176px;
            height: 176px;
            margin-left: 50px;
        }
    }
}
</style>
