<template>
    <div>
        <div
            class="goods-item"
            @click="clickShowcaseGoods"
        >
            <div class="gh">
                <div class="image">
                    <div class="num">{{ '0' + (index + 1) }}</div>
                    <img :src="advertDetail.icon" />
                </div>
                <div class="info">
                    <div>
                        <div class="t">{{ advertDetail.name }}</div>
                        <div class="p">
                            {{ advertDetail.subTitle }}
                        </div>
                        <div class="tags">
                            <div class="tag" v-for="tag in advertDetail.popupBuyTag" :key="tag">{{ tag }}</div>
                        </div>
                    </div>
                    <div class="btn-wrap" v-if="showPrice">
                        <div class="price">
                            ￥<span>{{showPrice}}</span>
                        </div>
                        <div
                            v-if="advertDetail.bought && !advertDetail.upgrade"
                            class="btn btn1"
                            @click.stop="clickShowcaseGoods()"
                        >
                            去使用
                        </div>
                        <div v-else class="btn btn2" @click.stop="openDetainment()">
                            {{
                                advertDetail.upgrade ? '去升级' : '去抢购'
                            }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <payPopup
            :key="advertDetail.icon + 'payPopup'"
            :advertDetail="advertDetail"
            :goodsDetailProp="goodsDetail"
            :motionDetailProp="motionDetail"
            :show.sync="payPopupVisible"
            :isInDialog="true"
            :fragmentName1="fragmentName1"
        />
        <payTypeSelectPupop
            v-if="goodsDetail.groupKey"
            :couponUsable="couponUsable"
            :goodsDetail="goodsDetail"
            :key="advertDetail.icon + 'payTypeSelect'"
            :show.sync="payTypeSelectPupopVisible"
            @buy="
                fragmentName2 => {
                    buy(advertDetail, fragmentName2)
                }
            "
            :price="showPrice"
        />
    </div>
</template>

<script>
import {mapState, mapGetters} from 'vuex'
import {find} from 'lodash-es'
import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    URLParams,
    isIOS,
    toast,
    getAuthToken,
    goLogin,
    trackEvent,
    isMucang,
    isWeixin,
    getOpenid,
    getCityCode,
} from '../utils/tools'
import {webClose} from '../utils/jump'
import goodsMixin from '../utils/goodsMixin'
import payPopup from './payPopup'
import payTypeSelectPupop from './payTypeSelectPupop'
import {getLiveGoodsDetail} from '../server/topLesson'
import {createMobileOrder, payLessonGroup, getPayAfterStrategy} from '../utils/payHelper'

export default {
    mixins: [goodsMixin],
    components: {payPopup, payTypeSelectPupop},
    data() {
        return {
            fragmentName2: '',
            fragmentName1: '主播推荐商品弹窗',
            payPopupVisible: false,
            payTypeSelectPupopVisible: false,
            goodsDetail: {},
            motionDetail: {},
        }
    },
    props: {
        advertDetail: Object,
        index: Number,
    },
    computed: {
        ...mapState(['selectCoupons']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed', 'readed2']),
        couponUsable() {
            return this.selectCoupons[this.advertDetail.goodsKey + '_selectCoupon'] || {}
        },
        showPrice() {
            if (this.advertDetail.advertType === 'vip') {
                let payPrice = this.goodsDetail.price
                let couponPrice = this.couponUsable.priceCent
                if (couponPrice) {
                    return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
                } else {
                    return payPrice
                }
            } else {
                return this.goodsDetail.price
            }
        },
    },
    async mounted() {
        this.goodsDetail = await this.getGoodsItem(this.advertDetail)
        this.motionDetail = await this.getLiveGoodsDetail(this.advertDetail)
    },
    methods: {
        async clickShowcaseGoods() {
            this.popupMotion(this.advertDetail, this.motionDetail, false)
            this.$emit('close')

            let fragmentName1 = '主播推荐商品弹窗'
            let actionType = '点击'
            let actionName = '查看商品详情'

            // 精品课直播间页_主播推荐商品弹窗_点击查看商品详情
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                groupKey: this.advertDetail.goodsKey,
                goodsUniqueKey: this.advertDetail.goodsKey,
            })
        },
        async openDetainment() {
            if (isIOS && !this.goodsDetail.entityGoods) {
                if (this.checkAgreement) {
                    if (this.goodsDetail.goodsType === 'training') {
                        if (!this.readed2) {
                            await this.$confirmProtocol({
                                type: this.goodsDetail.goodsType,
                            })
                        }
                    } else if (!this.readed) {
                        await this.$confirmProtocol()
                    }
                }
                this.buyVipGoods(this.advertDetail)
            } else if (this.goodsDetail.groupKey) {
                this.payTypeSelectPupopVisible = true
            }
        },
        async getLiveGoodsDetail(advertDetail) {
            let resData = await getLiveGoodsDetail({
                goodsId: advertDetail.id,
                sessionId: URLParams.id,
            })
            resData.popupSubTitle = resData.popupSubtitle
            resData.detailUrl = resData.boughtUrl || resData.jumpUrl
            resData.goodsKey = resData.goodsKey || resData.goodsUniqueKey
            return resData
        },
        async buy(item, fragmentName2) {
            this.fragmentName2 = fragmentName2
            if (this.checkAgreement) {
                if (this.goodsDetail.goodsType === 'training') {
                    if (!this.readed2) {
                        await this.$confirmProtocol({
                            type: this.goodsDetail.goodsType,
                        })
                    }
                } else if (!this.readed) {
                    await this.$confirmProtocol()
                }
            }
            this.buyVipGoods(item, true)
        },
        async buyVipGoods(advert, detainment) {
            if (!this.goodsDetail.groupKey) {
                let resData = await this.getGoodsItem(advert, true)
                if (!resData) return
            }

            let fragmentName1 = '主播推荐商品弹窗'
            let fragmentName2 = this.fragmentName2 || undefined
            if (advert.advertType === 'lesson') {
                this.payForLesson(fragmentName1)
            } else {
                this.payForVip(fragmentName1)
            }

            let actionType = '点击'
            let actionName = '确认支付'

            // 精品课直播间页_主播推荐商品弹窗_点击确认支付
            trackEvent({
                fragmentName1,
                fragmentName2,
                actionType,
                actionName,
                groupKey: advert.goodsKey,
                goodsUniqueKey: advert.goodsKey,
            })
        },
        async payForLesson(fragmentName1) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                return
            }
            payLessonGroup(
                {
                    payType,
                    payChannel,
                    lessonGroupId: this.advertDetail.goodsKey,
                    appleId: this.goodsDetail.applePriceId,
                    squirrelGoodsInfo: this.goodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        lessonId: URLParams.id,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 1,
                    pageData: {
                        lessonGroupId: this.advertDetail.goodsKey,
                    },
                },
                getPayAfterStrategy(this.isInDialog, 'lesson')
            )
        },
        async payForVip(fragmentName1) {
            let payType, payChannel
            let wxData = {}
            if (!isMucang) {
                if (isWeixin) {
                    payType = 2
                    payChannel = 'weixin_mobile'
                    wxData = {
                        openId: await getOpenid(),
                        platformType: 'wap',
                        _cityCode: (await getCityCode()).adcode,
                    }
                } else {
                    toast('请在微信浏览器内打开使用')
                    return
                }
            } else if (isIOS && !this.goodsDetail.entityGoods) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            if (this.goodsDetail.goodsType === 'training') {
                const authToken = await getAuthToken()
                if (!authToken) {
                    goLogin({refresh: true})
                    return
                }
            }

            createMobileOrder(
                {
                    sessionIds: this.goodsDetail.sessionIdList.join(','),
                    appleId: this.goodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    entityGoods: this.goodsDetail.entityGoods,
                    goodsType: this.goodsDetail.goodsType,
                    couponCode: this.couponUsable.couponCode,
                    activityType: this.goodsDetail.activityType,
                    groupKey: this.advertDetail.goodsKey,
                    squirrelGoodsInfo: this.goodsDetail.squirrelGoodsInfo,
                    promotionType: this.goodsDetail.promotionActivityData.activityExt,
                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        groupKey: this.advertDetail.goodsKey,
                        lessonId: URLParams.id,
                        liveActivityId: this.goodsDetail.promotionActivityData.activityId,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this.advertDetail.goodsKey,
                    },
                    ...wxData,
                },
                getPayAfterStrategy(this.isInDialog, this.goodsDetail.goodsType),
                (res) => {
                    if (this.goodsDetail.goodsType === 'training') {
                        toast(
                            '确认支付状态中，请勿点击或退出APP',
                            5000,
                            async () => {
                                this.openDetail(this.motionDetail.detailUrl)
                                await new Promise(resolve => {
                                    pageSwitch.onPageShow(resolve)
                                })
                                webClose()
                            },
                            {frozen: true}
                        )
                    }
                }
            )
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.goods-item {
    background: #ffffff;
    border-radius: 20px;
    .num {
        position: absolute;
        left: 0;
        right: 0;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0, 0, 0, 0.6);
        font-size: 20px;
        color: #ffffff;
    }
    .gh {
        display: flex;
        border-bottom: 1px solid #f2f2f2;
        padding: 30px 0;
    }
    .image {
        width: 278px;
        height: 278px;
        overflow: hidden;
        position: relative;
        border-radius: 8px;
        img {
            width: 100%;
        }
    }
    .info {
        margin-left: 40px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 0;
        .t {
            font-size: 28px;
            display: -webkit-box;
            overflow: hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            font-weight: bold;
            color: #000;
        }
        .p {
            font-size: 24px;
            color: #B49379;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 6px;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            height: 50px;
            overflow: hidden;
            margin-top: 6px;
        }
        .tag {
            border-radius: 5px;
            font-size: 22px;
            color: #ff4a40;
            border: 1px solid #ff4a40;
            padding: 4px 8px;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            margin-right: 10px;
            margin-top: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    .btn-wrap {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding-bottom: 10px;
    }
    .price {
        color: #FF2763;
        font-size: 28px;
        line-height: 1;
        span {
            font-size: 40px;
        }
    }
    .btn {
        width: 170px;
        height: 56px;
        font-weight: 500;
        font-size: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 12px;
    }
    .btn1 {
        border: 1px solid #ff2763;
        background: #fff;
        color: #ff2763;
    }
    .btn2 {
        background: linear-gradient(270deg,#fe18a3, #ff285d);
        color: #fff;
        flex-direction: column;
        margin-left: 20px;
        position: relative;
        p {
            font-size: 28px;
        }
        span {
            font-size: 20px;
        }
    }

}
</style>
