<template>
    <div class="lesson-recommend">
        <div class="content scroll_view">
            <div
                v-for="(item, index) in dataList"
                :key="item.icon + 'goods'"
            >
                <goodsItem :advertDetail="item" :index="index" />
            </div>
        </div>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
// import {MCProtocol} from '@simplex/simple-base'
import goodsItem from './goodsItem'

export default {
    components: {goodsItem},
    data() {
        return {
            fragmentName2: '',
        }
    },
    props: {
        dataList: Array,
        isInDialog: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapGetters(['checkAgreement', 'readed']),
    },
    mounted() {
        this.$store.dispatch('checkAgreement')
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.lesson-recommend {
    flex: 1;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content {
        padding: 0 30px 20px;
        overflow-y: auto;
        flex: 1;
    }
}
</style>
