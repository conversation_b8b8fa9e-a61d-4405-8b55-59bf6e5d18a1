<template>
    <popup
        class="help-popup"
        :showClose="false"
        :position="'bottom'"
        @closed="closed"
        :show.sync="visible"
    >
        <div class="content">
            <iframe width="100%" height="100%" :src="url"></iframe>
        </div>
    </popup>
</template>

<script>
import {once} from 'lodash-es'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {webOpen, getUrl, getURLParams, URLParams} from '../utils/tools'
import popup from '../components/dialog'
import {getAskStatus} from '../server/common'

const getAskStatusData = once(getAskStatus)

export default {
    components: {popup},
    data() {
        return {
            visible: false,
            src: `https://laofuzi.kakamobi.com/jkbd-qa/faq.html?qaCode=zbk`,
            // src: `https://laofuzi.kakamobi.com/jkbd-qa/faq.html?key=jk_vip_guessQA_zbk`,
            // src: `http://*************:8080/faq.html?qaCode=zbk`,
        }
    },
    props: {
        show: Boolean,
        fragmentName1: String,
    },
    computed: {
        url() {
            var params = getURLParams(null, window.location.href)
            let url = this.src
            let urlParams = getURLParams(null, url)
            // url上如果没有kemu或者kemuStyle,手动带上直播间的kemu
            if (!(urlParams.kemu || urlParams.kemuStyle) && URLParams.kemuStyle) {
                url = getUrl(url, {kemuStyle: URLParams.kemuNum})
            }
            return getUrl(
                url,
                Object.assign(params, {
                    pageName: stat.getPageName(),
                    fragmentName1: this.fragmentName1,
                })
            )
        },
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    created() {
        getAskStatusData({qaCode: 'zbk'}).then(resdata => {
            if (resdata && resdata.status) {
                this.$emit('setHelpIcon', true)
            }
        })
    },
    mounted() {
        window.addEventListener('message', e => {
            let {type, data} = e.data
            if (type === 'close') {
                this.close()
            } else if (type === 'open') {
                webOpen({
                    url: data.url,
                    titleBar: true,
                    title: data.title,
                })
            }
        })
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            this.$emit('update:show', false)
        },
        goFeedback() {
            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                webOpen({
                    url:
                        'https://feedback.nav.mucang.cn/send-feedback?application=jiakaobaodian&category=zhibokejubao',
                })
            })
        },
    },
}
</script>

<style lang="less" scoped>
.help-popup {
    .content {
        background-color: #fff;
        display: flex;
        border-radius: 20px 20px 0 0;
        overflow: hidden;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
        height: 880px;

        iframe {
            border: 0;
        }
    }
}
</style>
