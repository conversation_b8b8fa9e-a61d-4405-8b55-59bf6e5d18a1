<template>
    <popup
        class="iframe-popup"
        :showClose="false"
        :position="'bottom'"
        @closed="closed"
        :show.sync="visible"
    >
        <div class="content">
            <iframe width="100%" height="100%" :src="url"></iframe>
        </div>
    </popup>
</template>

<script>
import {webOpen, getUrl, getURLParams, URLParams} from '../utils/tools'
import popup from '../components/dialog'

export default {
    components: {popup},
    data() {
        return {
            visible: false,
        }
    },
    props: {
        show: Boolean,
        src: String,
    },
    computed: {
        url() {
            var params = getURLParams(null, window.location.href)
            let url = this.src
            let urlParams = getURLParams(null, url)
            // url上如果没有kemu或者kemuStyle,手动带上直播间的kemu
            if (!(urlParams.kemu || urlParams.kemuStyle) && URLParams.kemuStyle) {
                url = getUrl(url, {kemuStyle: URLParams.kemuNum})
            }
            return getUrl(
                url, params
            )
        },
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    created() {
    },
    mounted() {
        window.addEventListener('message', e => {
            let {type, data} = e.data
            if (type === 'close') {
                this.close()
            } else if (type === 'open') {
                webOpen({
                    url: data.url,
                    titleBar: true,
                    title: data.title,
                })
            }
        })
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            this.$emit('update:show', false)
        },
    },
}
</script>

<style lang="less" scoped>
.iframe-popup {
    .content {
        background-color: #fff;
        display: flex;
        border-radius: 20px 20px 0 0;
        overflow: hidden;
        flex-direction: column;
        justify-content: space-between;
        height: 880px;

        iframe {
            border: 0;
        }
    }
}
</style>
