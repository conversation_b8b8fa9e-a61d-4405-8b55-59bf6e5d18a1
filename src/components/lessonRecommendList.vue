<template>
    <div class="lesson-recommend">
        <div class="kemu-list" v-if="showKemu">
            <div class="item" :class="{active: this.currentKemu === 1}" @click="switchKemu(1)">科目一</div>
            <div class="item" :class="{active: this.currentKemu === 2}" @click="switchKemu(2)">科目二</div>
            <div class="item" :class="{active: this.currentKemu === 3}" @click="switchKemu(3)">科目三</div>
            <div class="item" :class="{active: this.currentKemu === 4}" @click="switchKemu(4)">科目四</div>
        </div>
        <div class="content">
            <div class="inner">
                <div class="scroller" ref="scroller">
                    <template v-if="lessonList.length">
                        <div class="tj-list">
                            <div
                                class="tj-item"
                                @click="goLesson(item)"
                                v-for="item in lessonList"
                                :key="item.id"
                            >
                                <div class="body">
                                    <div class="main-img">
                                        <span class="slg" v-if="item.price && carStyle === 'car'">全科VIP免费</span>
                                        <div v-if="item.cover">
                                            <img :src="item.cover" />
                                        </div>
                                        <div v-else class="seat-img"><img src="https://jiakao-web.mc-cdn.cn/jiakao-web/2024/03/13/18/c17266c9a3684b53a1892b0636e9641b.png" /></div>
                                    </div>
                                    <div class="info">
                                        <div class="title">{{ item.title }}</div>
                                        <div class="shu">
                                            <div class="has" v-if="item.hasPermission">已解锁</div>
                                            <div class="price" v-else-if="item.price">
                                                <span class="uni">¥</span>
                                                <span class="sum">{{ item.price }}</span>
                                            </div>
                                            <div class="has" v-else>限时免费</div>
                                            <div class="go-detail">立即学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <loading v-else-if="loading" bgColor="#666" />
                    <div v-else class="no-data">暂无数据</div>
                </div>
            </div>
        </div>
        <div class="sell-box" @click="openDetainment('好课推荐弹窗')" v-if="showSellBox">
            <div class="t1">{{ config.headerText }}</div>
            <div class="t2">
                <div class="price">
                    ¥&nbsp;<span>{{ showPrice }}</span>
                </div>
                <div class="text">
                    <p class="p1">{{ config.sellTitle }}</p>
                    <p class="p2">{{ config.sellSubTitle }}</p>
                </div>
                <div class="btn">立即解锁</div>
            </div>
        </div>

        <payTypeSelectPupop
            v-if="vipGoodsDetail.groupKey"
            :couponUsable="couponUsable"
            :goodsDetail="vipGoodsDetail"
            key="payTypeSelect"
            :show.sync="payTypeSelectPupopVisible"
            @buy="buy"
            :price="showPrice"
        />
    </div>
</template>

<script>
import {mapGetters, mapState, mapMutations} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {cloneDeep, find} from 'lodash-es'
import {getActiveConfig} from '../server/active'
import {getLessonList} from '../server/topLesson'
import {getBestCoupon} from '../utils/coupon'
import loading from './loading'
import payTypeSelectPupop from '../components/payTypeSelectPupop'
import {
    URLParams,
    formatDate,
    webOpen,
    isIOS,
    // toast,
    trackEvent,
} from '../utils/tools'
import {getSessionInfo} from '../server/goods'
import {createMobileOrder, getPayAfterStrategy} from '../utils/payHelper'

// https://web-resource.mucang.cn/minprogram/fenxiao/<EMAIL>
// https://web-resource.mucang.cn/minprogram/fenxiao/<EMAIL>

export default {
    components: {loading, payTypeSelectPupop},
    data() {
        return {
            carStyle: URLParams.carStyle,
            page: 1,
            lessonList: [],
            nowTime: new Date().getTime(),
            config: {},
            vipGoodsDetail: {},
            payTypeSelectPupopVisible: false,
            currentKemu: 0,
            loading: false,
        }
    },
    props: {
        hktjLessonData: Object,
        isInDialog: {
            type: Boolean,
            default: false,
        },
    },
    created() {
        let lessonList = cloneDeep(this.hktjLessonData.itemList)
        this.lessonList.push(...this.dealList(lessonList))
        this.currentKemu = +URLParams.kemuNum
    },
    mounted() {
        // if (this.hktjLessonData.hasMore) {
        //     this.$refs['scroller'].onscroll = this.onScroll
        // }
        this.$store.dispatch('checkAgreement')
        if (!URLParams.carStyle) {
            return
        }
        let key
        switch (URLParams.kemu) {
            case 'kemu1':
                key = 'lessonRecommendBottomSell_ke1'
                break
            case 'kemu2':
                key = 'lessonRecommendBottomSell_ke2'
                break
            case 'kemu4':
                key = 'lessonRecommendBottomSell_ke4'
                break
        }
        if (key) {
            getActiveConfig({key}).then(data => {
                if (data.value) {
                    let config = JSON.parse(data.value)
                    config = config && config[URLParams.carStyle]
                    this.config = config || {}
                    if (config.groupkey) {
                        this.getGoodsItem(config.groupkey)
                    }
                }
            })
        }
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'selectCoupons']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed']),
        showKemu() {
            return URLParams.sceneCode !== '102'
        },
        couponUsable() {
            return this.selectCoupons[this.config.groupkey + '_selectCoupon'] || {}
        },
        showPrice() {
            let payPrice = this.vipGoodsDetail.price
            let couponPrice = this.couponUsable.priceCent
            if (couponPrice) {
                return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
            } else {
                return payPrice
            }
        },
        showSellBox() {
            return (
                this.config.isOpen &&
                this.config.groupkey &&
                this.vipGoodsDetail.groupKey &&
                (!this.vipGoodsDetail.bought || this.vipGoodsDetail.upgrade)
            )
        },
    },
    methods: {
        ...mapMutations([
            'updateSelectCoupons',
        ]),
        switchKemu(kemu) {
            if (this.currentKemu === kemu) return
            this.currentKemu = kemu
            this.lessonList = []
            this.getLessonList()
        },
        async getLessonList() {
            this.loading = true
            const resData = await getLessonList({
                kemu: this.currentKemu,
                tagKey: 'HKTJ',
                page: 1,
                limit: 1000,
                carType: URLParams.carStyle,
            })
            this.loading = false
            this.lessonList.push(...this.dealList(resData.itemList))
            if (resData.hasMore) {
                this.$refs['scroller'].onscroll = this.onScroll
            }
        },
        dealList(lessonList) {
            let dataList = cloneDeep(lessonList)
            dataList.forEach(item => {
                let startTime = formatDate(item.liveBeginTime, 'MM.dd')
                let endTime = formatDate(item.liveEndTime, 'MM.dd')
                item.showTime = startTime + '-' + endTime
            })
            return dataList
        },
        async getGoodsItem(groupKey) {
            let resData
            resData = await getSessionInfo({
                tiku: URLParams.carStyle,
                groupKey: groupKey,
                promotionActivityId: this.bizConfig.playStatus === 1 && this.roomDetail.promotionActivityId,
            })
            this.getCoupon(resData)
            this.vipGoodsDetail = resData
            return resData
        },
        async getCoupon(goodsDetail) {
            let coupon = await getBestCoupon(goodsDetail)

            if (coupon.couponCode) {
                this.updateSelectCoupons({[goodsDetail.groupKey + '_selectCoupon']: coupon})
            }
        },
        goLesson(item) {
            let url
            if (item.subLessonCount === 1) {
                url = `http://jiakao.nav.mucang.cn/topLesson/play?from=202&id=${item.firstSubLessonId}&fromItemCode=${URLParams.id}`
            } else {
                url = `http://jiakao.nav.mucang.cn/topLesson/detail?from=202&id=${item.id}&fromItemCode=${URLParams.id}`
            }
            webOpen({
                url,
            })

            let fragmentName1 = '好课推荐弹窗'
            let actionName = '立即学习'
            let actionType = '点击'

            // 埋点梳理-驾考宝典-1129
            // 精品课直播间页_好课推荐弹窗_点击立即学习
            trackEvent({fragmentName1, actionName, actionType})
        },
        onScroll(e) {
            let {scrollTop, scrollHeight, clientHeight} = e.srcElement
            if (scrollHeight < clientHeight + scrollTop + 100) {
                ++this.page
                this.getLessonList()
                this.$refs['scroller'].onscroll = null
            }
        },
        async buy(fragmentName1) {
            if (this.checkAgreement && !this.readed) {
                await this.$confirmProtocol()
            }
            this.buyVip(fragmentName1, true)
        },
        async openDetainment(fragmentName1) {
            if (isIOS) {
                if (this.checkAgreement && !this.readed) {
                    await this.$confirmProtocol()
                }
                this.buyVip(fragmentName1)
            } else {
                this.payTypeSelectPupopVisible = true
            }
        },
        buyVip(fragmentName1, detainment) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else if (detainment) {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            } else {
                let pay = find(this.payList, {type: 2})
                if (pay) {
                    payType = 2
                    payChannel = 'weixin_mobile'
                } else {
                    payType = 1
                    payChannel = 'alipay_mobile'
                }
            }
            createMobileOrder(
                {
                    sessionIds: this.vipGoodsDetail.sessionIdList.join(','),
                    appleId: this.vipGoodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    couponCode: this.couponUsable.couponCode,
                    activityType: this.vipGoodsDetail.activityType,
                    groupKey: this.vipGoodsDetail.groupKey,
                    squirrelGoodsInfo: this.vipGoodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        groupKey: this.vipGoodsDetail.groupKey,
                        lessonId: URLParams.id,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this.vipGoodsDetail.groupKey,
                    },
                },
                getPayAfterStrategy(this.isInDialog, 'vip')
            )

            fragmentName1 = fragmentName1 || '好课推荐弹窗'
            let actionType = '点击'
            let actionName = '去支付'

            // 精品课直播间页_好课推荐弹窗_点击去支付
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                payPathType: 0,
                groupKey: this.vipGoodsDetail.groupKey,
            })
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.lesson-recommend {
    flex: 1;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .kemu-list {
        display: flex;
        padding: 10px 0 14px 30px;
        .item {
            margin-right: 16px;
            width: 138px;
            height: 58px;
            background: rgba(4,165,255,0.10);
            border-radius: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 26px;
            background: #f9f9f9;
            color: #6e6e6e;
            &.active {
                background: rgba(4,165,255,0.10);
                color: #04A5FF;
            }
        }
    }
    .content {
        height: 0;
        flex: 1;
        position: relative;
        margin: 0 21px;
    }
    .inner {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
    .scroller {
        height: 100%;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 30px;
    }
    .tj-list {
        display: flex;
        flex-wrap: wrap;
    }
    .tj-item {
        width: 336px;
        margin: 16px 9px 0;
        border-radius: 16px;
        overflow: hidden;
        background: #fff;
        padding-bottom: 16px;
        .body {
            .main-img {
                width: 100%;
                height: 190px;
                position: relative;
                overflow: hidden;
            }
            .seat-img {
                background-color: #E8ECEE;
                height: 190px;
                display: flex;
                justify-content: center;
                align-items: center;
                img {
                    width: 156px;
                }
            }
            .slg {
                position: absolute;
                right: 0;
                top: 0;
                width: 136px;
                height: 36px;
                background: url(../assets/images/<EMAIL>) no-repeat;
                background-size: 136px auto;
                line-height: 36px;
                color: #704100;
                font-size: 20px;
                text-align: center;
            }
            .info {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .title {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    .fontSizeWithElder(28px);
                    padding: 4px 0 4px 20px;
                }
            }
            .shu {
                padding: 0 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .has {
                .fontSizeWithElder(26px);
                display: flex;
                justify-content: center;
                align-items: center;
                color: #a8a8a8;
            }
            .price {
                display: flex;
                justify-content: center;
                align-items: center;
                .uni {
                    color: #ff4a40;
                    .fontSizeWithElder(22px);
                }
                .sum {
                    color: #ff4a40;
                    .fontSizeWithElder(32px);
                }
            }
            .go-detail {
                width: 136px;
                height: 42px;
                font-size: 24px;
                border-radius: 24px;
                color: #fff;
                background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
    .no-data {
        background: url(../assets/images/no-data.png) no-repeat center 40px;
        background-size: 440px 334px;
        font-size: 28px;
        text-align: center;
        padding-top: 402px;
        padding-bottom: 80px;
    }
    .sell-box {
        width: 100%;
        .t1 {
            background: #fff1e1;
            height: 60px;
            line-height: 60px;
            text-align: center;
            .fontSizeWithElder(28px);
            color: #ff4b42;
        }
        .t2 {
            background: linear-gradient(315deg, #fa3c32 0%, #fd5c54 100%);
            height: 102px;
            display: flex;
            align-items: center;
            padding: 0 30px 0 36px;
            color: #fff;
            box-sizing: content-box;
            padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
            padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
        }
        .price {
            .fontSizeWithElder(32px);
            span {
                .fontSizeWithElder(52px);
            }
        }
        .text {
            flex: 1;
            padding-left: 20px;
        }
        .btn {
            background: url(../assets/images/bottom-sell-btn.png) no-repeat;
            background-size: auto 100%;
            width: 186px;
            height: 64px;
            color: #843f01;
            .fontSizeWithElder(30px);
            text-align: center;
            line-height: 64px;
            font-weight: bold;
        }
        .p1 {
            font-size: 32px;
        }
        .p2 {
            .fontSizeWithElder(24px);
            opacity: 0.8;
        }
    }
}
</style>
