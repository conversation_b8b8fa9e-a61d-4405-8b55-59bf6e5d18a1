<template>
    <div class="motion">
        <template v-if="isVip">
            <div style="position: relative;">
                <img width="100%" :src="advertDetail.img" />
                <div class="mk" v-if="remainSotckInfo.sellRateNum <= 0 && canBuy">
                    已售罄
                </div>
            </div>
            <div class="sale-rate" v-if="sellRateEnable">
                <div class="text">{{ sellRateText }}:</div>
                <div class="line">
                    <div
                        class="bg float"
                        v-if="remainSotckInfo.sellRateNum > 0"
                        :style="{width: remainSotckInfo.sellRateNum + '%'}"
                    ></div>
                    <div class="num" :style="numStyle">
                        <template v-if="remainSotckInfo.sellRateNum > 0"
                            >{{ remainSotckInfo.sellRateNum }}%</template
                        >
                        <template v-else>已售罄</template>
                    </div>
                </div>
            </div>
            <div class="btn vip" :class="{ani: canBuy}" v-if="vipBtnText">
                {{ vipBtnText }}
            </div>
        </template>
        <template v-else>
            <img width="100%" :src="advertDetail.img" />
        </template>
    </div>
</template>

<script>
export default {
    data() {
        return {}
    },
    props: ['advertDetail', 'remainSotckInfo'],
    computed: {
        canBuy() {
            // 在ios端，当一个元素处于animation动画的状态下，
            // dom重排操作+修改动画元素的文字，修改文字操作大概率无法生效
            // 猜测由于dom重排操作会阻塞页面，导致修改动画元素被忽略！
            return !this.advertDetail.bought || this.advertDetail.upgrade
        },
        sellRateEnable() {
            return (
                this.remainSotckInfo.sellRateEnable &&
                this.remainSotckInfo.applyScene.indexOf('1') > -1 &&
                this.canBuy
            )
        },
        sellRateText() {
            return '优惠名额' + (this.remainSotckInfo.sellRateNum > 50 ? '剩余' : '仅剩')
        },
        numStyle() {
            // 40-30
            let up = 40
            let down = 30
            let left, color, transform
            if (this.remainSotckInfo.sellRateNum < up) {
                if (this.remainSotckInfo.sellRateNum > down) {
                    left = 50 + up - down
                } else {
                    left = 50
                }
                left = left + '%'
                transform = 'translateX(-50%)'
                color = '#ffbcb9'
            }
            return {
                color,
                transform,
                left,
            }
        },
        isVip() {
            return this.advertDetail.advertType === 'vip' || this.advertDetail.advertType === 'lesson' || this.advertDetail.advertType === 'training'
        },
        vipBtnText() {
            if (this.advertDetail.advertType === 'lesson') {
                if (this.advertDetail.bought) {
                    return '立即学习'
                } else {
                    return '立即购买'
                }
            } else if (this.advertDetail.advertType === 'training') {
                if (this.advertDetail.bought) {
                    return '立即上课'
                } else {
                    return '立即购买'
                }
            } else {
                if (this.advertDetail.bought) {
                    if (this.advertDetail.upgrade) {
                        return '立即升级'
                    } else {
                        return '去使用'
                    }
                } else {
                    return '立即购买'
                }
            }
        },
    },
    methods: {
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
@keyframes mymove {
    0% {
        transform: scale(0.9);
    }
    40% {
        transform: scale(1);
    }
    60% {
        transform: scale(0.98);
    }
    100% {
        transform: scale(0.9);
    }
}
.motion {
    // width: 212px;
    // min-height: 1px;
    // position: relative;
    // text-align: center;
    margin-top: 20px;
    img {
        width: 100%;
        position: relative;
        z-index: 1;
    }
    .mk {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background-color: rgba(0,0,0,0.6);
        z-index: 2;
        color: #fff;
    }
    .btn {
        margin: -29px auto 0;
        color: #fff;
        text-align: center;
        .fontSizeWithElder(28px);
        width: 212px;
        height: 58px;
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &.group {
            background: url(../assets/images/<EMAIL>) no-repeat;
            background-size: cover;
        }
        &.vip {
            background: url(../assets/images/<EMAIL>) no-repeat;
            background-size: cover;
            width: 212px;
            height: 56px;
            padding-bottom: 4px;
        }
        &.ani {
            transform: translateZ(0);
            animation: mymove 0.9s infinite;
            animation-timing-function: ease-out;
        }
    }

    .sale-rate {
        width: 100%;
        height: 104px;
        margin: 0 auto;
        text-align: left;
        padding: 6px 18px 0 12px;
        background: #ec212d;
        .text {
            .fontSizeWithElder(20px);
            color: #fff;
        }
        .line {
            margin-top: 6px;
            height: 24px;
            background: #ec212d;
            background: linear-gradient(180deg, #a50000 0%, #c50000 100%);
            border-radius: 8px;
            position: relative;
        }
        .num {
            position: absolute;
            left: 0;
            top: 0;
            color: #5a0000;
            padding-left: 8px;
            height: 24px;
            display: flex;
            align-items: center;
            .fontSizeWithElder(20px);
            &.sellout {
                color: #ffbcb9;
            }
        }
        .bg {
            height: 24px;
            background: url(../assets/images/progress2.gif) no-repeat;
            background-size: 180px 24px;
            border-radius: 8px;
            position: relative;
            transition: width 1.5s;
            &.float::after {
                content: '';
                position: absolute;
                right: 0;
                transform: translateX(50%);
                top: -2px;
                width: 27px;
                height: 32px;
                background: url(../assets/images/float-icon2.png) no-repeat;
                background-size: cover;
            }
        }
    }
    .timer {
        display: inline-block;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 14px;
        color: #fff;
        line-height: 1.2;
        margin: 0 auto;
        padding: 6px 16px;
        .fontSizeWithElder(18px);
    }
}

.landscape /deep/ {
    // landscape
    .motion {
        margin-top: 0;
        position: absolute;
        top: 16px;
        right: 16px;
        transform: scale(0.9);
    }
}
</style>
