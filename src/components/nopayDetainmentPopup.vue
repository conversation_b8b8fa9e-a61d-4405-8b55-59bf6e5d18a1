<template>
    <popup
        class="nopay-detainment-popup"
        :position="'bottom'"
        :show.sync="visible"
        :closeOnBlank="false"
        @closed="closed">
        <div class="wrap">
            <div class="img"><img :src="config.img"></div>
            <div class="btn-wrap">
                <div class="btn" @click="goUse"><img src="../assets/images/icon-wx.png">立即添加</div>
            </div>
        </div>
    </popup>
</template>

<script>
import {webOpen, trackEvent} from '../utils/tools'
import popup from './dialog'

export default {
    components: {
        popup,
    },
    data() {
        return {
            visible: false,
            timer: null,
            timeing: 0,
        }
    },
    props: {
        show: Boolean,
        config: Object,
    },
    watch: {
        async show(val) {
            this.visible = val
            if (!val) {
                this.stopTimer()
            }
        },
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            this.$emit('update:show', false)
        },
        popup() {
            this.$emit('update:show', true)
            if (this.config.time > 0) {
                this.startTimer(this.config.time)
            }

            let fragmentName1 = '私教支付挽留弹窗'
            let actionType = '出现'

            trackEvent({fragmentName1, actionType})
        },
        goUse() {
            webOpen({
                url: this.config.link,
            })

            let fragmentName1 = '私教支付挽留弹窗'
            let actionType = '点击'
            let actionName = '添加企微'

            trackEvent({fragmentName1, actionType, actionName})
        },
        startTimer(seconds = 0) {
            let date = +new Date()
            let end = date + seconds * 1000
            this.timer = setInterval(() => {
                date = +new Date()
                let remainder = end - date
                this.timeing = Math.max(Math.ceil(remainder / 1000), 0)
                if (remainder <= 0) {
                    this.close()
                }
            }, 16)
        },
        stopTimer() {
            clearInterval(this.timer)
            this.timer = null
            this.timeing = 0
        },
    },
}
</script>

<style lang="less" scoped>
.nopay-detainment-popup {
    /deep/ .dialog .close {
        right: initial;
        left: 14px;
    }
    /deep/ .dialog {
        // height: 516px;
        background: linear-gradient(148deg,#f0fafd 15%, #ceebfe 69%, #e8f7fd);
        border-radius: 32px 32px 0px 0px;
    }

    /deep/ .dialog {
        max-width: 7.5rem;
        transform: translateX(-50%);
        left: 50% !important;
    }

    .wrap {
        overflow: hidden;
        height: 100%;
        position: relative;
    }
    .btn-wrap {
        height: 200px;
    }
    .btn {
        margin: 0 auto;
        width: 690px;
        height: 104px;
        border-radius: 104px;
        background: linear-gradient(135deg,#14c1fb, #05afff);
        font-size: 36px;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
            width: 48px;
            margin-right: 20px;
        }
    }
}
</style>
