<template>
    <div v-show="visible">
        <div class="mask" v-if="frozen"></div>
        <div class="bread" style="width: 50px; height:50px;">
            <loading bgColor="#cbcbcb" />
        </div>
    </div>
</template>

<script>
import loading from './loading'

export default {
    components: {loading},
    data() {
        return {
            visible: false,
            frozen: false,
        }
    },
    mounted() {},
}
</script>

<style lang="less" scoped>
.mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9998;
}
.bread {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 12px;
}
</style>
