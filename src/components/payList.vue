<template>
    <div class="pay-list" :class="[direction, theme]">
        <div
            :class="[item.type === checkdPayType ? 'checked' : '', 'type'+item.type]"
            class="item"
            @click="checkPayType(item.type)"
            v-for="item in payList.filter(item => item.type !== 100 || showPayForOther)"
            :key="item.name"
        >
            {{ item.name }}
        </div>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {checkPayChannels} from '../utils/tools'

const getPayList = async () => {
    let payList = [
        {
            channelName: 'alipay_mobile',
            type: 1,
            name: '支付宝支付',
        },
        {
            channelName: '',
            type: 100,
            name: '找人代付',
        },
    ]
    let data = await checkPayChannels()
    if (data && data.wx) {
        payList.unshift({
            channelName: 'weixin_mobile',
            type: 2,
            name: '微信支付',
        })
    }
    return payList
}

export default {
    props: {
        direction: {
            type: String,
            default: 'hor',
        },
        theme: {
            type: String,
            default: 'hor1',
        },
        showPayForOther: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapGetters(['payList', 'checkdPayType']),
    },
    async created() {
        if (this.payList.length === 0) {
            this.$store.commit('updatePayConfig', {payList: await getPayList()})
            this.$store.commit('updatePayConfig', {checkdPayType: this.payList[0].type})
        }
    },

    methods: {
        checkPayType(type) {
            this.$store.commit('updatePayConfig', {checkdPayType: type})
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.pay-list.hor {
    display: flex;
    font-size: 28px;
    color: #333;
    &.hor1 {
        .item {
            margin-right: 10px;
            padding: 10px 10px 10px 72px;
            background: url(../assets/images/<EMAIL>) no-repeat 20px center;
            background-size: 36px auto;

            &.checked {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
    }
    &.hor2 {
        .item {
            margin-right: 10px;
            padding: 0 10px 0 72px;
            background: url(../assets/images/<EMAIL>) no-repeat 20px center;
            background-size: 36px auto;

            &.checked {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
    }
    &.hor3 {
        .item {
            margin-right: 10px;
            padding: 0 10px 0 72px;
            background: url(../assets/images/<EMAIL>) no-repeat 20px center;
            background-size: 36px auto;

            &.checked {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
    }
}

.pay-list.vrt {
    font-size: 30px;
    color: #333;
    .item {
        padding: 26px 0 26px 60px;
        border-bottom: 1px solid #e8e8e8;
        position: relative;
        &::before {
            position: absolute;
            content: '';
            left: 0;
            top: 25px;
            width: 52px;
            height: 52px;
        }
        &.type1 {
            &::before {
                background: url(../assets/images/<EMAIL>) no-repeat;
                background-size: 52px auto;
            }
        }
        &.type2 {
            &::before {
                background: url(../assets/images/<EMAIL>) no-repeat;
                background-size: 52px auto;
            }
        }
        &.type100 {
            &::before {
                background: url(../assets/images/icon-friend-pay.png) no-repeat center top;
                background-size: 48px 48px;
            }
        }
    }
    &.vrt1 {
        .item {
            background: url(../assets/images/<EMAIL>) no-repeat right center;
            background-size: 40px auto;

            &.checked {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
    }
    &.vrt2 {
        .item {
            background: url(../assets/images/<EMAIL>) no-repeat right 50px center;
            background-size: 40px auto;
            padding-left: 98px;
            &::before {
                left: 38px;
            }
            &.checked {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
    }
}
</style>
