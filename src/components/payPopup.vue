<template>
    <div>
        <popup
            class="pay-popup"
            @closed="closed"
            :position="'bottom'"
            :show.sync="visible"
            :isInDialog="isInDialog"
        >
            <div
                class="wrap"
                @touchmove="touchmove"
                @touchstart="touchstart"
                @touchend="touchend"
                :style="{height: !isInDialog ? height + 'px' : ''}"
                :class="{full: isTouchLock || isNoExpand}"
            >
                <template v-if="advertDetail.advertType === 'training'">
                    <div class="nogap-content scroll_view">
                        <div class="head-bg">
                            <div :class="trainingInfo.trial ? 'experince-bg' : 'standard-bg'"></div>
                        </div>
                        <div class="content-box">
                            <div class="title">课程安排</div>
                            <div class="dec-title">学员：{{trainingInfo.customerName}}</div>
                            <div class="content-bg"></div>
                            <div class="course-list">
                                <div class="course-item" v-if="trainingInfo.lessonScheduleList && trainingInfo.lessonScheduleList.length > 0">
                                    <div class="course-title">考试题目考点技巧讲解（ {{trainingInfo.lessonScheduleList.length}}节）</div>
                                    <div class="course-info-list">
                                        <div class="course-info-item">
                                            课前老师根据模考情况分析 <span class="black">你的弱项考点</span>
                                        </div>
                                    </div>

                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>讲次</th>
                                                <th>
                                                    <span>考点内容</span>
                                                    <img src="http://exam-room.mc-cdn.cn/exam-room/2024/08/23/11/39de755a81a24ee58ba087375e90b1bf.png"
                                                    class="tag" alt="">
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(item, index) in trainingInfo.lessonScheduleList" :key="item.lessonIndex">
                                                <td>第{{index + 1}}讲</td>
                                                <td>弱项考点：{{realTxt(item.lessonTitle)}}</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <div class="price-box">
                                        <div class="price">
                                            <span class="unit">￥</span>{{trainingInfo.skillExplainServicePrice}}</div>
                                        <div class="price-bg"></div>
                                    </div>
                                </div>
                                <div class="item" v-if="goodsDetail.tips" v-html="goodsDetail.tips.introduction"></div>
                            </div>
                        </div>
                        <div class="limit-send">
                            <img src="http://exam-room.mc-cdn.cn/exam-room/2024/08/23/11/db617498393c4429ab461e9c8510771e.png" alt="" width="100%">
                        </div>
                    </div>
                    <div class="nogap-footer">
                        <div class="pay-type-wrap">
                            <payList direction="hor" theme="hor1" v-if="isMucang" />
                        </div>
                        <div class="pt-sol"></div>
                        <div class="buy-vip" @click="buyVipGoods()">
                            <div class="kh">
                                <div class="price">
                                    ¥<span>{{ showPrice }}</span>
                                </div>
                                <div class="hf" v-if="goodsDetail.suggestedPrice && goodsDetail.suggestedPrice > goodsDetail.price">
                                    <div class="discount">
                                        直降{{ goodsDetail.suggestedPrice - goodsDetail.price }}元
                                    </div>
                                    <div class="del-price">
                                        原价{{ goodsDetail.suggestedPrice }}
                                    </div>
                                </div>
                            </div>
                            <div class="text">
                                立即支付
                            </div>
                        </div>
                        <div class="line between">
                            <vipAgreement theme="blue" :type="goodsDetail.goodsType"></vipAgreement>
                            <couponItem :couponUsable="couponUsable" :goodsDetail="goodsDetail" :entityGoods="goodsDetail.entityGoods" v-if="isMucang" />
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="header">
                        <div class="line">
                            <span class="title"
                                >{{ advertDetail.upgrade ? '升级 ' : ''
                                }}{{ advertDetail.popupTitle }}</span
                            >
                            <span class="mark" v-if="advertDetail.popupTag">{{
                                advertDetail.popupTag
                            }}</span>
                        </div>
                        <div class="line between">
                            <div class="desc">{{ advertDetail.popupSubTitle }}</div>
                        </div>
                    </div>
                    <div class="ifram-container" v-if="motionDetail.sellPageUrl">
                        <iframe
                            width="100%"
                            height="100%"
                            ref="iframe"
                            :src="assembleUrl(motionDetail.sellPageUrl)"
                        ></iframe>
                        <div
                            v-if="!(isTouchLock || isNoExpand)"
                            class="touch"
                            @touchmove="touchmove"
                            @touchstart="touchstart"
                            @touchend="touchend"
                        ></div>
                    </div>
                    <div class="content scroll_view" v-else>
                        <div class="legal-image" v-html="motionDetail.introduction"></div>
                    </div>
                    <div class="footer">
                        <payList direction="hor" theme="hor1" :showPayForOther="true" v-if="(isAndroid || goodsDetail.entityGoods) && isMucang" />
                        <div class="line around">
                            <template v-if="isMucang">
                                <span @click="openHelp" class="item icon icon_kefu">客服</span>
                            </template>
                            <div class="buy-vip" @click="buyVipGoods()">
                                <div class="kh">
                                    <div class="price">
                                        ¥<span>{{ showPrice }}</span>
                                    </div>
                                    <div class="hf" v-if="showDiscountInfo">
                                        <div class="del-price">
                                            日常价¥{{ goodsDetail.originalPrice }}元
                                        </div>
                                        <div class="discount" v-if="goodsDetail.discountSum">
                                            限时直降{{ goodsDetail.discountSum }}元
                                        </div>
                                    </div>
                                </div>
                                <div class="text">
                                    {{ advertDetail.upgrade ? '立即升级' : '立即开通' }}
                                </div>

                                <div class="nopay-time" v-if="nopayExpiredTime > 0 && nopayTimer">
                                    剩<span>{{ noplayRemainderText }}</span>自动取消订单
                                </div>
                                <div class="sale-rate" v-else-if="sellRateEnable">
                                    <div class="sale-text">{{ sellRateText }}:</div>
                                    <div class="sale-line">
                                        <div
                                            class="bg float"
                                            v-if="remainSotckInfo.sellRateNum > 0"
                                            :style="{width: remainSotckInfo.sellRateNum + '%'}"
                                        ></div>
                                        <div class="num" :style="numStyle">
                                            <template v-if="remainSotckInfo.sellRateNum > 0"
                                                >{{ remainSotckInfo.sellRateNum }}%</template
                                            >
                                            <template v-else>已售罄</template>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="time"
                                    v-else-if="showDiscountInfo && remainderText !== '00:00:00'"
                                >
                                    限时立减<span>{{ remainderText }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="line between">
                            <vipAgreement theme="red" :type="goodsDetail.goodsType"></vipAgreement>
                            <couponItem :couponUsable="couponUsable" :goodsDetail="goodsDetail" :entityGoods="goodsDetail.entityGoods" v-if="isMucang" />
                        </div>
                    </div>
                </template>
            </div>
        </popup>

        <helpPop :show.sync="helpPopupVisible" :fragmentName1="helpFragmentName1" @setHelpIcon="(val) => isShowHelpIcon = val" />
        <detainmentPupop
            :show.sync="detainmentPupopVisible"
            @buy="buy"
            :isInDialog="isInDialog"
            :title="advertDetail.popupTitle"
            :price="showPrice"
            :groupKey="detainmentGroupkey"
            :goodsUniqueKey="advertDetail.goodsKey"
        />
        <nopayDetainmentPopup :show.sync="nopayDetainmentPupopVisible" :config="payDetainmentConfig" ref="nopayDetainmentPopupRef" />
    </div>
</template>

<script>
import {mapState, mapGetters} from 'vuex'
import {MCProtocol} from '@simplex/simple-base'
import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {find} from 'lodash-es'
import {
    isIOS,
    getAuthToken,
    formatRemainTime,
    URLParams,
    isAndroid,
    trackEvent,
    toast,
    getUrl,
    webOpen,
    compareVersionComplex,
    getURLParams,
    goLogin,
    isMucang,
    isWeixin,
    getOpenid,
    getCityCode,
    openNewVip,
} from '../utils/tools'
import {reload, webClose} from '../utils/jump'
import {createMobileOrder, payLessonGroup, getPayAfterStrategy, getNopayExpiredTime} from '../utils/payHelper'
import {getTrainingQuoteDetail} from '../server/training'
import {getPayDetainmentConfig} from '../server/common'
import {helpURL, replaceInviteUrl} from '../utils/constant'
import helpPop from './helpPop'
import detainmentPupop from './detainmentPupop'
import nopayDetainmentPopup from './nopayDetainmentPopup'
import goodsMixin from '../utils/goodsMixin'
import touchMixin from '../utils/touchMixin'
import popup from '../components/dialog'
import payList from '../components/payList'
import vipAgreement from '../components/vipAgreement'
import couponItem from '../components/couponItem'

MCProtocol.register('Vip.enableHalfScroll', function(config) {
    return config
})

export default {
    mixins: [goodsMixin, touchMixin],
    components: {
        helpPop,
        detainmentPupop,
        nopayDetainmentPopup,
        popup,
        payList,
        vipAgreement,
        couponItem,
    },
    data() {
        return {
            isMucang,
            visible: false,
            isIOS,
            remainderText: '00:00:00',
            isAndroid: isAndroid,
            detainmentPupopVisible: false,
            nopayDetainmentPupopVisible: false,
            goodsDetailData: {},
            motionDetailData: {},
            nopayExpiredTime: 0,
            nopayTimer: null,
            noplayRemainderText: '00:00:00',
            isShowHelpIcon: false,
            helpFragmentName1: '',
            helpPopupVisible: false,
            trainingInfo: {},
            payDetainmentConfig: {},
        }
    },
    computed: {
        ...mapState(['selectCoupons', 'redPacketActive', 'bizConfig']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed', 'readed2']),
        isNoExpand() {
            return this.isInDialog
        },
        couponUsable() {
            return this.selectCoupons[this.advertDetail.goodsKey + '_selectCoupon'] || {}
        },
        showPrice() {
            if (this.advertDetail.advertType === 'vip' || this.advertDetail.advertType === 'training') {
                let payPrice = this.goodsDetail.price
                let couponPrice = this.couponUsable.priceCent
                if (couponPrice) {
                    return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
                } else {
                    return payPrice
                }
            } else {
                return this.goodsDetail.price
            }
        },
        sellRateEnable() {
            return (
                this.remainSotckInfo &&
                this.remainSotckInfo.sellRateEnable &&
                this.remainSotckInfo.applyScene.indexOf('2') > -1
            )
        },
        sellRateText() {
            return '优惠名额' + (this.remainSotckInfo.sellRateNum > 50 ? '剩余' : '仅剩')
        },
        numStyle() {
            // 44-30
            let up = 44
            let down = 30
            let left, color, transform
            if (this.remainSotckInfo.sellRateNum < up) {
                if (this.remainSotckInfo.sellRateNum > down) {
                    left = 50 + up - down
                } else {
                    left = 50
                }
                left = left + '%'
                transform = 'translateX(-50%)'
                color = '#ffbcb9'
            }
            return {
                color,
                transform,
                left,
            }
        },
        detainmentGroupkey() {
            return this.advertDetail.goodsKey
        },
        showDiscountInfo() {
            // 少于24小时
            let remainder = this.goodsDetail?.discountInfo?.discountEndTime - +new Date()
            return (
                remainder - 24 * 60 * 60 * 1000 < 0 &&
                this.goodsDetail.discountInfo &&
                this.goodsDetail.discountInfo.discountStatus === 2
            )
        },
        motionDetail() {
            if (this.motionDetailProp?.goodsKey) {
                return this.motionDetailProp
            } else if (this.motionDetailData?.goodsKey) {
                return this.motionDetailData
            } else {
                return {}
            }
        },
        goodsDetail() {
            if (this.goodsDetailProp?.groupKey) {
                return this.goodsDetailProp
            } else if (this.goodsDetailData?.groupKey) {
                return this.goodsDetailData
            } else {
                return {}
            }
        },
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                // if (this.redPacketActive.firstTime) {
                //     this.redPacketActive.firstTime = false
                //     let targetPrice = this.vipGoodsDetail.price
                //     let duration = 1500
                //     let speed = duration / this.redPacketActive.redPacketPrice
                //     this.vipGoodsDetail.price = this.redPacketActive.originalPrice
                //     let timer = setInterval(() => {
                //         this.$store.commit('updateDefaultVipGoodsDetail', {
                //             targetPrice: targetPrice,
                //             price: this.vipGoodsDetail.price - 1,
                //         })
                //         if (this.vipGoodsDetail.price <= targetPrice) {
                //             this.$store.commit('updateDefaultVipGoodsDetail', {
                //                 price: targetPrice,
                //                 targetPrice: 0,
                //             })
                //             clearInterval(timer)
                //         }
                //     }, speed)
                // }
                const v1 = async () => {
                    if (!this.goodsDetail.groupKey) {
                        if (this.isInDialog) {
                            this.goodsDetailData = await this.getGoodsEntry(this.advertDetail)
                        } else {
                            this.goodsDetailData = await this.getGoodsItem(this.advertDetail)
                        }
                    }
                }
                const v2 = async () => {
                    if (!this.motionDetail.goodsKey) {
                        this.motionDetailData = await this.getAdvertDetail(this.advertDetail)
                    }
                }
                const v3 = async () => {
                    if (this.advertDetail.advertType === 'training' && !this.trainingInfo.customerName) {
                        this.trainingInfo = await getTrainingQuoteDetail({
                            channelCode: this.advertDetail.goodsKey,
                        })
                    }
                }
                v1()
                v2()
                v3()

                if (this.fullScreen) {
                    this.setFull()
                }

                this.getTime()
            }
        },
        isTouchLock() {
            this.$EventBus.$emit('minimizePlayer', this.isTouchLock, {
                groupKey: this.detainmentGroupkey,
                goodsUniqueKey: this.advertDetail.goodsKey,
            })
        },
        'goodsDetail.discountInfo': {
            handler() {
                if (this.showDiscountInfo) {
                    let originalPrice, discountSum
                    if (isAndroid) {
                        originalPrice = this.goodsDetail?.discountInfo?.preDiscountPrice
                    } else {
                        originalPrice = this.goodsDetail?.discountInfo?.preDiscountApplePrice
                    }
                    setTimeout(() => {
                        discountSum =
                            originalPrice -
                            (this.goodsDetail.targetPrice || this.goodsDetail.price)
                        this.goodsDetail.originalPrice = originalPrice
                        this.goodsDetail.discountSum = discountSum

                        let endTime = this.goodsDetail?.discountInfo?.discountEndTime
                        if (endTime) {
                            this.startDiscountTimer(endTime)
                        }
                    }, 16)
                }
            },
            immediate: true,
        },
    },
    props: {
        advertDetail: Object,
        motionDetailProp: {
            type: Object,
            default: () => {
                return {}
            },
        },
        goodsDetailProp: {
            type: Object,
            default: () => {
                return {}
            },
        },
        remainSotckInfo: Object,
        show: Boolean,
        fragmentName1: String,
        fullScreen: Boolean,
        isInDialog: {
            type: Boolean,
            default: false,
        },
    },
    async created() {
        this.$EventBus.$on('quitMinimize', () => {
            this.close(false)
        })
    },
    async mounted() {
        window.addEventListener('message', e => {
            if (!this.$refs['iframe']) return
            if (e.source !== this.$refs['iframe'].contentWindow) return
            switch (e.data.type) {
                case 'pay':
                    this.buyVipGoods()
                    break
                default:
                    break
            }
        })
        getPayDetainmentConfig().then(res => {
            this.payDetainmentConfig = res
        })
    },
    methods: {
        closed() {
            const showDetainment = this.advertDetail?.advertType !== 'training'
            this.close(showDetainment)
        },
        async getTime() {
            const nopayExpiredTime = await getNopayExpiredTime(this.advertDetail.goodsKey)
            if (nopayExpiredTime) {
                this.nopayExpiredTime = nopayExpiredTime
                if (this.nopayExpiredTime > +new Date()) {
                    this.startNopayTimer(this.nopayExpiredTime)
                }
            }
        },
        buy(fragmentName1) {
            this.buyVipGoods(fragmentName1)
        },
        startDiscountTimer(endTime) {
            clearInterval(this.timer)
            this.timer = null

            let date = +new Date()
            let remainder = endTime - date
            if (remainder < 0) {
                return
            }
            this.timer = setInterval(() => {
                let date = +new Date()
                let remainder = endTime - date
                this.remainderText = formatRemainTime(remainder)
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    this.timer = null
                    this.$EventBus.$emit('updatePendantResource')
                }
            }, 16)
        },
        startNopayTimer(endTime) {
            clearInterval(this.nopayTimer)
            this.nopayTimer = null

            let date = +new Date()
            let remainder = endTime - date
            if (remainder < 0) {
                return
            }
            this.nopayTimer = setInterval(() => {
                let date = +new Date()
                let remainder = endTime - date
                this.noplayRemainderText = formatRemainTime(remainder, 'mm:ss:SS')
                if (remainder <= 0) {
                    clearInterval(this.nopayTimer)
                    this.nopayTimer = null
                }
            }, 16)
        },
        async openHelp() {
            if (this.isInDialog) {
                this.$EventBus.$emit('openHelpPopup', this.isShowHelpIcon)
                return
            }

            let helpFragmentName1 = '支付弹窗'

            if (this.isShowHelpIcon) {
                this.helpFragmentName1 = helpFragmentName1
                this.helpPopupVisible = true
            } else {
                const authToken = await getAuthToken()
                if (!authToken) {
                    await goLogin()
                }
                webOpen({
                    url: helpURL,
                    titleBar: true,
                })
            }

            let fragmentName1 = '支付弹窗'
            let actionName = '客服图标'
            let actionType = '点击'

            // 精品课直播间页_支付弹窗_点击客服图标
            trackEvent({fragmentName1, actionName, actionType, eventId: 'customer_service'})
        },
        close(showDetainment = true) {
            if (this.isInDialog) {
                if (isAndroid && showDetainment) {
                    this.$emit('showDetainmentPupop')
                    this.detainmentPupopVisible = !!showDetainment

                    MCProtocol.Vip.enableHalfScroll({
                        enable: false,
                    })
                } else {
                    webClose()
                }
            } else {
                this.$emit('update:show', false)
                if (isAndroid && showDetainment && isMucang) {
                    this.detainmentPupopVisible = !!showDetainment
                }
            }
        },
        async buyVipGoods(fragmentName1) {
            if (this.sellRateEnable && this.remainSotckInfo.sellRateNum === 0 && !(this.nopayExpiredTime > 0 && this.nopayTimer)) {
                toast('优惠名额已售罄')
                return
            } else if (this.checkAgreement) {
                if (this.goodsDetail.goodsType === 'training') {
                    if (!this.readed2) {
                        await this.$confirmProtocol({
                            type: this.goodsDetail.goodsType,
                        })
                    }
                } else if (!this.readed) {
                    await this.$confirmProtocol()
                }
            }
            if (!this.goodsDetail.groupKey) return

            fragmentName1 = fragmentName1 || this.fragmentName1
            if (isMucang && (isIOS && !this.goodsDetail.entityGoods) && compareVersionComplex('8.3.0') <= 0) {
                MCProtocol.Vip.BuyGoods({
                    groupKey: this.advertDetail.goodsKey,
                    fragmentName1: fragmentName1,
                    callback: data => {
                        console.log('BuyGoods', data)
                        if (data.success && data.data.status === 'success') {
                            toast(
                                '您购买的服务已到账，快去使用吧！',
                                2000,
                                () => {
                                    reload()
                                },
                                {frozen: true}
                            )
                        }
                    },
                })
            } else if (this.advertDetail.advertType === 'lesson') {
                this.payForLesson(fragmentName1)
            } else {
                this.payForVip(fragmentName1)
            }

            let fragmentName2 = '支付弹窗'
            let actionType = '点击'
            let actionName = '确认支付'

            // 精品课直播间页_右方商品图标_支付弹窗_点击确认支付
            // 精品课直播间页_支付挽留弹窗_支付弹窗_点击确认支付
            // 精品课直播间页_热卖提醒_支付弹窗_点击确认支付
            trackEvent({
                fragmentName1,
                fragmentName2,
                actionType,
                actionName,
                groupKey: this.advertDetail.goodsKey,
                goodsUniqueKey: this.advertDetail.goodsKey,
            })
        },
        async payForLesson(fragmentName1) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                return
            }
            if (payType === 100) {
                openNewVip({
                    url: getUrl(replaceInviteUrl, {
                        channelCode: this.goodsDetail.channelCode,
                    }),
                })
                return
            }
            payLessonGroup(
                {
                    payType,
                    payChannel,
                    lessonGroupId: this.advertDetail.goodsKey,
                    appleId: this.goodsDetail.applePriceId,
                    squirrelGoodsInfo: this.goodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        lessonId: URLParams.id,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 1,
                    pageData: {
                        lessonGroupId: this.advertDetail.goodsKey,
                    },
                },
                getPayAfterStrategy(this.isInDialog, 'lesson')
            )
        },
        async payForVip(fragmentName1) {
            let payType, payChannel
            let wxData = {}
            if (!isMucang) {
                if (isWeixin) {
                    payType = 2
                    payChannel = 'weixin_mobile'
                    wxData = {
                        openId: await getOpenid(),
                        platformType: 'wap',
                        _cityCode: (await getCityCode()).adcode,
                    }
                } else {
                    toast('请在微信浏览器内打开使用')
                    return
                }
            } else if (isIOS && !this.goodsDetail.entityGoods) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            if (this.goodsDetail.goodsType === 'training') {
                const authToken = await getAuthToken()
                if (!authToken) {
                    goLogin({refresh: true})
                    return
                }
            }

            if (payType === 100) {
                openNewVip({
                    url: getUrl(replaceInviteUrl, {
                        channelCode: this.advertDetail.goodsKey,
                    }),
                })
                return
            }

            createMobileOrder(
                {
                    sessionIds: this.goodsDetail.sessionIdList.join(','),
                    appleId: this.goodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    entityGoods: this.goodsDetail.entityGoods,
                    goodsType: this.goodsDetail.goodsType,
                    couponCode: this.couponUsable.couponCode,
                    activityType: this.goodsDetail.activityType,
                    groupKey: this.advertDetail.goodsKey,
                    squirrelGoodsInfo: this.goodsDetail.squirrelGoodsInfo,
                    promotionType: this.goodsDetail.promotionActivityData.activityExt,
                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        groupKey: this.advertDetail.goodsKey,
                        lessonId: URLParams.id,
                        liveActivityId: this.goodsDetail.promotionActivityData.activityId,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this.advertDetail.goodsKey,
                    },
                    ...wxData,
                },
                getPayAfterStrategy(this.isInDialog, this.goodsDetail.goodsType),
                (res) => {
                    if (this.goodsDetail.goodsType === 'training') {
                        toast(
                            '确认支付状态中，请勿点击或退出APP',
                            5000,
                            async () => {
                                this.openDetail(this.motionDetail.detailUrl)
                                await new Promise(resolve => {
                                    pageSwitch.onPageShow(resolve)
                                })
                                if (this.isInDialog) {
                                    webClose()
                                } else {
                                    this.$EventBus.$emit('updatePendantResource')
                                    this.$emit('update:show', false)
                                }
                            },
                            {frozen: true}
                        )
                    }
                },
                () => {
                    const {goodsList, img, link, time} = this.payDetainmentConfig
                    const isPopup = goodsList.indexOf(this.advertDetail.goodsKey) > -1 && img && link && time
                    if (isPopup) {
                        this.$refs['nopayDetainmentPopupRef'].popup()
                    }
                }
            )
        },
        assembleUrl(url) {
            if (!url) return url
            var params = getURLParams(null, window.location.href)
            let urlParams = getURLParams(null, url)
            // url上如果没有kemu或者kemuStyle,手动带上直播间的kemu
            if (!(urlParams.kemu || urlParams.kemuStyle) && URLParams.kemuStyle) {
                url = getUrl(url, {kemuStyle: URLParams.kemuNum})
            }
            return getUrl(url, Object.assign(params, {
                kemu: URLParams.kemuNum,
            }))
        },
        realTxt(text) {
            return text.replace(/\d+、/g, '')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.pay-popup {
    .wrap {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background-color: #fff;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
        height: 100%;
        &.full {
            border-radius: 0;
            .scroll_view {
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }
        }
    }
    .header {
        padding: 40px 40px 0;
        .line {
            display: flex;
            align-items: center;
            &.between {
                justify-content: space-between;
            }
        }
        .title {
            .fontSizeWithElder(40px);
            color: #333;
        }
        .mark {
            color: #29c0fb;
            border: 1px solid #29c0fb;
            border-radius: 6px;
            .fontSizeWithElder(22px);
            line-height: 32px;
            padding: 0 6px;
            margin-left: 10px;
            max-width: 228px;
        }
        .desc {
            .fontSizeWithElder(28px);
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    .nogap-content {
        overflow: hidden;
        flex: 1;
        background-color: #EBF8FF;

        .head-bg {
            height: 800px;
            .standard-bg{
                width: 100%;
                height: 100%;
                background: url('../assets/images/standard-bg.png') no-repeat;
                background-size:100% 100%;
            }
            .experince-bg{
                width: 100%;
                height: 100%;
                background: url('../assets/images/experience-bg.png') no-repeat;
                background-size:100% 100%;
            }
        }

        .content-box {
            background: linear-gradient(180deg, #dfefff, #ffffff 15%);
            border-radius: 32px;
            padding: 30px;
            width: 690px;
            margin: -420px auto 0;
            position: relative;

            .title {
                font-size: 40px;
                font-weight: bold;
                color: transparent;
                text-fill-color: transparent;
                background-clip: text;
                color: #0C2C84;
            }

            .dec-title {
                font-size: 26px;
                margin-top: 24px;
                color: #6285BB;
                margin-bottom: 40px;
            }
            .content-bg {
                position: absolute;
                top: 24px;
                right: 36px;
                width: 248px;
                height: 248px;
                background: url('../assets/images/training-bg.png') no-repeat;
                background-size: 100% 100%;
            }
            .course-list {
                .course-item {
                    background: linear-gradient(328deg,#f4fdff 0%, #ebf6ff 100%);
                    border-radius: 16px;
                    padding: 30px 16px 30px;
                    margin-top: 20px;
                    border: 1px solid #bfd9ff;
                    position: relative;
                    .course-title {
                        font-size: 32px;
                        font-weight: bold;
                        color: transparent;
                        text-fill-color: transparent;
                        background-clip: text;
                        color: #2B84FF;
                        margin-bottom: 8px;
                    }

                    .price-box {
                        position: absolute;
                        right: 20px;
                        top: 24px;
                        line-height: 1;

                        .price {
                            position: relative;
                            z-index: 1;
                            width: 100%;
                            height: 100%;
                            font-size: 48px;
                            color: #23324F;
                            font-weight: bold;

                            .unit {
                                font-size: 32px;
                                font-weight: bold;
                            }
                        }

                        .price-bg {
                            position: absolute;
                            width: 100%;
                            height: 20px;
                            left: 0;
                            bottom: 0;
                            background: linear-gradient(90deg, rgba(186, 229, 255, 0.00), #cbdfff);
                            border-radius: 8px;
                        }
                    }

                    .send-box {
                        position: absolute;
                        right: 20px;
                        top: 30px;
                        padding: 8px 10px;
                        background-color: #FFD5D5;
                        font-size: 24px;
                        color: #FF586F;
                        border-radius: 8px;
                        vertical-align: middle;
                    }

                    .table {
                        margin-top: 20px;
                        width: 100%;
                        background: #ffffff;
                        border: 1px solid #d7e7ff;
                        border-radius: 8px;
                        font-size: 24px;
                        border-collapse: collapse;

                        th,
                        td {
                            padding: 20px 10px;
                            text-align: center;

                        }

                        thead {
                            background: #CFF0FF;

                            th {
                                .tag {
                                    display: inline-block;
                                    width: 116px;
                                    vertical-align: middle;
                                    margin-left: 8px;
                                    height: 36px;
                                }
                            }
                        }

                        tbody {
                            tr {
                                td:nth-of-type(1) {
                                    white-space: nowrap;
                                }
                            }

                            td {
                                line-height: 1.5;
                                color: #878FA1;
                                border: 1px solid #D7E7FF;
                            }
                        }
                    }

                    .course-info-list {
                        .course-info-item {
                            margin-top: 8px;
                            font-size: 28px;
                            color: #878FA1;
                            line-height: 40px;
                            display: flex;
                            align-items: center;
                            display: inline-block;

                            &::before {
                                content: '';
                                width: 8px;
                                height: 8px;
                                display: inline-block;
                                background-color: #23324F;
                                border-radius: 50%;
                                margin-right: 16px;
                            }

                            .black {
                                color: #23324F;
                            }
                        }
                    }
                }
                .item {
                    /deep/ img {
                        vertical-align: middle;
                        margin-top: 20px;
                    }
                }
            }

        }
        .limit-send {
            margin: 0 auto;
            margin-top: 10px;
            height: 420px;
        }
    }
    .content {
        padding-bottom: 20px;
        margin-top: 38px;
        overflow: hidden;
        flex: 1;
        .legal-image {
            margin: 0 30px;
            &:not(:first-child) {
                margin-top: 38px;
            }
        }
    }
    .ifram-container {
        flex: 1;
        position: relative;
        overflow: hidden;
        .touch {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }
        iframe {
            border: 0;
        }
    }
    .nogap-footer {
        background: linear-gradient(180deg,#e2f1ff, #ffffff 21%);
        border-radius: 20px 20px 0px 0px;
        margin-top: -20px;
        padding-top: 30px;
        position: relative;
        .pay-type-wrap {
            margin-top: -15px;
        }
        .pay-list {
            padding: 0 17px 6px;
        }
        .pt-sol {
            background: url(../assets/images/training-slogan.png) no-repeat;
            background-size: 100%;
            width: 658px;
            height: 64px;
            margin: 0 auto;
        }
        .buy-vip {
            width: 690px;
            height: 120px;
            display: flex;
            align-items: center;
            background: url(../assets/images/buy-button3.png) no-repeat;
            background-size: 100%;
            position: relative;
            padding: 0 0 0 40px;
            margin: 0 auto 10px;
            .kh {
                display: flex;
                flex: 1;
                align-items: center;
            }
            .price {
                color: #FFF2C0;
                text-align: center;
                font-size: 36px;
                span {
                    padding-left: 5px;
                    font-size: 60px;
                }
            }
            .hf {
                margin-left: 20px;
            }
            .del-price {
                text-decoration: line-through;
                color: rgba(255, 255, 255, 0.5);
                font-size: 24px;
                margin-top: 4px;
            }
            .discount {
                background: url(../assets/images/discount-bg.png) no-repeat;
                background-size: 100%;
                width: 138px;
                height: 32px;
                color: #99581C;
                font-size: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                text-wrap: nowrap;
            }
            .text {
                text-align: center;
                width: 232px;
                font-size: 36px;
                color: #fff;
            }
        }
        .line {
            padding: 10px 36px 15px;
            display: flex;
            &.between {
                justify-content: space-between;
            }
            &.around {
                justify-content: space-around;
            }
        }
    }
    .footer {
        .pay-list {
            padding: 6px 30px;
            border-top: 1px solid #e8e8e8;
        }
        .sal {
            .fontSizeWithElder(20px);
            color: #ff5147;
            border-radius: 4px;
            background: rgba(255, 84, 74, 0.08);
            display: flex;
            padding: 0 6px;
            align-items: center;
        }
        .line {
            padding: 10px 30px;
            display: flex;
            &.between {
                justify-content: space-between;
            }
            &.around {
                justify-content: space-around;
            }
        }
        .item {
            margin-right: 30px;
            &:last-child {
                margin-right: 0;
            }
        }
        .icon {
            background-repeat: no-repeat;
            background-size: 44px 44px;
            background-position: center 8px;
            font-size: 24px;
            color: #333;
            padding-top: 50px;
            &.icon_kefu {
                background-image: url(../assets/images/<EMAIL>);
            }
        }
        .buy-vip {
            width: 520px;
            height: 80px;
            display: flex;
            align-items: center;
            background: url(../assets/images/buy-button.png) no-repeat;
            background-size: 100%;
            position: relative;
            padding: 0 10px 0 25px;
            .kh {
                display: flex;
                flex: 1;
                justify-content: center;
                align-items: center;
            }
            .price {
                color: #fff;
                text-align: center;
                span {
                    padding-left: 5px;
                    .fontSizeWithElder(40px);
                }
            }
            .hf {
                width: 160px;
            }
            .del-price {
                text-decoration: line-through;
                color: rgba(255, 255, 255, 0.5);
                .fontSizeWithElder(18px);
                text-align: center;
            }
            .discount {
                background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
                color: #fff;
                border-radius: 20px;
                .fontSizeWithElder(18px);
                height: 28px;
                margin: 0 auto;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .text {
                text-align: center;
                width: 196px;
                .fontSizeWithElder(32px);
                color: #fff;
            }
            .time {
                position: absolute;
                width: 220px;
                height: 36px;
                background: url(../assets/images/vip-time-bg.png) no-repeat;
                background-size: cover;
                right: 0;
                top: -26px;
                color: #6f2117;
                .fontSizeWithElder(22px);
                display: flex;
                justify-content: center;
                align-items: center;
                span {
                    color: #ff4a40;
                    margin-left: 5px;
                }
            }

            .sale-rate {
                position: absolute;
                width: 280px;
                height: 36px;
                background: url(../assets/images/sell-out-bg.png) no-repeat;
                background-size: 262px 36px;
                right: 0;
                top: -26px;
                padding-left: 10px;
                display: flex;
                align-items: center;
                .sale-text {
                    font-size: 18px;
                    color: #6f2117;
                    padding-top: 2px;
                }
                .sale-line {
                    width: 116px;
                    height: 20px;
                    background: #b45900;
                    border-radius: 10px;
                    margin-left: 8px;
                    position: relative;
                }
                .num {
                    position: absolute;
                    left: 0;
                    top: 0;
                    color: #6f2117;
                    padding-left: 8px;
                    padding-top: 2px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    .fontSizeWithElder(16px);
                    &.sellout {
                        color: #ffbcb9;
                    }
                }
                .bg {
                    height: 20px;
                    background: url(../assets/images/progress3.gif) no-repeat;
                    background-size: 122px 20px;
                    border-radius: 8px;
                    position: relative;
                    transition: width 1.5s;
                    &.float::after {
                        content: '';
                        position: absolute;
                        right: 0;
                        transform: translateX(50%);
                        top: -3px;
                        width: 23px;
                        height: 28px;
                        background: url(../assets/images/float-icon3.png) no-repeat;
                        background-size: cover;
                    }
                }
            }
            .nopay-time {
                position: absolute;
                width: 236px;
                height: 36px;
                background: url(../assets/images/nopay-bg3.png) no-repeat;
                background-size: cover;
                right: 0;
                top: -26px;
                color: #6f2117;
                font-size: 18px;
                display: flex;
                justify-content: center;
                align-items: center;
                line-height: 1;
                span {
                    white-space: nowrap;
                    min-width: 90px;
                    height: 24px;
                    background: #ff4c42;
                    border-radius: 4px;
                    color: #fff;
                    padding-left: 6px;
                    margin: 0 2px;
                    display: flex;
                    align-items: center;
                }
            }
        }
        .main {
            flex: 1;
            position: relative;
            .directly-buy {
                box-sizing: border-box;
                background: #fff;
                color: #333;
                border: 1px solid #b2b2b2;
                .text {
                    color: #666;
                }
            }
            .discount-buy {
                background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
                color: #fff;
                border: none;
                text-align: center;
                .del-price {
                    color: #ffcfcd;
                }
            }
            .price {
                display: block;
                .fontSizeWithElder(32px);
                line-height: 1.2;
            }
            .text {
                display: block;
                .fontSizeWithElder(22px);
                line-height: 1.2;
            }
            .del-price {
                display: block;
                .fontSizeWithElder(20px);
                line-height: 1.2;
            }
            button {
                width: 100%;
                height: 80px;
                border-radius: 40px;
                &.disabled {
                    background: #999;
                }
            }
            .mark {
                position: absolute;
                top: -30px;
                right: 8px;
                padding: 0 20px;
                text-align: center;
                background: linear-gradient(111deg, #ffd74f 0%, #ffc634 100%);
                border-radius: 0px 20px 0px 16px;
                color: #8c2801;
                .fontSizeWithElder(22px);
                line-height: 40px;
            }
        }
    }
}
</style>
