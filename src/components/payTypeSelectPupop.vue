<template>
    <popup
        class="pay-select-popup"
        :position="'bottom'"
        @closed="closed"
        :show.sync="visible"
    >
        <div class="header">选择支付方式</div>
        <div class="footer">
            <payList direction="vrt" theme="vrt1" v-if="(isAndroid || goodsDetail.entityGoods) && isMucang" />
            <div class="line between">
                <vipAgreement theme="blue" :type="goodsDetail.goodsType"></vipAgreement>
                <couponItem :couponUsable="couponUsable" :goodsDetail="goodsDetail" :entityGoods="goodsDetail.entityGoods" v-if="isMucang" />
            </div>
            <div class="line btn">
                <div class="item confirm" @click="buy">确认支付 ¥{{ price }}</div>
            </div>
        </div>
    </popup>
</template>

<script>
import {
    isAndroid,
    isMucang,
    // webOpen
} from '../utils/tools'
import popup from '../components/dialog'
import payList from '../components/payList'
import vipAgreement from '../components/vipAgreement'
import couponItem from '../components/couponItem'

export default {
    components: {
        popup,
        payList,
        vipAgreement,
        couponItem,
    },
    data() {
        return {
            visible: false,
            isMucang,
            isAndroid: isAndroid,
        }
    },
    model: {
        prop: 'show',
        event: 'setShow',
    },
    props: {
        show: Boolean,
        couponUsable: Object,
        goodsDetail: Object,
        price: [String, Number],
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            this.$emit('update:show', false)
        },
        buy() {
            this.$emit('buy', '支付选择弹窗')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.pay-select-popup {
    /deep/ .dialog {
        background-color: #fff;
        padding: 20px 30px 0;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
    }
    .header {
        text-align: center;
        .fontSizeWithElder(32px);
        padding-bottom: 20px;
    }
    .footer {
        .pay-list {
            padding: 0 10px 40px;
        }
        .line {
            margin-top: 20px;
            padding: 0 15px;
            display: flex;
            align-items: center;
            &.between {
                justify-content: space-between;
            }
        }
        .btn {
            margin-top: 15px;
            padding: 0 10px 20px;
        }
        .item {
            flex: 1;
            margin-right: 30px;
            height: 80px;
            line-height: 80px;
            border-radius: 40px;
            text-align: center;
            &:last-child {
                margin-right: 0;
            }
        }
        .confirm {
            background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
            color: #fff;
            border: none;
            .del-price {
                color: #ffcfcd;
            }
        }
        /deep/ .agreement {
            .fontSizeWithElder(20px);
        }
    }
}
</style>
