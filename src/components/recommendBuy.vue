<template>
    <div>
        <template v-if="isShow">
            <div class="motion" @click="openDialog">
                <div style="position: relative;">
                    <img
                        width="100%"
                        :src="
                            canUpgrade
                                ? recommendContent.upgradeIconUrl || recommendContent.iconUrl
                                : recommendContent.iconUrl
                        "
                    />
                    <div class="mk" v-if="recommendRemainSotckInfo.sellRateNum <= 0 && canBuy">
                        已售罄
                    </div>
                </div>
                <div class="sale-rate" v-if="sellRateEnable">
                    <div class="text">{{ sellRateText }}:</div>
                    <div class="line">
                        <div
                            class="bg float"
                            v-if="recommendRemainSotckInfo.sellRateNum > 0"
                            :style="{width: recommendRemainSotckInfo.sellRateNum + '%'}"
                        ></div>
                        <div class="num" :style="numStyle">
                            <template v-if="recommendRemainSotckInfo.sellRateNum > 0"
                                >{{ recommendRemainSotckInfo.sellRateNum }}%</template
                            >
                            <template v-else>已售罄</template>
                        </div>
                    </div>
                </div>
                <div class="btn vip">{{ canUpgrade ? '立即升级' : '立即购买' }}</div>
                <div class="timer">
                    <span class="time">{{ remainderText }} </span>
                    <span class="tit">后结束</span>
                </div>
            </div>
        </template>
        <recommendBuyPupop
            @showMotion="showMotion"
            :remainderText="remainderText2"
            :show.sync="recommendBuyVisible"
            :content="recommendContent"
            :recommendRemainSotckInfo="recommendRemainSotckInfo"
        ></recommendBuyPupop>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {formatRemainTime, trackEvent, toast} from '../utils/tools'
import recommendBuyPupop from './recommendBuyPupop'

export default {
    components: {recommendBuyPupop},
    data() {
        return {
            recommendRemainSotckInfo: {},
            recommendBuyVisible: false,
            remainderText: '00:00',
            remainderText2: '00:00:00',
            show: false,
            timer: null,
        }
    },
    watch: {
        isShow(val) {
            this.$emit('noticeRecommendVisualStatus', val)
            this.$EventBus.$emit('motionSizeChange')
        },
        remainSotckInfo: {
            handler() {
                this.generateRemainSotckInfo()
            },
            deep: true,
            immediate: true,
        },
    },
    computed: {
        ...mapState(['bizConfig', 'remainSotckInfo', 'pendantResource']),
        canBuy() {
            // 在ios端，当一个元素处于animation动画的状态下，
            // dom重排操作+修改动画元素的文字，修改文字操作大概率无法生效
            // 猜测由于dom重排操作会阻塞页面，导致修改动画元素被忽略！
            return !this.pendantResource.bought || this.pendantResource.upgrade
        },
        canUpgrade() {
            if (this.pendantResource.advertType === 'vip') {
                return this.pendantResource.bought && this.pendantResource.upgrade
            } else {
                return false
            }
        },
        isUnfinished() {
            return this.remainderText !== '00:00'
        },
        isShow() {
            return this.show && this.isUnfinished && !this.bizConfig.nopayMotionVisible
        },
        sellRateEnable() {
            return (
                this.recommendRemainSotckInfo.sellRateEnable &&
                this.recommendRemainSotckInfo.applyScene.indexOf('3') > -1
            )
        },
        sellRateText() {
            return '优惠名额' + (this.recommendRemainSotckInfo.sellRateNum > 50 ? '剩余' : '仅剩')
        },
        numStyle() {
            // 40-30
            let up = 40
            let down = 30
            let left, color, transform
            if (this.recommendRemainSotckInfo.sellRateNum < up) {
                if (this.recommendRemainSotckInfo.sellRateNum > down) {
                    left = 50 + up - down
                } else {
                    left = 50
                }
                left = left + '%'
                transform = 'translateX(-50%)'
                color = '#ffbcb9'
            }
            return {
                color,
                transform,
                left,
            }
        },
    },
    props: {
        recommendContent: Object,
    },
    methods: {
        generateRemainSotckInfo() {
            if (this.remainSotckInfo.dataList && this.remainSotckInfo.dataList.length) {
                let group = this.recommendContent.group || 1
                let {sellRateEnable, sellRateNum} = this.remainSotckInfo.dataList[group - 1]
                let applyScene = this.remainSotckInfo.applyScene
                this.recommendRemainSotckInfo = {
                    sellRateEnable,
                    sellRateNum,
                    applyScene,
                }
            }
        },
        startRemainder() {
            if (!this.recommendContent.popupTime) {
                this.remainderText = '00:00'
                return
            }
            let seconds = this.recommendContent.popupTime
            let date = +new Date()
            let end = date + seconds * 1000
            clearInterval(this.timer)
            this.timer = setInterval(() => {
                date = +new Date()
                let remainder = end - date
                this.remainderText = formatRemainTime(remainder, 'mm:ss')
                this.remainderText2 = formatRemainTime(remainder, 'mm:ss:SS')
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    this.recommendBuyVisible = false
                }
            }, 16)
        },
        openDialog() {
            if (this.sellRateEnable && this.remainSotckInfo.sellRateNum === 0) {
                toast('优惠名额已售罄')
            } else {
                this.recommendBuyVisible = true
            }
        },
        openRecommendBuy() {
            this.recommendBuyVisible = true
            setTimeout(() => {
                this.startRemainder()
                this.generateRemainSotckInfo()
                let fragmentName1 = '推荐商品弹窗'
                let actionType = '出现'

                // 埋点梳理-驾考宝典-0713
                // 精品课直播间页_推荐商品弹窗_出现
                trackEvent({
                    fragmentName1,
                    actionType,
                    groupKey: this.recommendContent.goodsUniqueKey,
                    goodsUniqueKey: this.recommendContent.goodsUniqueKey,
                })
            })
        },
        closeRecommendBuy() {
            this.recommendBuyVisible = false
            this.show = false
        },
        showMotion() {
            this.show = true
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
@keyframes mymove {
    0% {
        transform: scale(0.9);
    }
    40% {
        transform: scale(1);
    }
    60% {
        transform: scale(0.98);
    }
    100% {
        transform: scale(0.9);
    }
}
.motion {
    width: 212px;
    min-height: 1px;
    margin-top: 10px;
    position: relative;
    text-align: center;
    img {
        width: 100%;
        position: relative;
        z-index: 1;
    }
    .mk {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background-color: rgba(0,0,0,0.6);
        z-index: 2;
        color: #fff;
    }
    .btn {
        margin: -29px auto 0;
        color: #fff;
        text-align: center;
        .fontSizeWithElder(28px);
        width: 212px;
        height: 58px;
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &.vip {
            background: url(../assets/images/<EMAIL>) no-repeat;
            background-size: cover;
            width: 212px;
            height: 56px;
            padding-bottom: 4px;
            transform: translateZ(0);
            animation: mymove 0.9s infinite;
            animation-timing-function: ease-out;
        }
    }

    .sale-rate {
        width: 100%;
        height: 104px;
        margin: 0 auto;
        text-align: left;
        padding: 6px 18px 0 12px;
        background: #ec212d;
        .text {
            .fontSizeWithElder(20px);
            color: #fff;
        }
        .line {
            margin-top: 6px;
            height: 24px;
            background: #ec212d;
            background: linear-gradient(180deg, #a50000 0%, #c50000 100%);
            border-radius: 8px;
            position: relative;
        }
        .num {
            position: absolute;
            left: 0;
            top: 0;
            color: #5a0000;
            padding-left: 8px;
            height: 24px;
            display: flex;
            align-items: center;
            .fontSizeWithElder(20px);
            &.sellout {
                color: #ffbcb9;
            }
        }
        .bg {
            height: 24px;
            background: url(../assets/images/progress2.gif) no-repeat;
            background-size: 180px 24px;
            border-radius: 8px;
            position: relative;
            transition: width 1.5s;
            &.float::after {
                content: '';
                position: absolute;
                right: 0;
                transform: translateX(50%);
                top: -2px;
                width: 27px;
                height: 32px;
                background: url(../assets/images/float-icon2.png) no-repeat;
                background-size: cover;
            }
        }
    }
    .timer {
        display: inline-block;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 14px;
        color: #fff;
        line-height: 1.2;
        margin: 0 auto;
        padding: 6px 16px;
        .fontSizeWithElder(18px);
    }
    &.motion-comp {
        .timer {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: -10px;
            height: 50px;
            background: linear-gradient(128deg, #ffe2cf 0%, #ffcb9b 100%);
            box-shadow: 0px 1px 0px 0px rgba(244, 154, 94, 1);
            border-radius: 16px 16px 0 0;
            color: #ff3632;
            flex-direction: row;
            padding: 0;
            margin: 0 2px -4px;
            white-space: nowrap;
        }
        .tit {
            margin-right: 4px;
        }
    }
}

.landscape /deep/ {
    // landscape
    .motion {
        position: absolute;
        top: 16px;
        right: 16px;
        transform: scale(0.9);
    }
}
</style>
