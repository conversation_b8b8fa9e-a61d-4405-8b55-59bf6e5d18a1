<template>
    <popup
        @closed="closed"
        class="recommend-buy-popup"
        :position="'bottom'"
        :show.sync="visible"
        :isInDialog="isInDialog"
    >
        <div class="content">
            <div class="image">
                <img :src="content.imgUrl" />
            </div>
            <div class="timer">
                <img class="text" src="../assets/images/xsms-text.png" />
                <p v-html="remainderTextHtml"></p>
            </div>
            <div class="sale-rate" v-if="sellRateEnable">
                <div
                    class="bg float"
                    v-if="recommendRemainSotckInfo.sellRateNum > 0"
                    :style="{width: recommendRemainSotckInfo.sellRateNum + '%'}"
                ></div>
                <div class="text" :style="numStyle">
                    <template v-if="recommendRemainSotckInfo.sellRateNum > 0"
                        >{{ recommendRemainSotckInfo.sellRateNum }}%</template
                    >
                    <template v-else>已售罄</template>
                </div>
            </div>
            <div class="pay-list-warp">
                <payList direction="hor" theme="hor2" v-if="(isAndroid || goodsDetail.entityGoods) && isMucang" />
            </div>
            <div @click="buyVipGoods" class="buy-btn">
                <div class="mark" v-if="content.buyTag">{{ content.buyTag }}</div>
                <div class="price">
                    ¥<span>{{ showPrice }}</span>
                </div>
                <div class="desc">
                    <p class="text1">直播间福利价</p>
                    <p class="text2">限时特惠价</p>
                </div>
                <button>
                    {{ canUpgrade ? '立即升级' : '立即开通' }}
                </button>
            </div>
            <div class="line between">
                <vipAgreement theme="light" :type="goodsDetail.goodsType"></vipAgreement>
                <couponItem :couponUsable="couponUsable" :goodsDetail="goodsDetail" :entityGoods="goodsDetail.entityGoods" v-if="isMucang" />
            </div>
        </div>
    </popup>
</template>

<script>
import {mapState, mapGetters} from 'vuex'
import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {MCProtocol} from '@simplex/simple-base'
import {find} from 'lodash-es'
import {
    URLParams,
    isAndroid,
    isIOS,
    trackEvent,
    toast,
    compareVersionComplex,
    getAuthToken,
    goLogin,
    isMucang,
    isWeixin,
    getOpenid,
    getCityCode,
} from '../utils/tools'
import goodsMixin from '../utils/goodsMixin'
import {reload, webClose} from '../utils/jump'
import {createMobileOrder, payLessonGroup, getPayAfterStrategy} from '../utils/payHelper'
import popup from '../components/dialog'
import payList from '../components/payList'
import vipAgreement from '../components/vipAgreement'
import couponItem from '../components/couponItem'

export default {
    mixins: [goodsMixin],
    components: {popup, payList, vipAgreement, couponItem},
    data() {
        return {
            isMucang,
            visible: false,
            isIOS,
            isAndroid,
            timer: null,
            goodsDetailData: {},
        }
    },
    watch: {
        show(val) {
            this.visible = val

            if (val) {
                const v1 = async () => {
                    if (!this.goodsDetail.groupKey) {
                        let advertDetail = {goodsKey: this.content.goodsUniqueKey, advertType: this.content.recommendType}
                        if (this.isInDialog) {
                            this.goodsDetailData = await this.getGoodsEntry(advertDetail)
                        } else {
                            this.goodsDetailData = await this.getGoodsItem(advertDetail)
                        }
                    }
                }
                v1()
            }
        },
    },
    props: {
        show: Boolean,
        remainderText: String,
        content: Object,
        recommendRemainSotckInfo: Object,
        isInDialog: {
            type: Boolean,
            default: false,
        },
        goodsDetailProp: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    computed: {
        ...mapState(['selectCoupons']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed', 'readed2']),
        couponUsable() {
            return this.selectCoupons[this.content.goodsUniqueKey + '_selectCoupon'] || {}
        },
        showPrice() {
            if (this.content.recommendType === 'vip') {
                let payPrice = this.goodsDetail.price
                let couponPrice = this.couponUsable.priceCent
                if (couponPrice) {
                    return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
                } else {
                    return payPrice
                }
            } else {
                return this.goodsDetail.price
            }
        },
        numStyle() {
            // 18-0
            let up = 18
            let down = 100
            let left, color, transform
            if (this.recommendRemainSotckInfo.sellRateNum < up) {
                if (this.recommendRemainSotckInfo.sellRateNum > down) {
                    left = 50 + up - down
                } else {
                    left = 50
                }
                left = left + '%'
                transform = 'translateX(-50%)'
                color = '#ffbcb9'
            }
            return {
                color,
                transform,
                left,
            }
        },
        sellRateEnable() {
            return (
                this.recommendRemainSotckInfo.sellRateEnable &&
                this.recommendRemainSotckInfo.applyScene.indexOf('4') > -1
            )
        },
        sellRateText() {
            return this.recommendRemainSotckInfo.sellRateNum > 50 ? '剩余' : '仅剩'
        },
        remainderTextHtml() {
            let t = this.remainderText.replace(/\d/g, '<span>$&</span>')
            return t
        },
        canUpgrade() {
            if (this.content.recommendType === 'vip') {
                return this.goodsDetail.bought && this.goodsDetail.upgrade
            } else {
                return false
            }
        },
        goodsDetail() {
            if (this.goodsDetailProp?.groupKey) {
                return this.goodsDetailProp
            } else if (this.goodsDetailData?.groupKey) {
                return this.goodsDetailData
            } else {
                return {}
            }
        },
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.$emit('update:show', false)
                this.$emit('showMotion')
            }

            let fragmentName1 = '推荐商品弹窗'
            let actionType = '点击'
            let actionName = '关闭弹窗'

            // 埋点梳理-驾考宝典-0713
            // 精品课直播间页_推荐商品弹窗_点击关闭弹窗
            trackEvent({fragmentName1, actionType, actionName})
        },
        async buyVipGoods() {
            if (this.sellRateEnable && this.recommendRemainSotckInfo.sellRateNum === 0) {
                toast('优惠名额已售罄')
                return
            } else if (this.checkAgreement) {
                if (this.goodsDetail.goodsType === 'training') {
                    if (!this.readed2) {
                        await this.$confirmProtocol({
                            type: this.goodsDetail.goodsType,
                        })
                    }
                } else if (!this.readed) {
                    await this.$confirmProtocol()
                }
            }
            if (!this.goodsDetail.groupKey) return

            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                if (isMucang && (isIOS && !this.goodsDetail.entityGoods) && compareVersionComplex('8.3.0') <= 0) {
                    MCProtocol.Vip.BuyGoods({
                        groupKey: this.content.goodsUniqueKey,
                        fragmentName1: '推荐商品弹窗',
                        callback: data => {
                            console.log('BuyGoods', data)
                            if (data.success && data.data.status === 'success') {
                                toast(
                                    '您购买的服务已到账，快去使用吧！',
                                    2000,
                                    () => {
                                        reload()
                                    },
                                    {frozen: true}
                                )
                            }
                        },
                    })
                } else if (this.content.recommendType === 'lesson') {
                    this.payForLesson()
                } else {
                    this.payForVip()
                }
            })

            let fragmentName1 = '推荐商品弹窗'
            let actionType = '点击'
            let actionName = '去支付'

            // 埋点梳理-驾考宝典-0713
            // 精品课直播间页_推荐商品弹窗_点击去支付
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                payPathType: 1,
                groupKey: this.content.goodsUniqueKey,
                goodsUniqueKey: this.content.goodsUniqueKey,
            })
        },
        async payForLesson() {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                return
            }
            payLessonGroup(
                {
                    payType,
                    payChannel,
                    lessonGroupId: this.content.goodsUniqueKey,
                    appleId: this.goodsDetail.applePriceId,
                    squirrelGoodsInfo: this.goodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        lessonId: URLParams.id,
                    }),
                    fragmentName1: '推荐商品弹窗',
                    payPathType: 1,
                    pageData: {
                        lessonGroupId: this.content.goodsUniqueKey,
                    },
                },
                getPayAfterStrategy(this.isInDialog, 'lesson')
            )
        },
        async payForVip() {
            let payType, payChannel
            let wxData = {}
            if (!isMucang) {
                if (isWeixin) {
                    payType = 2
                    payChannel = 'weixin_mobile'
                    wxData = {
                        openId: await getOpenid(),
                        platformType: 'wap',
                        _cityCode: (await getCityCode()).adcode,
                    }
                } else {
                    toast('请在微信浏览器内打开使用')
                    return
                }
            } else if (isIOS && !this.goodsDetail.entityGoods) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            if (this.goodsDetail.goodsType === 'training') {
                const authToken = await getAuthToken()
                if (!authToken) {
                    goLogin({refresh: true})
                    return
                }
            }

            createMobileOrder(
                {
                    sessionIds: this.goodsDetail.sessionIdList.join(','),
                    appleId: this.goodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    entityGoods: this.goodsDetail.entityGoods,
                    goodsType: this.goodsDetail.goodsType,
                    couponCode: this.couponUsable.couponCode,
                    activityType: this.goodsDetail.activityType,
                    groupKey: this.content.goodsUniqueKey,
                    squirrelGoodsInfo: this.goodsDetail.squirrelGoodsInfo,
                    promotionType: this.goodsDetail.promotionActivityData.activityExt,
                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        groupKey: this.content.goodsUniqueKey,
                        lessonId: URLParams.id,
                        liveActivityId: this.goodsDetail.promotionActivityData.activityId,
                    }),
                    fragmentName1: '推荐商品弹窗',
                    payPathType: 1,
                    pageData: {
                        groupKey: this.content.goodsUniqueKey,
                    },
                    ...wxData,
                },
                getPayAfterStrategy(this.isInDialog, this.goodsDetail.goodsType),
                (res) => {
                    if (this.goodsDetail.goodsType === 'training') {
                        toast(
                            '确认支付状态中，请勿点击或退出APP',
                            5000,
                            async () => {
                                this.openDetail(this.motionDetail.detailUrl)
                                await new Promise(resolve => {
                                    pageSwitch.onPageShow(resolve)
                                })
                                if (this.isInDialog) {
                                    webClose()
                                } else {
                                    this.$EventBus.$emit('updatePendantResource')
                                    this.$emit('update:show', false)
                                }
                            },
                            {frozen: true}
                        )
                    }
                }
            )
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.recommend-buy-popup {
    /deep/ .dialog .close,
    /deep/ .in-dialog .close {
        position: absolute;
        right: 10px;
        top: 70px;
        width: 50px;
        height: 50px;
        background: url(../assets/images/<EMAIL>) no-repeat;
        background-size: 50px 50px;
    }

    .content {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        background-image: url(../assets/images/<EMAIL>);
        background-repeat: no-repeat;
        background-position: center 26px;
        background-size: 100% auto;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 40px);
    }
    /deep/ .in-dialog {
        .content {
            background-image: url(../assets/images/<EMAIL>);
            background-color: #e93931;
            background-position: center bottom;
            height: 100%;
        }
        .close {
            top: 20px;
            right: 30px;
        }
    }
    .content {
        .image {
            width: 692px;
            height: 300px;
            margin: 0 auto;
            overflow: hidden;
            img {
                width: 692px;
                height: 300px;
            }
        }
        .sale-rate {
            width: 476px;
            height: 30px;
            background: #810000;
            border-radius: 15px;
            margin: 30px auto 0;
            .fontSizeWithElder(22px);
            position: relative;
            .text {
                position: absolute;
                left: 0;
                top: 0;
                color: #1d1818;
                padding-left: 15px;
                height: 30px;
                display: flex;
                align-items: center;
                &.sellout {
                    color: #ffbcb9;
                }
            }
            .bg {
                height: 30px;
                background: url(../assets/images/progress.gif) no-repeat;
                background-size: 476px 30px;
                border-radius: 10px;
                position: relative;
                transition: width 1.5s;
                &.float::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    transform: translateX(50%);
                    top: -8px;
                    width: 34px;
                    height: 50px;
                    background: url(../assets/images/float-icon.png) no-repeat;
                    background-size: cover;
                }
            }
        }
        .pay-list-warp {
            padding: 20px 0 10px;
        }
        .pay-list {
            justify-content: center;
            color: #fff;
            /deep/ .item {
                margin: 0 30px;
            }
        }
        /deep/ .agreement {
            text-align: center;
            // padding: 15px 30px 10px;
            .fontSizeWithElder(20px);
            color: rgba(255, 255, 255, 0.6);
            span {
                color: rgba(255, 255, 255, 0.6);
            }
        }
        .buy-btn {
            display: flex;
            margin: 30px auto 0;
            width: 560px;
            height: 96px;
            background: linear-gradient(90deg, #f2ab79 0%, #f9be95 100%);
            border-radius: 50px;
            align-items: center;
            padding: 0 10px 0 35px;
            position: relative;
            button {
                display: block;
                background: linear-gradient(135deg, #211c1c 0%, #141111 100%);
                width: 192px;
                height: 80px;
                line-height: 80px;
                text-align: center;
                border-radius: 42px;
                .fontSizeWithElder(32px);
                color: #fff;
            }
        }
        .coupon-item {
            color: #ededed;
        }
        .line {
            padding: 20px 30px 30px;
            display: flex;
            &.between {
                justify-content: space-between;
            }
            &.around {
                justify-content: space-around;
            }
        }
        .mark {
            position: absolute;
            left: 4px;
            top: -26px;
            .fontSizeWithElder(20px);
            color: #ffca98;
            background: url(../assets/images/<EMAIL>) no-repeat;
            background-size: 220px auto;
            width: 220px;
            height: 40px;
            line-height: 34px;
            text-align: center;
        }
        .price {
            color: #1d1818;
            .fontSizeWithElder(30px);
            span {
                padding-left: 5px;
                .fontSizeWithElder(48px);
                letter-spacing: normal;
                font-weight: bold;
            }
        }
        .desc {
            flex: 1;
            padding-left: 10px;
            line-height: 1.2;
            .text1 {
                color: #1d1818;
                // .fontSizeWithElder(28px);
                font-size: 28px;
                font-weight: bold;
            }
            .text2 {
                color: rgba(94, 46, 6, 0.4);
                .fontSizeWithElder(20px);
            }
        }
        .timer {
            color: #fff;
            text-align: center;
            margin-top: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            p {
                display: flex;
                align-items: center;
                justify-content: center;
                .fontSizeWithElder(54px);
                line-height: 50px;
            }
            /deep/ span {
                text-align: center;
                width: 48px;
                line-height: 70px;
                background: url(../assets/images/time-bg.png) no-repeat;
                background-size: cover;
                margin: 0 4px;
                font-weight: 600;
            }
            .text {
                width: 60px;
                margin: 0 20px 0 0;
            }
            .gif {
                width: 102px;
                margin: 0 4px;
            }
        }
    }
}
.landscape {
    // landscape
    .recommend-buy-popup {
        /deep/ .dialog {
            width: 750px;
            left: 50%;
            transform: translate(-50%, 0);
        }
        .content {
            padding-bottom: 10px;
            .sale-rate {
                margin: 24px auto 0;
            }
            .pay-list-warp {
                padding-bottom: 10px 0 0;
            }
        }
    }
}
</style>
