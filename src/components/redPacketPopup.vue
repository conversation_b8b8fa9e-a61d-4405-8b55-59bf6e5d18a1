<template>
    <div v-if="remainderText !== '00:00:00'">
        <div v-show="showMotion" @click="openActive" class="motion">
            <div class="poster">
                <img width="100%" src="../assets/images/coupon5.png" />
            </div>
        </div>

        <popup
            class="modal-box"
            :position="'center'"
            :bottomCloseIcon="true"
            :show.sync="showModal"
        >
            <div class="content">
                <div class="coupon">
                    <div class="sum">
                        <span class="s1">{{ redPacketPrice }}</span>
                        <span class="s2">元</span>
                    </div>
                    <div class="name">适用商品：<span>全科目通关VIP</span></div>
                </div>
                <div class="timer">
                    福利将在<span> {{ remainderText }}</span> 后消失
                </div>
                <div class="btn">
                    <div class="b1" @click="close">考虑一下</div>
                    <div class="b2" @click="goPay1">立即使用</div>
                </div>
            </div>
        </popup>

        <popup
            class="active-box"
            :position="'center'"
            :bottomCloseIcon="true"
            :show.sync="showActive"
        >
            <div class="content">
                <div class="text">
                    <img :src="roomDetail.teacherHeadImg" />{{ roomDetail.teacherName }}老师
                </div>
                <div class="btn" @click="open"></div>
            </div>
        </popup>
        <popup
            class="model-leave"
            :position="'center'"
            :bottomCloseIcon="true"
            :show.sync="showLeave"
        >
            <div class="content">
                <div class="title">
                    您还有<span>{{ redPacketPrice }}元</span>红包未使用
                </div>
                <p>
                    离开直播间将无法使用了哦!
                </p>

                <div class="coupon">
                    <div class="prize">
                        <div class="number">
                            ￥<span>{{ redPacketPrice }}</span>
                        </div>

                        <div class="original">
                            {{ originalPrice }}元降至{{ originalPrice - redPacketPrice }}元
                        </div>
                    </div>
                </div>

                <div class="btns">
                    <div class="leave" @click="webClose">暂不需要</div>
                    <div class="confirm" @click="goPay2">享受优惠</div>
                </div>
            </div>
        </popup>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {URLParams, formatRemainTime, trackEvent} from '../utils/tools'
import {getActiveConfig} from '../server/active'
import popup from '../components/dialog'

let localDataKey = 'jiaokaobaodian-zhibojian-red-packet-popup'
let minute1 = 60 * 1000
let hour1 = 60 * minute1
let day1 = 24 * hour1
export default {
    components: {popup},
    inject: ['waitPupopList'],
    data() {
        let seconds = 40
        return {
            redPacketPrice: null,
            originalPrice: null,
            activeVipGoodsDetail: {},
            showMotion: false,
            showModal: false,
            showActive: false,
            showLeave: false,
            monitor: null,
            show: false,
            stayTime: seconds * 1000,
            timer: null,
            remainderText: '00:00:00',
            groupKey: '',
            firstTime: false,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'pendantResource', 'redPacketActive', 'bizConfig']),
        showDialog() {
            return this.showModal || this.showActive
        },
    },
    created() {
        if (!URLParams.carStyle) {
            return
        }
        let key
        switch (URLParams.kemu) {
            case 'kemu1':
                key = 'redPacketPopupConfig_ke1'
                break
        }
        if (key) {
            getActiveConfig({key}).then(data => {
                console.log(data.value)
                if (data.value) {
                    let config = JSON.parse(data.value)
                    config = (config && config[URLParams.carStyle]) || {}
                    this.groupKey = config.groupKey
                    this.stayTime = (config.stayTime || 40) * 1000
                    if (this.groupKey) {
                        this.getDetail()
                    }
                }
            })
        }
    },
    methods: {
        async getDetail() {
            // TODO 计算售卖价和日常价差价
            // if (this.pendantResource.goodsKey !== this.groupKey) return

            // const resData = await getSessionInfo({
            //     tiku: URLParams.carStyle,
            //     groupKey: this.groupKey,
            //     promotionActivityId: this.bizConfig.playStatus === 1 && this.roomDetail.promotionActivityId,
            // })
            // let k1 = this.defaultVipGoodsDetail
            // let k2 = resData
            // let redPacketPrice
            // let originalPrice
            // if (k1.price > k2.price) {
            //     redPacketPrice = k1.price - k2.price
            //     originalPrice = k1.price
            // } else if (k2.originalPrice > k2.price) {
            //     redPacketPrice = k2.originalPrice - k2.price
            //     originalPrice = k2.originalPrice
            // }
            // if (redPacketPrice) {
            //     this.activeVipGoodsDetail = resData
            //     this.redPacketPrice = redPacketPrice
            //     this.originalPrice = originalPrice
            //     let date = +new Date()
            //     let actObj = localStorage.getItem(localDataKey)
            //     actObj = JSON.parse(actObj || '{}')
            //     actObj = actObj[URLParams.carStyle]
            //     if (actObj) {
            //         if (actObj.expiryDate > date) {
            //             this.showMotion = true
            //             this.startTask()
            //             this.startTimer(actObj.expiryDate)
            //             // this.updateInfo()
            //         } else if (actObj.endTime < date) {
            //             this.startMonitor()
            //         }
            //     } else {
            //         this.startMonitor()
            //     }
            // }
        },
        start() {
            let date = +new Date()
            let expiryDate = date + hour1
            let actObj = localStorage.getItem(localDataKey)
            actObj = JSON.parse(actObj || '{}')
            actObj[URLParams.carStyle] = {
                endTime: date + day1,
                expiryDate: expiryDate,
            }
            localStorage.setItem(localDataKey, JSON.stringify(actObj))
            this.startTimer(expiryDate)
            this.firstTime = true
            // this.updateInfo(true)
        },
        endActive() {
            this.$store.commit('setRedPacketActive', {})
        },
        startTask() {
            this.taskList.push({
                _uid: this._uid,
                priority: 108,
                callback: () => {
                    this.showLeave = true

                    let fragmentName1 = '福利红包挽留弹窗'
                    let actionType = '出现'

                    // v8.17.0-埋点文档
                    // 精品课直播间页_福利红包挽留弹窗_出现
                    trackEvent({fragmentName1, actionType})
                },
            })
        },
        updateInfo() {
            this.$store.commit('setRedPacketActive', {
                redPacketPrice: this.redPacketPrice,
                originalPrice: this.originalPrice,
                firstTime: this.firstTime,
            })
            if (this.firstTime) this.firstTime = false
            // todo vip购买
        },
        startTimer(endTime) {
            clearInterval(this.timer)
            this.timer = null

            let date = +new Date()
            let remainder = endTime - date
            if (remainder < 0) {
                return
            }
            this.timer = setInterval(() => {
                let date = +new Date()
                let remainder = endTime - date
                this.remainderText = formatRemainTime(remainder, 'hh:mm:ss')
                if (remainder <= 0) {
                    this.endActive()
                    clearInterval(this.timer)
                    this.timer = null
                }
            }, 16)
        },
        startMonitor() {
            clearInterval(this.monitor)
            let date = +new Date()
            this.monitor = setInterval(() => {
                let now = +new Date()
                let spend = now - date
                date = now
                this.stayTime -= spend
                if (this.stayTime <= 0) {
                    clearInterval(this.monitor)
                    this.showMotion = true
                    this.startTask()
                    this.start()
                    console.log('waitPupopList', 'redpacket')
                    this.waitPupopList.push({
                        callback: () => {
                            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                                this.showActive = true
                            })

                            let fragmentName1 = '福利红包弹窗'
                            let actionType = '出现'

                            // 埋点梳理-驾考宝典-220210
                            // 精品课直播间页_福利红包弹窗_出现
                            trackEvent({fragmentName1, actionType})
                        },
                    })
                }
            }, 16)
        },
        openActive() {
            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                this.showModal = true
            })

            let actionName = '福利红包icon'
            let actionType = '点击'

            // 埋点梳理-驾考宝典-1224
            // 精品课直播间页_点击福利红包icon
            trackEvent({actionName, actionType})
        },
        open() {
            this.showModal = true
            this.showActive = false

            let fragmentName1 = '福利红包弹窗'
            let actionName = '开'
            let actionType = '点击'

            // 埋点梳理-驾考宝典-220210
            // 精品课直播间页_福利红包弹窗_点击开
            trackEvent({fragmentName1, actionName, actionType})
        },
        goPay1() {
            this.close()
            this.updateInfo()
            this.$EventBus.$emit('openDefaultMotion', '福利红包弹窗')

            let fragmentName1 = '福利红包弹窗'
            let fragmentName2 = '已开启'
            let actionName = '立即使用'
            let actionType = '点击'

            // 埋点梳理-驾考宝典-220210
            // 精品课直播间页_福利红包弹窗_已开启_点击立即使用
            trackEvent({fragmentName1, fragmentName2, actionName, actionType, payPathType: -1})
        },
        goPay2() {
            this.close()
            this.updateInfo()
            this.$EventBus.$emit('openDefaultMotion', '福利红包弹窗')

            let fragmentName1 = '福利红包挽留弹窗'
            let actionType = '点击'
            let actionName = '去支付'

            // v8.17.0-埋点文档
            // 精品课直播间页_福利红包挽留弹窗_点击去支付
            trackEvent({fragmentName1, actionType, actionName, payPathType: 0})
        },
        close() {
            this.showModal = false
            this.showActive = false
            this.showLeave = false
        },
        webClose() {
            this.$EventBus.$emit('webClose')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.motion {
    .poster {
        width: 100px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 100%;
    }
}
.modal-box {
    .content {
        width: 672px;
        height: 770px;
        background: url(../assets/images/coupon4.png) no-repeat center center;
        background-size: 100% auto;
        padding-top: 198px;
        text-align: center;
        .coupon {
            height: 340px;
        }
        .sum {
            color: #ff133b;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            padding: 20px 0;
            .s1 {
                padding-right: 5px;
                .fontSizeWithElder(140px);
                line-height: 1;
            }
            .s2 {
                .fontSizeWithElder(80px);
                line-height: 1.4;
            }
        }
        .name {
            padding-top: 4px;
            .fontSizeWithElder(34px);
            color: #464646;
            span {
                color: #c57101;
            }
        }
        .timer {
            .fontSizeWithElder(26px);
            color: #fde9ce;
            span {
                font-weight: bold;
                .fontSizeWithElder(28px);
            }
        }
        .btn {
            display: flex;
            justify-content: space-around;
            padding: 30px 60px 0 70px;
        }
        .b1 {
            .fontSizeWithElder(32px);
            width: 200px;
            height: 90px;
            border-radius: 45px;
            border: 1px solid rgba(253, 233, 206, 0.6);
            color: #fde9ce;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .b2 {
            width: 304px;
            height: 116px;
            background: url(../assets/images/coupon-popup-btn.png) no-repeat center center;
            background-size: 100% auto;
            .fontSizeWithElder(38px);
            font-weight: bold;
            color: #ed0026;
            padding-bottom: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
.active-box {
    .content {
        width: 630px;
        height: 670px;
        background: url(../assets/images/coupon3.gif) no-repeat center center;
        background-size: 100% auto;
        padding-top: 148px;
        text-align: center;
        .text {
            .fontSizeWithElder(32px);
            color: #f6ca92;
            height: 110px;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
                margin-right: 12px;
                width: 56px;
                border-radius: 100%;
                border: 1px solid #fcc694;
            }
        }
        .btn {
            margin: 180px auto 0;
            width: 200px;
            height: 180px;
        }
    }
}

.model-leave {
    .content {
        border-radius: 40px;
        width: 600px;
        height: 692px;
        background: url(../assets/images/<EMAIL>) no-repeat;
        background-size: 100% 100%;
        padding: 65px 0;
    }
    .title {
        height: 48px;
        margin: 0 auto 10px;
        font-size: 50px;
        font-weight: bold;
        text-align: center;
        color: #4e1c0e;
        line-height: 48px;
        span {
            color: #ff2c20;
        }
    }
    p {
        font-size: 28px;
        text-align: center;
        color: #6f2117;
        margin-top: 25px;
    }
    .coupon {
        width: 450px;
        height: 286px;
        margin: 55px auto 0;
        background: url(../assets/images/red-bg.png) no-repeat;
        background-size: cover;
        padding-top: 88px;
        font-size: 54px;
        color: #ffffff;
        position: relative;
        .prize {
            position: absolute;
            left: 111px;
        }
        .number {
            font-weight: bold;
            text-align: center;
            span {
                font-size: 76px;
            }
        }
        .original {
            width: 200px;
            height: 42px;
            line-height: 42px;
            background: linear-gradient(298deg, #fadd88 8%, #ffe5c1 84%);
            border-radius: 22px;
            font-size: 26px;
            text-align: center;
            color: #6f2117;
        }
    }
    .btns {
        display: flex;
        padding: 0 30px;
        justify-content: space-around;
        margin-top: 26px;
        .leave {
            width: 240px;
            height: 88px;
            border: 1px solid #fc5977;
            border-radius: 46px;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
            color: #fa0431;
            line-height: 88px;
        }
        .confirm {
            width: 240px;
            height: 88px;
            background: linear-gradient(307deg, #fb2b35 11%, #fd5a3e);
            line-height: 88px;
            border-radius: 46px;
            font-size: 32px;
            text-align: center;
            color: #ffffff;
        }
    }
}
.landscape /deep/ {
    // landscape
    .motion {
        margin-left: 15px;
        .poster {
            width: 72px;
        }
    }
}

.portrait,
.vertical {
    .motion {
        margin-left: 20px;
        .poster {
            width: 72px;
        }
    }
}
</style>
