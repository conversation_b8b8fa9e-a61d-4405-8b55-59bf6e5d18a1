<template>
    <div class="box">
        <p class="text padding">{{ text }}</p>
        <p class="text backup">{{ text }}</p>
    </div>
</template>

<script>
export default {
    props: {
        text: String,
    },
    methods: {
        startScroll() {
            let box = this.$el
            let text1 = this.$el.querySelector('.padding')
            let text2 = this.$el.querySelector('.backup')
            let [textWidth, boxWidth] = [text2.offsetWidth, box.offsetWidth]
            function checkScrollLeft() {
                // 判断文字长度是否大于盒子长度
                if (boxWidth > textWidth) {
                    return false
                }
                textWidth = text1.offsetWidth
                toScrollLeft()
            }
            function toScrollLeft() {
                //  如果文字长度大于滚动条距离，则递归拖动
                if (textWidth > box.scrollLeft) {
                    box.scrollLeft++
                    setTimeout(toScrollLeft, 18)
                } else {
                    box.scrollLeft = 0
                    setTimeout(checkScrollLeft, 180000)
                }
            }
            checkScrollLeft()
        },
    },
}
</script>

<style lang="less" scoped>
.box {
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    p {
        display: inline-block;
    }
    p.padding {
        padding-right: 100%;
    }
}
</style>
