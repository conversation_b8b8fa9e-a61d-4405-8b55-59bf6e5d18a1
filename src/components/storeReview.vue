<template>
    <div></div>
</template>

<script>
import {mapState} from 'vuex'
import {MCProtocol} from '@simplex/simple-base'
import {
    trackEvent,
    isIOS,
    getData,
    saveData,
    isAndroid,
    compareVersionComplex,
    webOpen,
} from '../utils/tools'
let localDataKey = 'jiaokaobaodian-showCommentGuideDialog-date'
let intervalTime = 7 * 24 * 60 * 60 * 1000

export default {
    data() {
        let seconds = 5 * 60
        return {
            todayEncourageUsed: true,
            seconds,
            stayTime: seconds * 1000,
        }
    },
    computed: {
        ...mapState(['isLandscape']),
    },
    mounted() {
        clearInterval(this.monitor)
        let date = +new Date()
        this.monitor = setInterval(() => {
            let now = +new Date()
            let spend = now - date
            date = now
            this.stayTime -= spend
            if (this.stayTime <= 0) {
                console.log('storeReview')
                clearInterval(this.monitor)
                this.storeReview()
            }
        }, 16)
    },
    methods: {
        storeReview() {
            if (isIOS) {
                MCProtocol.core.native.storeReview({
                    callback: data => {
                        console.log('storeReview', data)
                        if (data.success) {
                            let actionType = '请求'
                            let actionName = '系统评分'

                            // 埋点梳理-驾考宝典-220210
                            // 精品课直播间页_请求系统评分
                            trackEvent({actionType, actionName})
                        }
                    },
                })
            } else if (compareVersionComplex('8.19.0') >= 0 && isAndroid && !this.isLandscape) {
                getData(localDataKey).then(data => {
                    let date = data && data.date
                    if (!date || intervalTime < new Date().getTime() - date) {
                        webOpen({
                            url: 'http://jiakao.nav.mucang.cn/showCommentGuideDialog',
                        })

                        saveData({
                            key: localDataKey,
                            value: JSON.stringify({date: new Date().getTime()}),
                        })
                    }
                })
            }
        },
    },
}
</script>
