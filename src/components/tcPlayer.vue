<template>
    <div
        class="vjs-home"
        @touchend.capture="playToggle"
        :class="[
            bizConfig.playStatus === 1 && (isMucang || roomDetail.orientation === 1) ? 'live' : 'vod',
            {
                'with-advert-img': bizConfig.motionVisible,
                'notshow-fullscreen': !isShowFullScreen,
                'with-quick-comment': bizConfig.quickCommentVisible,
                'warmup': bizConfig.playStatus === 3,
                'contain-mode': videoContainMode
            },
        ]"
    >
        <div class="inv" ref="vPlayer" :key="videoKey">
            <video id="J_vjsPlayer" class="tc-player"></video>
        </div>
        <div class="por-no" v-if="showPor">备案号：{{ roomDetail.porNo }}</div>

        <popup
            class="error-popup"
            :position="'center'"
            :show.sync="errorVisible"
            :closeOnBlank="false"
            :showClose="false"
        >
            <div class="wrap">
                <div>
                    检测到当前直播间视频流加载异常，<br>5秒后将为您自动刷新直播间。
                </div>
                <div class="btns">
                    <div class="btn1" @click="leaveImmediately">立即刷新</div>
                    <div class="btn2" @click="exit">退出直播间</div>
                </div>
            </div>
        </popup>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {MCProtocol} from '@simplex/simple-base'
import {
    addClass,
    removeClass,
    loadScript,
    loadStyle,
    getMapfromArray,
    isAndroid,
    isIOS,
    isHarmony,
    isMucang,
    webOpen,
    trackEvent,
    isVIVO,
    isOppo,
    androidVersion,
    getUrl,
} from '../utils/tools'
import {liveRoomURL} from '../utils/constant'
import videoMixin from '../utils/videoMixin'
import popup from '../components/dialog'

let poster =
    'https://jiakao-web.mc-cdn.cn/jiakao-web/2021/12/13/17/e044a4219e2a4cf09b56cd6a82631466.png'
let fullscreenBtn = null
let danmuBtn = null
let player
let commonOptions = {
    playsinline: true,
    poster,
    width: '100%',
    height: '100%',
    autoplay: true,
    language: 'zh-cn',
}
let liveSkinLayout = {
    controlBar: {
        playToggle: false, // 是否显示播放、暂停切换按钮。
        progressControl: false, // 是否显示播放进度条。
        volumePanel: false, // 是否显示音量控制。
        currentTimeDisplay: false, // 是否显示视频当前时间。
        durationDisplay: false, // 是否显示视频时长。
        timeDivider: false, // 是否显示时间分割符。
        playbackRateMenuButton: false, // 是否显示播放速率选择按钮。
        fullscreenToggle: !isMucang, // 是否显示全屏按钮。
        QualitySwitcherMenuButton: false, // 是否显示清晰度切换菜单。
    },
}
let vodSkinLayout = {
    controlBar: {
        playToggle: true, // 是否显示播放、暂停切换按钮。
        progressControl: true, // 是否显示播放进度条。
        volumePanel: false, // 是否显示音量控制。
        currentTimeDisplay: true, // 是否显示视频当前时间。
        durationDisplay: true, // 是否显示视频时长。
        timeDivider: true, // 是否显示时间分割符。
        playbackRateMenuButton: true, // 是否显示播放速率选择按钮。
        fullscreenToggle: !isMucang, // 是否显示全屏按钮。
        QualitySwitcherMenuButton: false, // 是否显示清晰度切换菜单。
    },
}

let visibleCallback = null
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        visibleCallback && visibleCallback()
    }
})
let sdkLoading = false
let sdkReady = false
let sdkCallbacks = []
const getSdk = async () => {
    if (sdkReady) return Promise.resolve()
    if (sdkLoading) {
        return new Promise(resolve => {
            sdkCallbacks.push(() => {
                resolve()
            })
        })
    }
    sdkLoading = true
    new Promise(async resolve => {
        await loadScript(
            'https://web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/libs/hls.min.1.1.6.js'
        )
        await loadScript(
            'https://web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/tcplayer.v4.8.0.min.js'
        )
        resolve()
    }).then(() => {
        sdkLoading = false
        sdkReady = true
        sdkCallbacks.forEach(cb => {
            cb && cb()
        })
    })

    return new Promise(resolve => {
        sdkCallbacks.push(() => {
            resolve()
        })
    })
}

function insertBefore(newNode, referenceNode) {
    try {
        referenceNode.parentNode.insertBefore(newNode, referenceNode)
    } catch (error) {}
}
const useBackup = () => {
    // vivo oppo 安卓11设备 竖屏推流 有一定机率播webrtc失败，并且不会降级，所以这里手动降级
    return isAndroid && (isVIVO() || isOppo()) && Math.floor(androidVersion) === 11
}

export default {
    mixins: [videoMixin],
    components: {popup},
    props: {
        isShowFullScreen: Boolean,
    },
    data() {
        return {
            isMucang,
            poster,
            errorVisible: false,
            leaveTimer: null,
            videoKey: 0,
        }
    },
    watch: {
        isLandscape() {
            this.updateLandscapeIcon()
        },
        isShowDanmu() {
            this.updateDanmuIcon()
        },
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'isLandscape', 'isShowDanmu']),
        liveSource() {
            let _useBackup = (this.roomDetail.orientation === 1 && useBackup()) || isHarmony
            let sourceMap = this.pullStreamUrls.map(item => {
                let src = _useBackup ? item.backupUrl : item.url
                if (item.liveKey) {
                    src = getUrl(src, {token: item.liveKey})
                }
                return {
                    key: item.definition.toUpperCase(),
                    value: [{src}],
                }
            })
            return getMapfromArray(sourceMap)
        },
    },
    beforeDestroy() {
        this.dispose()
    },
    created() {
        /* global TCPlayer */
        loadStyle('https://web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/tcplayer.min.css')
        if (this.roomDetail.orientation === 1) {
            this.poster = commonOptions.poster =
                'https://jiakao-web.mc-cdn.cn/jiakao-web/2023/03/21/16/6a639943cd2b454396a247d9b09d42fb.png'
        }
        if (this.roomDetail.cover) {
            this.poster = commonOptions.poster = this.roomDetail.cover
        }
    },
    mounted() {
        pageSwitch.onPageShow(() => {
            if (isAndroid && isMucang) {
                this.initPlayer()
            } else {
                player.play()
            }
            console.log('play')
        })
        pageSwitch.onPageHide(() => {
            if (isAndroid && isMucang) {
                this.dispose()
            } else {
                player.pause()
            }
            console.log('pause')
        })
    },
    methods: {
        startLeave() {
            this.errorVisible = true
            let now = +new Date()
            let end = now + (5 * 1000)
            this.leaveTimer = setInterval(() => {
                now = +new Date()
                if (now >= end) {
                    this.leaveImmediately()
                }
            }, 16)
        },
        leaveImmediately() {
            clearInterval(this.leaveTimer)
            this.leave()
        },
        leave() {
            webOpen({
                url: `${liveRoomURL}?anchorId=${this.roomDetail.anchorId}`,
                closeCurrent: 1,
            })

            let liveMode = ''
            if (this.bizConfig.playStatus === 1) {
                if (this.bizConfig.isRealLive) {
                    liveMode = '直播'
                } else {
                    liveMode = '录播'
                }
            } else if (this.bizConfig.playStatus === 2) {
                liveMode = '回放'
            }
            // 精品课直播间页_逻辑播放失败自动跳转
            trackEvent({
                actionName: '逻辑播放失败自动跳转',
                liveMode,
            })
        },
        exit() {
            MCProtocol.Core.Web.close()
        },
        playToggle(e) {
            let tagName = e.target.tagName.toLowerCase()
            if (
                tagName === 'video' &&
                this.roomDetail.orientation === 1 &&
                this.bizConfig.playStatus === 2
            ) {
                if (player.hasStarted_ && !player.paused()) {
                    player.pause()
                }
            }
        },
        updateLandscapeIcon() {
            if (!fullscreenBtn) return
            if (this.isLandscape) {
                addClass(fullscreenBtn, 'fullscreen')
            } else {
                removeClass(fullscreenBtn, 'fullscreen')
            }
        },
        updateDanmuIcon() {
            if (!danmuBtn) return
            if (this.isShowDanmu) {
                addClass(danmuBtn, 'open')
            } else {
                removeClass(danmuBtn, 'open')
            }
        },
        playerReady() {
            setTimeout(() => {
                addClass(this.$el, 'ready')
            }, 200)
            let hand = this.$el.querySelector('.vjs-audio-button')
            fullscreenBtn = document.createElement('div')
            danmuBtn = document.createElement('div')
            fullscreenBtn.className = 'custom-fullscreen-control vjs-control vjs-button custom'
            danmuBtn.className = 'custom-danmu-control vjs-control vjs-button custom'
            insertBefore(danmuBtn, hand)
            insertBefore(fullscreenBtn, hand)
            this.updateLandscapeIcon()
            this.updateDanmuIcon()
            fullscreenBtn.addEventListener('click', () => {
                this.$EventBus.$emit('toggleRrientation')
            })
            danmuBtn.addEventListener('click', () => {
                this.$EventBus.$emit('toggleDanmu')
            })
        },
        dispose() {
            return new Promise(resolve => {
                if (player) {
                    try {
                        player.dispose()
                    } catch (error) {}
                    player = null
                    this.empty()
                }
                setTimeout(() => {
                    resolve()
                }, 0)
            })
        },
        waitVisible() {
            return new Promise(resolve => {
                if (document.visibilityState === 'visible' || isIOS) {
                    resolve()
                } else {
                    visibleCallback = () => {
                        resolve()
                    }
                }
            })
        },
        empty() {
            // let newCont = document.createElement('video')
            // newCont.id = 'J_vjsPlayer'
            // newCont.className = 'tc-player'
            // newCont.poster = this.poster
            // this.$refs['vPlayer'].appendChild(newCont)
            this.videoKey = +new Date()
        },
        startLive(options = {}) {
            Promise.all([getSdk(), this.dispose(), this.waitVisible()]).then(() => {
                let opt = {
                    ...commonOptions,
                    ...liveSkinLayout,
                    multiResolution: {
                        sources: this.liveSource,
                    },
                    ...options,
                }
                console.log('startLive', opt)
                this.$EventBus.$emit('startPlay', true)
                player = TCPlayer('J_vjsPlayer', opt)
                player.ready(e => {
                    this.playerReady()
                    this.$store.commit('updateBizConfig', {
                        webrtcStream: false,
                    })
                })
                this.tcPlayerEvents()
            })
        },
        startVod(options) {
            Promise.all([getSdk(), this.dispose(), this.waitVisible()]).then(() => {
                let opt = {
                    ...commonOptions,
                    ...vodSkinLayout,
                    ...options,
                }
                console.log('startVod', opt)
                this.$EventBus.$emit('startPlay', false)
                player = TCPlayer('J_vjsPlayer', opt)
                player.ready(e => {
                    this.playerReady()
                })
                this.tcPlayerEvents()
            })
        },
        startPlayback(autoplay = true) {
            this.startVod({
                sources: {src: this.playbackSources[this.playbackIndex]},
                autoplay,
            })
        },
        startWarmup() {
            this.startVod({
                sources: [{src: this.roomDetail.warmUpVideo}],
                loop: true,
                controlBar: false,
            })
        },
        tcPlayerEvents() {
            player.on('play', e => {
                console.log('EVENT play', e)
            })
            player.on('playing', e => {
                console.log('EVENT playing', e)
            })
            player.on('loadstart', e => {
                console.log('EVENT loadstart', e)
            })
            player.on('loadedmetadata', e => {
                console.log('EVENT loadedmetadata', e)
            })
            player.on('loadeddata', e => {
                console.log('EVENT loadeddata', e)
            })
            // player.on('progress', e => {
            //     console.log('EVENT progress', e)
            // })
            player.on('canplay', e => {
                console.log('EVENT canplay', e)
            })
            player.on('canplaythrough', e => {
                console.log('EVENT canplaythrough', e)
            })
            player.on('error', e => {
                console.log('EVENT error', e)

                if (isMucang) {
                    player.dispose()
                    this.startLeave()
                }
            })
            player.on('pause', e => {
                console.log('EVENT pause', e)
            })
            // player.on('timeupdate', e => {
            //     console.log('EVENT timeupdate', e)
            // })
            player.on('waiting', e => {
                console.log('EVENT waiting', e)
            })
            player.on('ended', e => {
                console.log('EVENT ended', e)
                if (this.playbackIndex >= 0) {
                    if (this.playbackSources.length - 1 > this.playbackIndex) {
                        this.playbackIndex += 1
                        this.startPlayback()
                    } else {
                        this.playbackIndex = 0
                        this.startPlayback(false)
                    }
                }
            })
            player.on('webrtcevent', (event) => {
                console.log('EVENT webrtcevent', event.data.code)

                if (event.data.code === 1003) {
                    this.$store.commit('updateBizConfig', {
                        webrtcStream: true,
                    })
                }
            })
        },
    },
}
</script>
<style lang="less">
.landscape .vjs-home.with-advert-img {
    padding-bottom: 10%;
    .por-no {
        margin-bottom: 10%;
    }
}
.vjs-home {
    height: 100%;
    position: relative;
    .inv {
        height: 100%;
        position: relative;
    }
    .por-no {
        font-size: 20px;
        position: absolute;
        left: 20px;
        bottom: 10px;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0px 0 1px rgba(0, 0, 0, 0.3);
        pointer-events: none;
        z-index: 1;
    }
}
#J_vjsPlayer {
    z-index: 1;
    width: 100%;
    height: 100%;
}
body {
    .tc-player .vjs-live-display {
        &:before {
            // display: none;
            background-color: transparent;
        }
    }
    .vjs-poster {
        // background-color: transparent;
        background-size: cover;
    }
    .vjs-control-bar {
        display: none;
    }
    .ready .vjs-control-bar {
        display: flex;
    }
    // .live {
    //     .vjs-control-bar {
    //         flex-direction: row-reverse;
    //     }
    // }
    .notshow-fullscreen .custom-fullscreen-control {
        display: none;
    }
    &.vertical {
        .video-js {
            background: #1b1d22;
        }
        .contain-mode {
            .vjs-tech {
                object-fit: contain !important;
            }
        }
        .vjs-home:not(.warmup) {
            .vjs-tech {
                object-fit: cover;
            }
        }
        .vjs-home .por-no {
            left: 30px;
            bottom: 24px;
        }
        .vjs-control-bar {
            background: none;
        }
        .live .vjs-control-bar {
            left: -99999px;
            top: -99999px;
        }
        .vod {
            .vjs-paused.vjs-controls-enabled .vjs-big-play-button {
                display: block;
            }
            .vjs-play-control,
            .vjs-playback-rate,
            .vjs-custom-control-spacer {
                display: none;
            }
            .vjs-control-bar {
                height: 46px;
                margin-bottom: 136px;
                box-sizing: content-box;
                opacity: 1 !important;
                display: flex;
                align-items: center;
                padding-bottom: calc(constant(safe-area-inset-bottom) - 50px);
                padding-bottom: calc(env(safe-area-inset-bottom) - 50px);
            }
            .vjs-progress-control {
                position: relative;
                order: 1;
                top: 0;
                .vjs-progress-holder {
                    margin: 0 0 0 20px;
                }
            }
            .vjs-time-control {
                width: auto;
                order: 2;
                line-height: 46px;
            }
            .vjs-current-time {
                padding-left: 20px;
            }
            .vjs-duration {
                padding-right: 20px;
            }
            &.with-quick-comment .vjs-control-bar {
                margin-bottom: 226px;
            }
        }
        .vjs-error .vjs-error-display:before {
            top: 44%;
        }
        .vjs-errors-content-container {
            padding-top: 450px;
        }
        .vjs-errors-dialog .vjs-control.vjs-close-button {
            top: 20%;
        }
    }

    .tc-player .custom-danmu-control {
        display: none;
        background: url(../assets/images/<EMAIL>) no-repeat center;
        background-size: 48px 48px;
        .vjs-icon-placeholder,
        .vjs-control-text {
            display: none;
        }
        &.open {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    .tc-player .custom-fullscreen-control {
        background: url(../assets/images/<EMAIL>) no-repeat center;
        background-size: 48px 48px;
        .vjs-icon-placeholder,
        .vjs-control-text {
            display: none;
        }
        &.fullscreen {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    .minimize {
        .vjs-home {
            pointer-events: none;
        }
        .vjs-control-bar {
            display: none;
        }
    }
}
</style>
<style lang="less" scoped>
.error-popup {
    /deep/ .dialog {
        width: 580px;
    }
    /deep/ .dialog .close {
        background: url(../assets/images/<EMAIL>) no-repeat;
        background-size: 50px 50px;
    }

    .wrap {
        background-color: rgba(75, 75, 75, 0.8);
        border-radius: 26px;
        padding: 40px 50px 50px;
        color: #c9c9c9;
    }
    .btns {
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        margin-top: 32px;
    }
    .btn1 {
        margin: 30px auto 0;
        width: 200px;
        height: 60px;
        line-height: 60px;
        border-radius: 46px;
        font-size: 28px;
        text-align: center;
        color: #c9c9c9;
        border: 1px solid #c9c9c9;
    }
    .btn2 {
        margin: 10px auto 0;
        width: 200px;
        height: 60px;
        padding-bottom: 10px;
        line-height: 60px;
        font-size: 28px;
        text-align: center;
        color: #ababab;
    }
}
</style>
