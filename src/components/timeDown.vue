<template>
    <div class="time-wraper" :class="'time-wraper-' + theme">
        <span class="timer-num">{{ hour }}</span>
        <span class="timer-hour-unit" />
        <span class="timer-num">{{ min }}</span>
        <span class="timer-min-unit" />

        <span class="timer-num">{{ sec }}</span>
    </div>
</template>

<script>
export default {
    data() {
        return {
            hour: 0,
            min: 0,
            sec: 0,
        }
    },
    props: {
        time: Number,
        theme: String,
    },
    mounted() {
        setTimeout(() => {
            this.startCount(+this.time)
        }, 200)
    },

    methods: {
        startCount(leftTime) {
            clearInterval(this.timer)
            this.timer = setInterval(() => {
                if (leftTime > 0) {
                    this.getHMT(leftTime)
                    leftTime -= 1000
                } else {
                    clearInterval(this.timer)
                }
            }, 1000)
        },
        getHMT(leftTime) {
            var day = Math.floor(parseFloat(leftTime / (24 * 60 * 60 * 1000)))
            var hour = Math.floor(
                parseFloat((leftTime - day * 24 * 60 * 60 * 1000) / (60 * 60 * 1000))
            )
            var min = Math.floor(
                parseFloat(
                    (leftTime - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000) / (60 * 1000)
                )
            )
            var sec = Math.floor(
                parseFloat(
                    (leftTime -
                        day * 24 * 60 * 60 * 1000 -
                        hour * 60 * 60 * 1000 -
                        min * 60 * 1000) /
                        1000
                )
            )
            var nextSec = sec > 0 ? sec - 1 : 59

            if (hour < 10) {
                hour = '0' + hour
            }
            if (min < 10) {
                min = '0' + min
            }
            if (sec < 10) {
                sec = '0' + sec
            }
            if (nextSec < 10) {
                nextSec = '0' + nextSec
            }

            this.hour = hour
            this.min = min
            this.sec = sec
            this.nextSec = nextSec
        },
    },
}
</script>

<style lang="less" scoped>
.time-wraper {
    .timer-num {
        padding: 0 4px;
        height: 44px;
        line-height: 44px;
        background: #ffffff;
        border-radius: 8px;
        font-size: 30px;
        font-weight: 600;
        text-align: center;
        color: #f21f6b;
    }
    .timer-hour-unit,
    .timer-min-unit {
        height: 28px;
        line-height: 25px;
        margin: 0 10px;
    }
    .timer-hour-unit::after {
        content: ':';
    }
    .timer-min-unit::after {
        content: ':';
    }
}
.time-wraper-red {
    color: #fff090;
    .timer-num {
        padding: 0 4px;
        height: 44px;
        line-height: 44px;
        background: transparent;
        border-radius: 8px;
        font-size: 30px;
        font-weight: 600;
        text-align: center;
        color: #fff090;
    }
    .timer-hour-unit,
    .timer-min-unit {
        height: 28px;
        line-height: 25px;
        margin: 0;
    }
}
</style>
