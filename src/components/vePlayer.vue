<template>
    <div
        class="ve-home"
        @touchend.capture="playToggle"
        :class="[
            bizConfig.playStatus === 1 && (isMucang || roomDetail.orientation === 1) ? 'live' : 'vod',
            {
                'with-advert-img': bizConfig.motionVisible,
                'notshow-fullscreen': !isShowFullScreen,
                'with-quick-comment': bizConfig.quickCommentVisible,
                'warmup': bizConfig.playStatus === 3,
                'contain-mode': videoContainMode
            },
        ]"
    >
        <div id="J_vePlayer" class="ve-player" :key="videoKey"></div>
        <div class="por-no" v-if="showPor">备案号：{{ roomDetail.porNo }}</div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    addClass,
    removeClass,
    loadScript,
    loadStyle,
    isAndroid,
    isIOS,
    isHarmony,
    isMucang,
    isVIVO,
    isOppo,
    androidVersion,
    getAppUser,
} from '../utils/tools'
import videoMixin from '../utils/videoMixin'

let fullscreenBtn = null
let danmuBtn = null
let player
let commonOptions = {
    id: 'J_vePlayer',
    poster: 'https://jiakao-web.mc-cdn.cn/jiakao-web/2021/12/13/17/e044a4219e2a4cf09b56cd6a82631466.png',
    width: '100%',
    height: '100%',
    autoplay: true,
    autoplayMuted: false,
    pip: false,
    enableHlsMSE: true,
    HlsPlayer: true,
    closeVideoDblclick: true,
    fullscreen: {
        disable: isMucang,
    },
    hls: {
        fetchOptions: {
            referrerPolicy: 'strict-origin-when-cross-origin',
        },
    },
}

let visibleCallback = null
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        visibleCallback && visibleCallback()
    }
})
let sdkLoading = false
let sdkReady = false
let sdkCallbacks = []
let eventRegister = false
let isRTMSupported, isRTMSupportH264
const getSdk = () => {
    if (sdkReady) return Promise.resolve()
    if (sdkLoading) {
        return new Promise(resolve => {
            sdkCallbacks.push(() => {
                resolve()
            })
        })
    }
    sdkLoading = true
    loadScript('https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/1.13.1/index.min.js').then(async () => {
        VePlayer.setLicenseConfig({
            license: {
                sign: 'yHpnG4oR0WoFFaa/AgG+tc7AuTyXWZMD+zr3FIWxYjNkYncTNmItl5N58JTR0mjwO0Qw4p+7+P4AKvb5Aue7YJlI+FGJAjHGrcW3hRemei3JJKw9SDY7uPeEMQPQjDPifyVPucdv8NTB6nNWdEmVVqwA38Otd+okeLXd+Yr7cQxM8ITRzSoot+foYwOBYVI/UYO4nGj5/dnR8Wka4fK1rDkY0oScRy0FiChjF6rvxskFNsUvFy24mpcW0pacdLuRc7Bk+81/vhX941QRU7VKz5KEs+TRnqe/t0usLXuVGgRBkygDjKw+6bkQfjXdZKBKqZmCIxL5Qq9/Ri6RQ4G5mg==',
                content: '************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            },
        })
        isRTMSupported = VePlayer.isRTMSupported()
        isRTMSupportH264 = await VePlayer.isRTMSupportCodec('h264')
        sdkLoading = false
        sdkReady = true
        sdkCallbacks.forEach(cb => {
            cb && cb()
        })
    })

    return new Promise(resolve => {
        sdkCallbacks.push(() => {
            resolve()
        })
    })
}

function insertBefore(newNode, referenceNode) {
    try {
        referenceNode.parentNode.insertBefore(newNode, referenceNode)
    } catch (error) {}
}
const useBackup = () => {
    // vivo oppo 安卓11设备 竖屏推流 有一定机率播webrtc失败，并且不会降级，所以这里手动降级
    return isAndroid && (isVIVO() || isOppo()) && Math.floor(androidVersion) === 11
}

export default {
    mixins: [videoMixin],
    props: {
        isShowFullScreen: Boolean,
    },
    data() {
        return {
            isMucang,
            videoKey: 0,
        }
    },
    watch: {
        isLandscape() {
            this.updateLandscapeIcon()
        },
        isShowDanmu() {
            this.updateDanmuIcon()
        },
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'isLandscape', 'isShowDanmu']),
        liveSource() {
            let _useBackup = (this.roomDetail.orientation === 1 && useBackup()) || !(isRTMSupported && isRTMSupportH264) || isHarmony
            let sourceMap = this.pullStreamUrls.map(item => {
                let src = _useBackup ? item.backupUrl : item.url
                return {definition: item.definition, definitionTextKey: item.definition, url: src}
            })
            return sourceMap
        },
        liveBackSource() {
            let backSourceMap = this.pullStreamUrls.map(item => {
                return {definition: item.definition, definitionTextKey: item.definition, url: item.backupUrl}
            })
            return backSourceMap
        },
    },
    beforeDestroy() {
        this.dispose()
    },
    created() {
        /* global VePlayer */
        loadStyle('https://lf-unpkg.volccdn.com/obj/vcloudfe/sdk/@volcengine/veplayer/1.13.1/index.min.css')
        if (this.roomDetail.orientation === 1) {
            commonOptions.poster =
                'https://jiakao-web.mc-cdn.cn/jiakao-web/2023/03/21/16/6a639943cd2b454396a247d9b09d42fb.png'
        }
        if (this.roomDetail.cover) {
            commonOptions.poster = this.roomDetail.cover
        }
    },
    mounted() {
        pageSwitch.onPageShow(() => {
            if (isAndroid && isMucang) {
                this.initPlayer()
            } else {
                if (player?.player) {
                    player.player.play()
                }
            }
            console.log('play')
        })
        pageSwitch.onPageHide(() => {
            if (isAndroid && isMucang) {
                this.dispose()
            } else {
                if (player?.player) {
                    player.player.pause()
                }
            }
            console.log('pause')
        })
    },
    methods: {
        playToggle(e) {
            let tagName = e.target.tagName.toLowerCase()
            if (
                tagName === 'xg-trigger' &&
                this.roomDetail.orientation === 1 &&
                this.bizConfig.playStatus === 2
            ) {
                if (player.player.state === 6) {
                    player.player.pause()
                }
            }
        },
        updateLandscapeIcon() {
            if (!fullscreenBtn) return
            if (this.isLandscape) {
                addClass(fullscreenBtn, 'fullscreen')
            } else {
                removeClass(fullscreenBtn, 'fullscreen')
            }
        },
        updateDanmuIcon() {
            if (!danmuBtn) return
            if (this.isShowDanmu) {
                addClass(danmuBtn, 'open')
            } else {
                removeClass(danmuBtn, 'open')
            }
        },
        playerReady() {
            setTimeout(() => {
                addClass(this.$el, 'ready')
            }, 200)
            let hand = this.$el.querySelector('.xgplayer-volume')
            const parser = new DOMParser()
            const htmlString =
                '<div style="width: 20px; height: 40px; margin-right: 10px;"></div>'
            const domElement = parser.parseFromString(htmlString, 'text/html').body.firstChild
            fullscreenBtn = domElement.cloneNode()
            danmuBtn = domElement.cloneNode()
            fullscreenBtn.className = 'custom-fullscreen-btn'
            danmuBtn.className = 'custom-danmu-btn'
            insertBefore(danmuBtn, hand)
            insertBefore(fullscreenBtn, hand)
            this.updateLandscapeIcon()
            this.updateDanmuIcon()
            fullscreenBtn.addEventListener('click', () => {
                this.$EventBus.$emit('toggleRrientation')
            })
            danmuBtn.addEventListener('click', () => {
                this.$EventBus.$emit('toggleDanmu')
            })
        },
        dispose() {
            return new Promise(resolve => {
                if (player?.player) {
                    player.player.destroy()
                    this.empty()
                }
                setTimeout(() => {
                    resolve()
                }, 0)
            })
        },
        waitVisible() {
            return new Promise(resolve => {
                if (document.visibilityState === 'visible' || isIOS) {
                    resolve()
                } else {
                    visibleCallback = () => {
                        resolve()
                    }
                }
            })
        },
        empty() {
            // let contentEl = document.getElementById('J_vePlayer')
            // contentEl.innerHtml = ''
            // while (contentEl.firstChild) {
            //     contentEl.removeChild(contentEl.firstChild)
            // }
            // this.$el.removeChild(contentEl)
            // let newCont = document.createElement('div')
            // newCont.id = 'J_vePlayer'
            // this.$el.appendChild(newCont)
            this.videoKey = +new Date()
        },
        startLive(options = {}) {
            Promise.all([getSdk(), this.dispose(), this.waitVisible()]).then(() => {
                let opt = {
                    isLive: true,
                    ...commonOptions,
                    playList: this.liveSource,
                    rtm: {
                        backupURL: this.liveBackSource[0].url,
                        backupStreamType: 'hls',
                    },
                    ...options,
                    liveLogger: {
                        appId: 756337,
                        deviceId: getAppUser(),
                    },
                }
                console.log('startLive', opt)
                this.$EventBus.$emit('startPlay', true)
                player = new VePlayer(opt)
                player.on('ready', e => {
                    this.playerReady()
                    try {
                        let display = this.$el.querySelector('.xgplayer-play .xgplayer-icon')
                        display.innerText = '直播'
                        display.style.color = '#fff'
                    } catch (error) {}
                })
                this.vePlayerEvents()
            })
        },
        startVod(options) {
            Promise.all([getSdk(), this.dispose(), this.waitVisible()]).then(() => {
                let opt = {
                    ...commonOptions,
                    ...options,
                    vodLogOpts: {
                        line_app_id: 756337,
                        line_user_id: getAppUser(),
                    },
                }
                console.log('startVod', opt)
                this.$EventBus.$emit('startPlay', false)
                player = new VePlayer(opt)
                player.on('ready', e => {
                    this.playerReady()
                })
                this.vePlayerEvents()
            })
        },
        startPlayback() {
            this.startVod({
                url: this.playbackSources[this.playbackIndex],
            })
        },
        startWarmup() {
            this.startVod({
                url: this.roomDetail.warmUpVideo,
                loop: true,
                skinLayout: false,
            })
        },
        vePlayerEvents() {
            if (eventRegister) return
            eventRegister = true
            player.on('play_backup_change', e => {
                console.log('EVENT play_backup_change', e)
            })
            player.on('play_backup_change_finis', e => {
                console.log('EVENT play_backup_change_finis', e)
            })
            player.on('play_error_button_click', e => {
                console.log('EVENT play_error_button_click', e)
            })
            player.on('error', e => {
                console.log('EVENT error', e)
            })
            player.on('autoplay_was_prevente', e => {
                console.log('EVENT autoplay_was_prevente', e)
            })
            player.on('waiting', e => {
                console.log('EVENT waiting', e)
            })
        },
    },
}
</script>
<style lang="less">
.landscape .ve-home.with-advert-img {
    padding-bottom: 10%;
    .por-no {
        margin-bottom: 10%;
    }
}
.ve-home {
    height: 100%;
    position: relative;
    .inv {
        height: 100%;
        position: relative;
    }
    .por-no {
        font-size: 20px;
        position: absolute;
        left: 20px;
        bottom: 10px;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0px 0 1px rgba(0, 0, 0, 0.3);
        pointer-events: none;
        z-index: 1;
    }
    .xgplayer-volume {
        display: none;
    }
}
#J_vePlayer {
    z-index: 1;
    width: 100%;
    height: 100%;
    position: relative;
}
body {
    .ve-player .vjs-live-display {
        &:before {
            // display: none;
            background-color: transparent;
        }
    }
    // .xgplayer-poster {
    //     background-size: cover;
    // }
    .xgplayer-controls {
        display: none;
    }
    .ready .xgplayer-controls {
        display: block;
    }
    .xgplayer-refresh {
        display: none !important;
    }
    .notshow-fullscreen .custom-fullscreen-btn {
        display: none;
    }
    .xgplayer-mobile-playBackRate {
        display: none;
    }
    .xgplayer .xg-inner-controls {
        bottom: 0 !important;
    }
    .xgplayer .gradient {
        background-image: linear-gradient(rgba(0,0,0,0),rgba(0,0,0,0) 70%,rgba(0,0,0,.1) 80%,rgba(0,0,0,.22) 83%,rgba(0,0,0,.36))
    }
    &.vertical {
        .gradient {
            display: none !important;
        }
        .ve-player {
            background: #1b1d22;
        }
        .contain-mode {
            .xgplayer video {
                object-fit: contain !important;
            }
        }
        .ve-home:not(.warmup) {
            .xgplayer video {
                object-fit: cover;
            }
        }
        .ve-home .por-no {
            left: 30px;
            bottom: 24px;
        }
        .xgplayer-controls {
            background: none;
        }
        .live .xgplayer-controls {
            left: -99999px;
            top: -99999px;
        }
        .vod {
            .xgplayer .controls-autohide {
                pointer-events: auto;
                visibility: visible;
            }
            .xgplayer-play,
            .xg-right-grid {
                display: none;
            }
            .xgplayer-controls {
                margin-bottom: 116px;
                box-sizing: content-box;
                opacity: 1 !important;
                display: flex;
                align-items: center;
                padding-bottom: calc(constant(safe-area-inset-bottom) - 50px);
                padding-bottom: calc(env(safe-area-inset-bottom) - 50px);
            }
            &.with-quick-comment .xgplayer-controls {
                margin-bottom: 206px;
            }
        }
    }

    .ve-player .custom-danmu-btn {
        display: none;
        background: url(../assets/images/<EMAIL>) no-repeat center;
        background-size: 90% auto;
        &.open {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    .ve-player .custom-fullscreen-btn {
        background: url(../assets/images/<EMAIL>) no-repeat center;
        background-size: 90% auto;
        &.fullscreen {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    .minimize {
        .trigger {
            pointer-events: none;
        }
        .xgplayer-controls {
            display: none;
        }
    }
}
</style>
