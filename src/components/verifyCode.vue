<template>
    <div>
        <popup
            class="model-verify-code"
            :position="'center'"
            @closed="closed"
            :show.sync="visible"
            :bottomCloseIcon="true"
        >
            <div class="com-verify-conent">
                <div class="header">你好！请登录</div>
                <div class="com-verify-ipt">
                    <input
                        type="tel"
                        pattern="[0-9]*"
                        class="phone"
                        placeholder="请输入手机号"
                        maxlength="11"
                        ref="phone"
                        v-model="phone"
                        @blur="scrollToTop"
                    />
                </div>
                <div class="com-verify-ipt mt12">
                    <input
                        type="tel"
                        pattern="[0-9]*"
                        class="yzm"
                        placeholder="请输入验证码"
                        skip="true"
                        key="code"
                        maxlength="6"
                        ref="code"
                        v-model="code"
                        @blur="scrollToTop"
                    />
                    <div
                        class="djs"
                        :class="{disabeld: !(djs === -1 || djs === 0)}"
                        @click="onDjs"
                        :disabled="isDisabled"
                    >
                        {{
                            djs === -1 ? '获取验证码' : djs === 0 ? '重新获取' : `重新发送(${djs})`
                        }}
                    </div>
                </div>

                <div class="text">
                    <p>1.未注册用户会自动创建驾考宝典账号</p>
                    <p>
                        2.登录即同意
                        <a
                            href="https://laofuzi.kakamobi.com/protocol/protocol.html?_productCategory=jiakaobaodian&_product=%E9%A9%BE%E8%80%83%E5%AE%9D%E5%85%B8&_appName=jiakaobaodian&protocolKey=jkbdUserAgreement"
                            >《用户使用协议》</a
                        >和
                        <a
                            href="https://laofuzi.kakamobi.com/protocol/protocol.html?_productCategory=jiakaobaodian&_product=%E9%A9%BE%E8%80%83%E5%AE%9D%E5%85%B8&_appName=jiakaobaodian&protocolKey=jkbdPrivateAgreement"
                            >《隐私政策》</a
                        >
                    </p>
                </div>

                <div class="com-verify-btn" :class="{activied: phone && code}" @click="onSubmit">
                    确认
                </div>
            </div>
        </popup>
        <code126 :show.sync="code126Visible" @validateDone="sendSms" />
    </div>
</template>

<script>
import popup from './dialog'
import {toast} from '../utils/tools'
import {smsLogin, smsCheck} from '../server/login'
import code126 from './126Code'

export default {
    components: {code126, popup},
    data() {
        return {
            code126Visible: false,
            visible: false,
            toast: '',
            djs: -1,
            phone: '',
            code: '',
            isDisabled: false,
        }
    },
    props: {
        show: Boolean,
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    methods: {
        closed() {
            this.$emit('update:show', false)
        },
        close() {
            this.$emit('update:show', false)
        },
        getRandomR(t) {
            var e = Math.abs(parseInt(new Date().getTime() * Math.random() * 1e4)).toString()
            var o = 0
            var n = 0
            var i
            var g = function(t) {
                return function(e, o) {
                    var ist = o + e.length <= 0

                    return ist ? e : (t[o] || (t[o] = Array(o + 1).join(0))) + e
                }
            }

            for (o = 0, n = 0; n < e.length; n++) {
                o += parseInt(e[n])
            }

            i = g([])

            o += e.length
            o = i(o, 3 - o.toString().length)

            return t.toString() + e + o
        },
        async onDjs() {
            if (this.isDisabled) return
            if (!/(1[3-9]\d|999)\d{8}/.test(this.phone)) {
                toast('手机号码格式不正确')
                return
            }
            this.code126Visible = true
        },
        async sendSms(NECaptchaValidate) {
            if (NECaptchaValidate) {
                this.smsId = await smsCheck({
                    NECaptchaValidate: NECaptchaValidate,
                    phoneNumber: this.phone,
                })

                this.onDjs2()
            }
        },
        onDjs2() {
            this.isDisabled = true
            this.djs = 60

            var intervalId = setInterval(() => {
                var djs = this.djs

                djs--

                if (djs >= 0) {
                    this.djs = djs
                } else {
                    this.isDisabled = false
                    clearInterval(intervalId)
                }
            }, 1000)
        },
        async onSubmit() {
            var smsId = this.smsId
            var phone = this.phone
            var code = this.code

            if (!phone || !code) {
                return
            }

            if (!/(1[3-9]\d|999)\d{8}/.test(phone)) {
                toast('手机号码格式不正确')
                return
            }

            if (!smsId) {
                toast('请先发送验证码')

                return
            }

            if (!code) {
                toast('请填写验证码')

                return
            }
            await smsLogin(
                {
                    phoneNumber: phone,
                    smsCode: code,
                    smsId: smsId,
                },
                {showErrorInfo: true}
            )
            this.$emit('smsLoginSuccess')
            this.close()
        },
        scrollToTop() {
            window.scroll(0, 0)
        },
    },
}
</script>
<style lang="less" scoped>
.com-verify-conent {
    background: #fff;
    width: 630px;
    height: 600px;
    border-radius: 30px;
    padding: 32px 52px 48px;
    display: flex;
    flex-direction: column;

    .header {
        font-size: 36px;
        font-weight: bold;
        text-align: center;
    }

    .tip {
        font-size: 24px;
        color: #d23104;
        margin-top: 8px;
    }

    .com-verify-ipt {
        margin-top: 16px;
        display: flex;
        background-size: 40px 44px;
        background-position: left center;
        background-repeat: no-repeat;
        justify-content: space-between;
        width: 518px;
        height: 88px;
        border-radius: 4px;

        input {
            outline: none;
            border: none;
            background: #f7f7f7;
            font-size: 30px;
            padding-left: 24px;
        }

        .phone {
            width: 100%;
        }

        .yzm {
            width: 286px;
            height: 88px;
            border-radius: 4px;
            border: none;
        }

        .djs {
            width: 198px;
            height: 88px;
            border-radius: 4px;
            border: 1px solid #f4c3a9;
            font-size: 28px;
            font-weight: 400;
            color: #ed631c;
            line-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #ffeee6;
            border-radius: 8px;

            &[disabled] {
                color: #999;
            }
        }

        .disabeld {
            background: #f4f7f7;
            border: none !important;
            color: #999 !important;
        }
    }

    .text {
        margin-top: 30px;

        p {
            font-size: 24px;
            font-weight: 400;
            color: #9f5217;
            line-height: 34px;
            margin-bottom: 4px;

            a {
                font-size: 24px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #04a5ff;
                line-height: 34px;
            }
        }
    }

    .com-verify-btn {
        width: 498px;
        height: 80px;
        line-height: 80px;
        box-shadow: 0px 8px 24px -8px rgba(254, 89, 89, 0.5);
        background: linear-gradient(135deg, #fcc6a6 0%, #fda789 100%);
        border-radius: 50px;
        font-size: 32px;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
        margin-top: 32px;
    }

    .activied {
        background: linear-gradient(135deg, #ff7900 0%, #ff1c54 100%);
    }
}
</style>
