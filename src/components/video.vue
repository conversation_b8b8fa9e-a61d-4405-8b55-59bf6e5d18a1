<template>
    <div>
        <div class="player">
            <video
                id="my-video"
                class="video-js vjs-polyzor-skin"
                controls=""
                playsinline=""
                webkit-playsinline=""
                preload="auto"
                :poster="imgCover"
                data-setup=""
            >
                <source :src="flv" type="video/flv" />
                <source :src="rtmp" type="rtmp/flv" />
                <source :src="m3u8" type="application/x-mpegURL" />
            </video>
        </div>
    </div>
</template>

<script>
function loadScript(url) {
    let script = document.createElement('script')

    script.type = 'text/javascript'
    script.src = url
    script.async = false
    document.head.appendChild(script)
    return new Promise(resolve => {
        script.onload = function() {
            resolve()
        }
    })
}
function loadStyle(url) {
    let style = document.createElement('link')
    style.rel = 'stylesheet'
    style.href = url
    document.head.appendChild(style)
}
export default {
    data() {
        return {
            imgCover:
                'https://doc.shangzhibo.tv/resources/102920/10445642/3c5c7fae8409f2b76d7ba923344b404b.png',
            rtmp: '//pull.shangzhibo.tv/onelive/10445642-6JoRFW64P',
            flv: '//pull.shangzhibo.tv/onelive/10445642-6JoRFW64P.flv',
            m3u8: '//pull.shangzhibo.tv/onelive/10445642-6JoRFW64P.m3u8',
        }
    },
    props: {
        url: String,
    },
    mounted() {
        // ["flvjs", "html5"];
        // ["flash", "html5"]
        loadStyle('./static/videojs/video-js.min.css')
        Promise.all([
            loadScript('./static/videojs/video.min.js'),
            loadScript('./static/videojs/<EMAIL>'),
            loadScript('./static/videojs/video-flvjs.js'),
            loadScript('./static/videojs/videojs-contrib-hls.min.js'),
        ]).then(() => {
            /* global videojs */
            videojs('my-video', {
                techOrder: ['html5'],
                controlBar: {
                    playToggle: !1,
                    timeDivider: !1,
                    remainingTimeDisplay: !1,
                    progressControl: !1,
                    fullscreenToggle: !0,
                },
                flvjs: {
                    mediaDataSource: {
                        isLive: !0,
                        cors: !0,
                        withCredentials: !1,
                    },
                    config: {
                        isLive: !0,
                        enableStashBuffer: !1,
                    },
                },
            })
        })
    },
    methods: {},
}
</script>

<style lang="less">
.player {
    height: 420px;
}
video {
    box-shadow: 0 0 43px -3px #474747;
}
video:focus-visible {
    outline: none;
}
.video-js .vjs-big-play-button,
.video-js .vjs-play-control,
.vjs-polyzor-skin.video-js .vjs-icon-play {
    font-family: VideoJS;
    font-weight: 400;
    font-style: normal;
}
.vjs-polyzor-skin.video-js .vjs-big-play-button {
    line-height: 1;
    height: 1em;
    width: 1em;
    color: #fff;
    border: 0;
    transition: all 0.3s ease !important;
    left: 50%;
    top: 50%;
    margin-left: -0.5em;
    margin-top: -0.5em;
    border-radius: 2.5em;
    background-color: hsla(0, 0%, 100%, 0.6);
    font-size: 6em;
    color: #383838;
}
.video-js .vjs-big-play-button:focus,
.video-js:hover .vjs-big-play-button {
    outline: 0;
    background-color: hsla(0, 0%, 100%, 0.6);
    transform: scale(1.2);
}
.video-js .vjs-control-bar {
    z-index: 3;
    background-color: rgba(0, 0, 0, 0.6);
}
.vjs-polyzor-skin.video-js .vjs-button > .vjs-icon-placeholder:before {
    line-height: 1;
    position: relative;
    font-size: 2.8em;
}
.video-js {
    width: 100%;
    height: 100%;
}
.vjs-tech {
    top: -1px;
}
video {
    box-shadow: none;
}
.video-js .vjs-play-progress:after {
    padding: 0;
}
</style>
