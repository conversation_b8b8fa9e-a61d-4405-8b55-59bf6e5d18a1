<template>
    <div class="so-line nottorem">
        <div class="expand"></div>
        <div class="inline" :class="{bg: showBg}">
            <div class="image" @click.stop="clickShowcaseGoods()"><img :src="advertDetail.img" /></div>
        </div>

        <payPopup
            :key="advertDetail.icon + 'payPopup'"
            :advertDetail="advertDetail"
            :motionDetailProp="motionDetail"
            :show.sync="payPopupVisible"
            :fragmentName1="fragmentName1"
            :fullScreen="fullScreen"
        />
    </div>
</template>

<script>
import goodsMixin from '../utils/goodsMixin'
import payPopup from './payPopup'

export default {
    mixins: [goodsMixin],
    components: {payPopup},
    props: {
        advertDetail: Object,
        showBg: Boolean,
        fragmentName1: String,
    },
    data() {
        return {
            fullScreen: false,
            payPopupVisible: false,
            goodsDetail: {},
            motionDetail: {},
        }
    },
    async mounted() {
        this.motionDetail = await this.getAdvertDetail(this.advertDetail)
    },
    methods: {
        async clickShowcaseGoods() {
            this.popupMotion(this.advertDetail, this.motionDetail)
        },
    },
}
</script>

<style lang="less" scoped>
.so-line {
    .inline {
        height: 100%;
        overflow: hidden;
        display: flex;
        &.bg {
            background: url(../assets/images/crossband_bg.png) no-repeat;
            background-size: cover;
        }
    }
    .image {
        flex: 1;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        background-size: auto 100%;
        background-repeat: no-repeat;
        background-position: center center;
        img {
            height: 100%;
            width: auto;
        }
    }
}
.portrait /deep/ {
    // portrait
    .so-line {
        position: relative;
        height: 75px;
    }
}

.landscape /deep/ {
    .expand {
        padding-top: 10%;
    }
    // landscape
    .so-line {
        bottom: 0;
        left: 0;
        position: fixed;
        z-index: 1;
        .inline {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
    }

    @media (min-width: 689px) {
        .so-line {
            padding-left: constant(safe-area-inset-left);
            padding-left: env(safe-area-inset-left);
            box-sizing: content-box;
            &.nottorem {
                width: 540px;
            }
        }
    }

    /* 540+150=690 */
    @media (max-width: 690px) {
        .so-line {
            right: 4.5rem;
        }
    }
}
.vertical {
    .inline {
        background: transparent;
    }
}
</style>
