<template>
    <div
        id="J-video-container"
        :class="{settle: roomDetail.title}"
        class="video-container nottorem"
    >
        <slot name="default"></slot>
        <div id="J-no-video" class="no-video">该视频已下架</div>
        <aliPlayer
            v-if="roomDetail.streamOrigin === 1"
            :isShowFullScreen="isShowFullScreen"
        />
        <tcPlayer
            v-if="roomDetail.streamOrigin === 3"
            :isShowFullScreen="isShowFullScreen"
        />
        <vePlayer
            v-if="roomDetail.streamOrigin === 4"
            :isShowFullScreen="isShowFullScreen"
        />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {addClass, removeClass, convertPixelFromUI, trackEvent} from '../utils/tools'
import aliPlayer from './aliPlayer'
import tcPlayer from './tcPlayer'
import vePlayer from './vePlayer'

let iframeContainer = null
let fontSize = parseFloat(document.documentElement.style.fontSize)

export default {
    components: {
        aliPlayer,
        tcPlayer,
        vePlayer,
    },
    props: ['isShowFullScreen'],
    data() {
        let windowWidth = window.innerWidth
        console.log(document.documentElement.clientHeight, document.body.clientHeight)
        let windowHeight = document.body.clientHeight
        return {
            minimize: false,
            placeX: -1,
            placeY: -1,
            minPlaceX: 0,
            maxPlaceX: windowWidth - windowWidth * 0.45,
            minPlaceY: 0,
            maxPlaceY: windowHeight - convertPixelFromUI(420) * 0.45,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'isLandscape', 'isShowDanmu']),
        defaultX() {
            let defaultX = fontSize * 3.8
            if (this.roomDetail.orientation === 1) {
                defaultX = fontSize * 4.55
            }
            return parseFloat(localStorage.getItem('minimize_player_x')) || defaultX
        },
        defaultY() {
            let defaultY = fontSize * 2
            if (this.roomDetail.orientation === 1) {
                defaultY = fontSize * 1.2
            }
            return parseFloat(localStorage.getItem('minimize_player_y')) || fontSize || defaultY
        },
    },
    mounted() {
        iframeContainer = document.getElementById('J-video-container')
        iframeContainer.addEventListener('click', e => {
            this.$EventBus.$emit('quitMinimize')
        })
        this.$EventBus.$on('minimizePlayer', (minimize, data) => {
            this.minimize = minimize
            if (minimize) {
                addClass(iframeContainer, 'minimize')
                this.placeX = this.placeX > -1 ? this.placeX : this.defaultX
                this.placeY = this.placeY > -1 ? this.placeY : this.defaultY
                iframeContainer.style.left = this.placeX + 'px'
                iframeContainer.style.top = this.placeY + 'px'

                iframeContainer.ontouchstart = e => {
                    this.lastX = e.changedTouches[0].pageX
                    this.lastY = e.changedTouches[0].pageY
                }
                iframeContainer.ontouchmove = e => {
                    e.preventDefault()
                    e.stopPropagation()
                    let currentX = e.changedTouches[0].pageX
                    let currentY = e.changedTouches[0].pageY
                    let moveX = currentX - this.lastX
                    let moveY = currentY - this.lastY
                    this.move(moveX, moveY)
                    this.lastX = currentX
                    this.lastY = currentY
                }

                let fragmentName1 = '视频浮窗'
                let actionType = '出现'

                // 埋点梳理-驾考宝典-0819
                // 精品课直播间页_视频浮窗_出现
                trackEvent({
                    fragmentName1,
                    actionType,
                    groupKey: data.groupKey,
                    goodsUniqueKey: data.goodsUniqueKey,
                })
            } else {
                removeClass(iframeContainer, 'minimize')
                iframeContainer.style.left = 'initial'
                iframeContainer.style.top = 'initial'

                iframeContainer.ontouchstart = null
                iframeContainer.ontouchmove = null
            }
        })
    },
    methods: {
        move(moveX, moveY) {
            this.placeX = Math.min(Math.max(this.minPlaceX, this.placeX + moveX), this.maxPlaceX)
            this.placeY = Math.min(Math.max(this.minPlaceY, this.placeY + moveY), this.maxPlaceY)
            iframeContainer.style.left = this.placeX + 'px'
            iframeContainer.style.top = this.placeY + 'px'
            localStorage.setItem('minimize_player_x', this.placeX)
            localStorage.setItem('minimize_player_y', this.placeY)
        },
    },
}
</script>
<style lang="less">
.video-container {
    box-sizing: content-box;
    position: relative;
}
.video-container.minimize {
    transform: scale(0.45);
    position: fixed;
    transform-origin: left top;
    top: 1rem;
    left: 3.8rem;
    z-index: 6;
}
.vertical {
    .video-container {
        flex: 1;
        height: 100%;
        width: 100%;
    }
    .video-container.minimize {
        transform: scale(0.35);
    }
}

.video-container iframe {
    border: 0;
    position: relative;
}
.video-container.minimize iframe {
    pointer-events: none;
}
.video-container .no-video {
    width: 100%;
    height: 100%;
    background: url(../assets/images/<EMAIL>) no-repeat center 20% #000;
    background-size: 3.1rem 2rem;
    color: #fff;
    font-size: 0.28rem;
    text-align: center;
    padding-top: 34%;
    display: none;
    position: absolute;
}

.portrait .video-container {
    margin-top: 10px;
    height: 4.2rem;
    width: 100%;
    &.settle {
        background: #000;
    }
}

/* landscape */
.landscape .video-container {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
    box-sizing: content-box;
    flex-shrink: 0;
    overflow: hidden;
    &.settle {
        background: #000;
    }
}
.landscape .video-container .no-video {
    background-position: center 38%;
    padding-top: 39%;
}

@media (min-width: 689px) {
    .landscape .video-container {
        height: 100%;
        &.nottorem {
            width: 540px;
        }
    }
}
/* 540+150=690 */
@media (max-width: 690px) {
    .landscape .video-container {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        height: 100%;
    }
}
</style>
