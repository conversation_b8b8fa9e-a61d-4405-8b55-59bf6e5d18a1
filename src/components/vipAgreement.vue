<template>
    <div class="agreement" :class="theme">
        <template v-if="checkAgreement && showAgreement">
            <input
                class="regular-checkbox"
                id="agreement"
                :checked="finalReaded"
                @change="onChcek"
                type="checkbox"
            />
            <label class="regular-label" for="agreement"></label>
        </template>
        <label for="agreement"
            >{{type === 'training' ? '我已阅读并同意' : '开通前请先阅读'}}
            <span @click.prevent="openAgreement">《{{ agreementName }}》</span>
        </label>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {URLParams, webOpen} from '../utils/tools'
import {setReaded, setReaded2} from '../utils/agreementHelper'
import {agreementURL, agreementURL2} from '../utils/constant'

export default {
    data() {
        return {
        }
    },
    props: {
        theme: {
            type: String,
            default: 'blue',
        },
        showAgreement: {
            type: Boolean,
            default: true,
        },
        type: {
            type: String,
            default: 'vip',
        },
    },
    computed: {
        ...mapGetters(['checkAgreement', 'readed', 'readed2']),
        finalReaded() {
            if (this.type === 'training') {
                return this.readed2
            } else {
                return this.readed
            }
        },
        agreementName() {
            if (this.type === 'training') {
                return `驾考学习辅导服务协议`
            } else {
                return `${URLParams._product}会员协议`
            }
        },
    },
    watch: {
        readed(val) {
            setReaded(val)
        },
        readed2(val) {
            setReaded2(val)
        },
    },

    methods: {
        openAgreement() {
            if (this.type === 'training') {
                webOpen({
                    url: agreementURL2,
                    titleBar: true,
                    title: this.agreementName,
                })
            } else {
                webOpen({
                    url: agreementURL,
                    titleBar: true,
                    title: this.agreementName,
                })
            }
        },
        onChcek(e) {
            if (this.type === 'training') {
                this.$store.commit('updatePayConfig', {readed2: e.target.checked})
            } else {
                this.$store.commit('updatePayConfig', {readed: e.target.checked})
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.agreement {
    font-size: 22px;
    color: #333;
    display: flex;
    align-items: center;
    height: 30px;
    .regular-checkbox {
        display: none;
    }
    .regular-checkbox + .regular-label {
        width: 32px;
        height: 32px;
        display: block;
        margin-right: 6px;
    }
    &.blue {
        .regular-checkbox + .regular-label {
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
        span {
            color: #1dacf9;
        }
    }
    &.red {
        .regular-checkbox + .regular-label {
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
        span {
            color: #f25247;
        }
    }
    &.light {
        .regular-checkbox + .regular-label {
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
    }
    &.brown {
        .regular-checkbox + .regular-label {
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
        span {
            color: #ff822d;
        }
    }
    &.brown2 {
        .regular-checkbox + .regular-label {
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
        span {
            color: #f9a559;
        }
    }
    &.brown3 {
        .regular-checkbox + .regular-label {
            background: url(../assets/images/<EMAIL>) no-repeat center;
            background-size: 30px 30px;
        }
        .regular-checkbox:checked + .regular-label {
            background-image: url(../assets/images/<EMAIL>);
        }
        span {
            color: #984321;
        }
    }
}
</style>
