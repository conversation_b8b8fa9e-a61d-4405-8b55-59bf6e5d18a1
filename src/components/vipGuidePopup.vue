<template>
    <popup
        class="vip-guide-popup"
        :position="'bottom'"
        :show.sync="visible"
        @closed="closed"
        :isInDialog="isInDialog">
        <div class="wrap">
            <template v-if="textData.desc">
                <div class="kemu" :class="textData.kemu"></div>
                <div class="content">
                    <div class="title"></div>
                    <div class="desc">{{ textData.desc }}</div>
                    <div class="step-list">
                        <div class="item" v-for="(item, index) in textData.steps" :key="item.image">
                            <div class="num">第{{ index+1 }}步</div>
                            <div class="name" v-html="item.name" :class="{s: removeTags(item.name).length > 8}"></div>
                            <div class="image"><img :src="item.image" ></div>
                        </div>
                    </div>
                    <div class="btn-wrap">
                        <div class="btn" @click="goUse">
                            去学习
                        </div>
                        <span class="btn-icon"></span>
                    </div>
                    <div class="time-down" @click="stopTimer" v-if="textData.isAutoUse && timeing">取消（{{ timeing }}S）</div>
                </div>
            </template>
            <loading v-else-if="loading" bgColor="#666" />
            <div v-else class="no-data">暂无数据</div>
        </div>
    </popup>
</template>

<script>
import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {URLParams, openNewVip, trackEvent} from '../utils/tools'
import popup from './dialog'
import loading from './loading'
import {webClose} from '../utils/jump'
import {VIP_BUYED_URL} from '../utils/constant'
import k1$1 from '../assets/images/vip-guide/<EMAIL>'
import k1k4$2 from '../assets/images/vip-guide/<EMAIL>'
import k1$3 from '../assets/images/vip-guide/<EMAIL>'
import k1k4$4 from '../assets/images/vip-guide/<EMAIL>'
import k2$1 from '../assets/images/vip-guide/<EMAIL>'
import k2$2 from '../assets/images/vip-guide/<EMAIL>'
import k2$3 from '../assets/images/vip-guide/<EMAIL>'
import k2$4 from '../assets/images/vip-guide/<EMAIL>'
import k3$1 from '../assets/images/vip-guide/<EMAIL>'
import k3$2 from '../assets/images/vip-guide/<EMAIL>'
import k3$3 from '../assets/images/vip-guide/<EMAIL>'
import k3$4 from '../assets/images/vip-guide/<EMAIL>'
import k4$1 from '../assets/images/vip-guide/<EMAIL>'
import k4$3 from '../assets/images/vip-guide/<EMAIL>'
import bk1$1 from '../assets/images/vip-guide/<EMAIL>'
import bk1$2 from '../assets/images/vip-guide/<EMAIL>'
import bk4$1 from '../assets/images/vip-guide/<EMAIL>'
import bk4$2 from '../assets/images/vip-guide/<EMAIL>'
import tk1$1 from '../assets/images/vip-guide/<EMAIL>'
import tk1$2 from '../assets/images/vip-guide/<EMAIL>'
import tk4$1 from '../assets/images/vip-guide/<EMAIL>'
import tk4$2 from '../assets/images/vip-guide/<EMAIL>'
import mk1$1 from '../assets/images/vip-guide/<EMAIL>'
import mk1$2 from '../assets/images/vip-guide/<EMAIL>'
import mk4$1 from '../assets/images/vip-guide/<EMAIL>'
import mk4$2 from '../assets/images/vip-guide/<EMAIL>'
import mf$1 from '../assets/images/vip-guide/<EMAIL>'
import mf$2 from '../assets/images/vip-guide/<EMAIL>'
import mmf$2 from '../assets/images/vip-guide/<EMAIL>'

let desc1 = '1听2做3考4复习'
let desc2 = '专项攻克 实操特训'

let k1 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: k1$1,
    }, {
        name: '<b>做</b>精简500题',
        image: k1k4$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k1$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu1',
}
let k2 = {
    desc: desc2,
    steps: [{
        name: '看五大项教学视频',
        image: k2$1,
    }, {
        name: '看场地实操讲解视频',
        image: k2$2,
    }, {
        name: '3D模拟练车',
        image: k2$3,
    }, {
        name: '3D真实模拟考场演练',
        image: k2$4,
    }],
    isAutoUse: false,
    kemu: 'kemu2',
}
let k3 = {
    desc: desc2,
    steps: [{
        name: '看真实路线实拍视频',
        image: k3$1,
    }, {
        name: '3D模拟练车',
        image: k3$2,
    }, {
        name: '3D真实路线互动练习',
        image: k3$3,
    }, {
        name: '3D真实模拟考场演练',
        image: k3$4,
    }],
    isAutoUse: false,
    kemu: 'kemu3',
}
let k4 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: k4$1,
    }, {
        name: '<b>做</b>精简500题',
        image: k1k4$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k4$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu4',
}
let bk1 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: bk1$1,
    }, {
        name: '<b>做</b>精简600题',
        image: bk1$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k1$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu1',
}
let bk4 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: bk4$1,
    }, {
        name: '<b>做</b>精简600题',
        image: bk4$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k4$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu4',
}
let tk1 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: tk1$1,
    }, {
        name: '<b>做</b>精简600题',
        image: tk1$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k1$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu1',
}
let tk4 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: tk4$1,
    }, {
        name: '<b>做</b>精简600题',
        image: tk4$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k4$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu4',
}
let mk1 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>摩托车精华课',
        image: mk1$1,
    }, {
        name: '<b>做</b>精简120题',
        image: mk1$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k1$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu1',
}
let mk4 = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>摩托车精华课',
        image: mk4$1,
    }, {
        name: '<b>做</b>精简100题',
        image: mk4$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k4$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'kemu4',
}
let mf = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: mf$1,
    }, {
        name: '<b>做</b>精简600题',
        image: mf$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k1$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'manfen',
}
let mmf = {
    desc: desc1,
    steps: [{
        name: '<b>听</b>3小时精华课',
        image: mf$1,
    }, {
        name: '<b>做</b>精简200题',
        image: mmf$2,
    }, {
        name: '真实模拟<b>考</b>场演练',
        image: k1$3,
    }, {
        name: '宝典秘卷强化<b>复习</b>',
        image: k1k4$4,
    }],
    isAutoUse: true,
    kemu: 'manfen',
}

export default {
    components: {
        popup,
        loading,
    },
    data() {
        let {sceneCode, kemuNum, carStyle} = URLParams
        let textData = {}
        if (carStyle === 'car' || carStyle === 'bus' || carStyle === 'truck' || carStyle === 'moto') {
            if (sceneCode === '102') {
                if (carStyle === 'moto') {
                    textData = mmf
                } else {
                    textData = mf
                }
            } else if (carStyle === 'moto') {
                if (+kemuNum === 1) {
                    textData = mk1
                } else if (+kemuNum === 4) {
                    textData = mk4
                }
            } else if (carStyle === 'bus') {
                if (+kemuNum === 1) {
                    textData = bk1
                } else if (+kemuNum === 4) {
                    textData = bk4
                }
            } else if (carStyle === 'truck') {
                if (+kemuNum === 1) {
                    textData = tk1
                } else if (+kemuNum === 4) {
                    textData = tk4
                }
            } else if (carStyle === 'car') {
                if (+kemuNum === 1) {
                    textData = k1
                } else if (+kemuNum === 2) {
                    textData = k2
                } else if (+kemuNum === 3) {
                    textData = k3
                } else if (+kemuNum === 4) {
                    textData = k4
                }
            }
        }
        return {
            visible: false,
            loading: false,
            textData,
            timer: null,
            timeing: 0,
        }
    },
    props: {
        show: Boolean,
        isInDialog: Boolean,
    },
    watch: {
        async show(val) {
            this.visible = val
            if (val) {
                if (this.textData.isAutoUse) {
                    this.startTimer(10)
                }

                let fragmentName1 = 'VIP使用引导弹窗'
                let actionType = '出现'

                // 精品课直播间页_VIP使用引导弹窗_出现
                trackEvent({
                    fragmentName1,
                    actionType,
                    payStatus: 1,
                })
            }
        },
    },
    mounted() {
        pageSwitch.onPageShow(() => {
            if (this.show && this.timeing) {
                this.startTimer(this.timeing)
            }
        })
        pageSwitch.onPageHide(() => {
            if (this.timeing) {
                this.stopTimer()
            }
        })
    },
    methods: {
        closed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.close()
            }
            this.stopTimer()

            let fragmentName1 = 'VIP使用引导弹窗'
            let actionType = '点击'
            let actionName = '关闭'

            // 精品课直播间页_VIP使用引导弹窗_点击关闭
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
            })
        },
        close() {
            this.$emit('update:show', false)
        },
        goUse() {
            this.stopTimer()
            openNewVip({
                url: VIP_BUYED_URL,
            })

            let fragmentName1 = 'VIP使用引导弹窗'
            let actionType = '点击'
            let actionName = '去学习'

            // 精品课直播间页_VIP使用引导弹窗_点击去学习
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
            })
        },
        startTimer(seconds = 0) {
            let date = +new Date()
            let end = date + seconds * 1000
            this.timer = setInterval(() => {
                date = +new Date()
                let remainder = end - date
                this.timeing = Math.max(Math.ceil(remainder / 1000), 0)
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    this.goUse()
                }
            }, 16)
        },
        stopTimer() {
            clearInterval(this.timer)
            this.timer = null
            this.timeing = 0
        },
        removeTags(str) {
            return str.replace(/<[^>]+>/g, '')
        },
    },
}
</script>

<style lang="less" scoped>
.vip-guide-popup {
    /deep/ .dialog .close,
    /deep/ .in-dialog .close {
        background: url(../assets/images/<EMAIL>) no-repeat;
        background-size: 50px 50px;
    }
    /deep/ .dialog {
        height: 1030px;
    }

    /deep/ .dialog,
    /deep/ .in-dialog {
        max-width: 7.5rem;
        transform: translateX(-50%);
        left: 50% !important;
    }

    .wrap {
        overflow: hidden;
        height: 100%;
        position: relative;
        background: url(../assets/images/vip-guide-bg1.png) no-repeat top / 750px auto;
    }
    .content {
        position: relative;
    }
    .kemu {
        position: absolute;
        left: 50%;
        top: 150px;
        transform: translate(-50%, 0);
        width: 476px;
        height: 152px;
        &.kemu1 {
            background: url(../assets/images/vip-guide-text1.png) no-repeat top / 100% auto;
        }
        &.kemu2 {
            background: url(../assets/images/vip-guide-text2.png) no-repeat top / 100% auto;
        }
        &.kemu3 {
            background: url(../assets/images/vip-guide-text3.png) no-repeat top / 100% auto;
        }
        &.kemu4 {
            background: url(../assets/images/vip-guide-text4.png) no-repeat top / 100% auto;
        }
        &.manfen {
            background: url(../assets/images/vip-guide-text5.png) no-repeat top / 100% auto;
        }
    }
    .title {
        width: 676px;
        height: 108px;
        background: url(../assets/images/vip-guide-text6.png) no-repeat top / 100% auto;
        margin: 26px auto 0;
    }
    .desc {
        margin: 6px 124px 0;
        text-align: center;
        color: #4b1f13;
        font-size: 28px;
        line-height: 68px;
        background: linear-gradient(
            270deg,
            rgba(255, 255, 255, 0) 6%,
            rgba(255, 255, 255, 0.6) 51%,
            rgba(255, 255, 255, 0) 94%
        );
    }
    .step-list {
        display: flex;
        flex-wrap: wrap;
        padding: 20px 25px 0;
        .item {
            margin: 20px 10px 0;
            width: 330px;
            height: 268px;
            background: url(../assets/images/vip-guide-bg2.png) no-repeat top / 100% auto;
            position: relative;
            overflow: hidden;
        }
        .num {
            position: absolute;
            top: 14px;
            left: 14px;
            font-size: 24px;
            color: #fff;
            width: 80px;
            text-align: center;
        }
        .name {
            position: absolute;
            top: 14px;
            left: 114px;
            font-size: 24px;
            color: #5D2503;
            font-weight: bold;
            &.s {
                font-size: 22px;
            }
            /deep/ b {
                color: #FF0F00;
            }
        }
        .image {
            margin: 80px auto 0;
            width: 290px;
        }
    }
    .btn-wrap {
        position: relative;
        .btn {
            margin: 38px auto 0;
            width: 694px;
            height: 98px;
            background: url(../assets/images/vip-guide-btn1.png) no-repeat center / 100% auto;
            line-height: 98px;
            font-size: 36px;
            text-align: center;
            color: #ffffff;
            position: relative;
        }
        .btn-icon {
            position: absolute;
            top: -22px;
            right: 28px;
            width: 184px;
            height: 60px;
            background: url(../assets/images/vip-guide-text7.png) no-repeat center / 100% auto;
        }
    }
    .time-down {
        margin-top: 18px;
        text-align: center;
        font-size: 28px;
        color: #621f04;
    }
    .no-data {
        // background: url(../assets/images/no-data.png) no-repeat center 40px;
        background-size: 440px 334px;
        font-size: 28px;
        text-align: center;
        padding-top: 402px;
        padding-bottom: 80px;
    }
}
</style>
