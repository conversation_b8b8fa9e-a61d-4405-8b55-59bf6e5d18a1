<template>
    <wx-open-launch-app
        class="launch-app-btn weixn-btn"
        appid="wx6fcf65f28d6a3919"
        v-if="(isAndroid || isHarmony) && isWeixin"
        :extinfo="targetUrl"
        @launch="launch"
        @error="error"
    >
        <script type="text/wxtag-template">
            <style>.btn { position: absolute; top: 0; right: 0; bottom: 0; left: 0; }</style>
            <div class="btn"></div>
        </script>
    </wx-open-launch-app>
    <div v-else class="launch-app-btn test-btn" @click="goApp"></div>
</template>

<script>
import {isAndroid, isWeixin, isHarmony} from '../utils/tools'
let timer
let donwloadUrl =
    'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-jiaoliantuiguang/down1.html?channelCode=zhibo'

export default {
    data() {
        return {
            isAndroid,
            isHarmony,
            isWeixin,
        }
    },
    props: {
        targetUrl: {
            type: String,
            default: '',
        },
    },
    mounted() {
        console.log('wakeupBtn mounted isHarmony', isHarmony)
    },
    methods: {
        goApp() {
            location.href =
                'https://www.jiakaobaodian.com/mucang-gateway?navUrl=' +
                encodeURIComponent(this.targetUrl)
            if (timer) clearTimeout(timer)
            timer = setTimeout(() => {
                window.location.href = donwloadUrl
            }, 2000)

            document.addEventListener('visibilitychange', () => {
                clearTimeout(timer)
            })
            this.$emit('trigger')
        },
        launch() {
            this.$emit('trigger')
        },
        error(e) {
            console.log('launch fail', e.detail)
            window.location.href = donwloadUrl
            this.$emit('trigger')
        },
    },
}
</script>
<style lang="less" scoped>
.launch-app-btn {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
</style>
