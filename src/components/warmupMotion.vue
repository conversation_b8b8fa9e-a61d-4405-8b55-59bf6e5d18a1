<template>
    <div>
        <div class="showcase-item" @click.stop="clickShowcaseGoods()">
            <div class="image">
                <img :src="advertDetail.img" />
            </div>
            <div class="info">
                <div>
                    <div class="t">{{ advertDetail.popupTitle }}</div>
                    <div class="p">
                        {{ advertDetail.popupSubTitle }}
                    </div>
                </div>
                <div class="btn-wrap">
                    <div class="price">
                        ￥<span>{{goodsDetail.price }}</span>
                    </div>
                    <div
                        v-if="advertDetail.bought && !advertDetail.upgrade"
                        class="btn btn1"
                    >
                        去使用
                    </div>
                    <div v-else class="btn btn2">
                        {{
                            advertDetail.upgrade ? '去升级' : '去抢购'
                        }}
                    </div>
                </div>
            </div>
        </div>
        <payPopup
            :key="advertDetail.icon + 'payPopup'"
            :advertDetail="advertDetail"
            :goodsDetailProp="goodsDetail"
            :motionDetailProp="motionDetail"
            :show.sync="payPopupVisible"
            :fragmentName1="fragmentName1"
            :fullScreen="fullScreen"
        />
    </div>
</template>

<script>
import {trackEvent} from '../utils/tools'
import goodsMixin from '../utils/goodsMixin'
import payPopup from './payPopup'

export default {
    mixins: [goodsMixin],
    components: {payPopup},
    data() {
        return {
            fullScreen: false,
            fragmentName1: '暖场视频运营位',
            payPopupVisible: false,
            goodsDetail: {},
            motionDetail: {},
        }
    },
    props: {
        advertDetail: Object,
    },

    async mounted() {
        this.goodsDetail = await this.getGoodsItem(this.advertDetail)
        this.motionDetail = await this.getAdvertDetail(this.advertDetail)

        let fragmentName1 = this.fragmentName1
        let actionType = '出现'

        // 精品课直播间页_暖场视频运营位_出现
        trackEvent({fragmentName1, actionType, payStatus: 2})
    },
    methods: {
        async clickShowcaseGoods() {
            this.popupMotion(this.advertDetail, this.motionDetail)
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';
.showcase-item {
    display: flex;
    background-color: rgba(0,0,0,0.8);
    border: 1px solid rgba(244,247,247,0.3);
    border-radius: 28px;
    padding: 10px;
    .image {
        width: 172px;
        height: 172px;
        overflow: hidden;
        position: relative;
        border-radius: 8px;
        img {
            width: 100%;
        }
    }
    .info {
        margin-left: 14px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 0;
        .t {
            font-size: 30px;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .p {
            font-size: 22px;
            color: #F9DBC0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 6px;
        }
    }
    .btn-wrap {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding-bottom: 10px;
    }
    .price {
        color: #F9DBC0;
        font-size: 24px;
        line-height: 1.3;
        span {
            font-size: 40px;
        }
    }
    .btn {
        width: 188px;
        height: 64px;
        font-weight: 500;
        font-size: 26px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 32px;
        background: linear-gradient(135deg,#f9dbc0, #efaf8b);
        color: #3e260b;
    }
}
</style>
