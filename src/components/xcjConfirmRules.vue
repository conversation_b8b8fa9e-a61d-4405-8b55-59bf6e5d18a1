<template>
    <popup class="model-leave" :position="'center'" @closed="closed" :show.sync="visible">
        <div class="conf-wrap content">
            <div class="conf-t title">敬请确认</div>
            <div class="conf-c desc">
                支付前请阅读<span class="proto" @click="openRules">《活动规则》</span>
            </div>
            <div class="btns">
                <div class="conf-ok confirm" @click="confirm"><span>同意并继续</span></div>
                <div class="conf-cancel cancel" @click="close"><span>不同意</span></div>
            </div>
        </div>
    </popup>
</template>

<script>
import popup from './dialog'
export default {
    components: {
        popup,
    },
    data() {
        return {
            visible: false,
        }
    },
    props: {
        show: Boolean,
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    mounted() {},
    methods: {
        // TODO 手动同步状态
        closed() {
            this.$emit('update:show', false)
        },
        close() {
            this.$emit('update:show', false)
            this.$emit('cancel')
        },
        confirm() {
            this.close()
            this.$emit('confirm')
        },
        openRules() {
            this.$emit('openRules')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../assets/styles/variables';

.model-leave {
    /deep/ .dialog {
        width: 500px;
    }

    .content {
        background-color: #fff;
        border-radius: 20px;
        padding: 30px 20px 60px;
        text-align: center;
    }
    .title {
        font-size: 36px;
    }

    .desc {
        font-size: 28px;
        margin-top: 30px;
    }

    .proto {
        color: #cd5909;
    }

    .btns {
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        justify-content: space-around;
        margin-top: 10px;

        .cancel {
            flex: 1;
            margin: 0 20px;
            height: 88px;
            border: 1px solid #1dacf9;
            border-radius: 46px;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
            color: #1dacf9;
            line-height: 88px;
        }

        .confirm {
            flex: 1;
            margin: 20px 20px;
            height: 88px;
            background: linear-gradient(135deg, #67cef8, #1e74fa);
            line-height: 88px;
            border-radius: 46px;
            font-size: 32px;
            text-align: center;
            color: #ffffff;
        }
    }
}
</style>
