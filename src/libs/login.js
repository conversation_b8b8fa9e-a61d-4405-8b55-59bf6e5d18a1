!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.mcLogin=n():e.mcLogin=n()}(self,(function(){return function(){var e={72:function(e,n,t){"use strict";var o,i=function(){var e={};return function(n){if(void 0===e[n]){var t=document.querySelector(n);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(e){t=null}e[n]=t}return e[n]}}(),r=[];function a(e){for(var n=-1,t=0;t<r.length;t++)if(r[t].identifier===e){n=t;break}return n}function c(e,n){for(var t={},o=[],i=0;i<e.length;i++){var c=e[i],s=n.base?c[0]+n.base:c[0],d=t[s]||0,u="".concat(s," ").concat(d);t[s]=d+1;var l=a(u),f={css:c[1],media:c[2],sourceMap:c[3]};-1!==l?(r[l].references++,r[l].updater(f)):r.push({identifier:u,updater:h(f,n),references:1}),o.push(u)}return o}function s(e){var n=document.createElement("style"),o=e.attributes||{};if(void 0===o.nonce){var r=t.nc;r&&(o.nonce=r)}if(Object.keys(o).forEach((function(e){n.setAttribute(e,o[e])})),"function"==typeof e.insert)e.insert(n);else{var a=i(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(n)}return n}var d,u=(d=[],function(e,n){return d[e]=n,d.filter(Boolean).join("\n")});function l(e,n,t,o){var i=t?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=u(n,i);else{var r=document.createTextNode(i),a=e.childNodes;a[n]&&e.removeChild(a[n]),a.length?e.insertBefore(r,a[n]):e.appendChild(r)}}function f(e,n,t){var o=t.css,i=t.media,r=t.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),r&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var p=null,m=0;function h(e,n){var t,o,i;if(n.singleton){var r=m++;t=p||(p=s(n)),o=l.bind(null,t,r,!1),i=l.bind(null,t,r,!0)}else t=s(n),o=f.bind(null,t,n),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)};return o(e),function(n){if(n){if(n.css===e.css&&n.media===e.media&&n.sourceMap===e.sourceMap)return;o(e=n)}else i()}}e.exports=function(e,n){(n=n||{}).singleton||"boolean"==typeof n.singleton||(n.singleton=(void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o));var t=c(e=e||[],n);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<t.length;o++){var i=a(t[o]);r[i].references--}for(var s=c(e,n),d=0;d<t.length;d++){var u=a(t[d]);0===r[u].references&&(r[u].updater(),r.splice(u,1))}t=s}}}},314:function(e){"use strict";e.exports=function(e){var n=[];return n.toString=function(){return this.map((function(n){var t=function(e,n){var t,o,i,r=e[1]||"",a=e[3];if(!a)return r;if(n&&"function"==typeof btoa){var c=(t=a,o=btoa(unescape(encodeURIComponent(JSON.stringify(t)))),i="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(o),"/*# ".concat(i," */")),s=a.sources.map((function(e){return"/*# sourceURL=".concat(a.sourceRoot||"").concat(e," */")}));return[r].concat(s).concat([c]).join("\n")}return[r].join("\n")}(n,e);return n[2]?"@media ".concat(n[2]," {").concat(t,"}"):t})).join("")},n.i=function(e,t,o){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(o)for(var r=0;r<this.length;r++){var a=this[r][0];null!=a&&(i[a]=!0)}for(var c=0;c<e.length;c++){var s=[].concat(e[c]);o&&i[s[0]]||(t&&(s[2]?s[2]="".concat(t," and ").concat(s[2]):s[2]=t),n.push(s))}},n}},625:function(e){e.exports='<div class="com-verify-code"> <div class="com-verify-conent"> <div class="header">你好！请登录</div> <div class="com-verify-ipt"> <input id="J-muLogin-phone" type="tel" pattern="[0-9]*" class="phone" placeholder="请输入手机号" maxlength="11"/> </div> <div class="com-verify-ipt mt12"> <input id="J-muLogin-code" type="tel" pattern="[0-9]*" class="yzm" placeholder="请输入验证码" skip="true" key="code" maxlength="6"/> <div class="djs" id="J-muLogin-djs"></div> </div> <div class="text"> <p>1.未注册用户会自动创建驾考宝典账号</p> <p> 2.登录即同意 <a href="https://laofuzi.kakamobi.com/protocol/protocol.html?_productCategory=jiakaobaodian&_product=%E9%A9%BE%E8%80%83%E5%AE%9D%E5%85%B8&_appName=jiakaobaodian&protocolKey=jkbdUserAgreement">《用户使用协议》</a>和 <a href="https://laofuzi.kakamobi.com/protocol/protocol.html?_productCategory=jiakaobaodian&_product=%E9%A9%BE%E8%80%83%E5%AE%9D%E5%85%B8&_appName=jiakaobaodian&protocolKey=jkbdPrivateAgreement">《隐私政策》</a> </p> </div> <div class="com-verify-btn" id="J-muLogin-btn"> 确认 </div> </div> <div class="df-close" id="J-muLogin-close"></div> </div>'},865:function(e,n,t){(n=t(314)(!1)).push([e.id,".com-verify-code {\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n.com-verify-code .com-verify-conent {\n  background: #fff;\n  width: 315px;\n  height: 300px;\n  border-radius: 15px;\n  padding: 16px 18px 24px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n.com-verify-code .com-verify-conent .header {\n  font-size: 18px;\n  font-weight: bold;\n  text-align: center;\n}\n.com-verify-code .com-verify-conent .tip {\n  font-size: 12px;\n  color: #D23104;\n  margin-top: 4px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt {\n  margin-top: 8px;\n  display: flex;\n  background-size: 20px 22px;\n  background-position: left center;\n  background-repeat: no-repeat;\n  justify-content: space-between;\n  width: 249px;\n  height: 44px;\n  border-radius: 2px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt input {\n  outline: none;\n  border: none;\n  background: #f7f7f7;\n  font-size: 15px;\n  padding-left: 12px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .phone {\n  width: 100%;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .yzm {\n  width: 138px;\n  height: 44px;\n  border-radius: 2px;\n  border: none;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .djs {\n  width: 99px;\n  height: 44px;\n  border-radius: 2px;\n  border: 1px solid #F4C3A9;\n  font-size: 14px;\n  font-weight: 400;\n  color: #ED631C;\n  line-height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #FFEEE6;\n  border-radius: 4px;\n}\n.com-verify-code .com-verify-conent .com-verify-ipt .disabeld {\n  background: #F4F7F7;\n  border: none !important;\n  color: #999 !important;\n}\n.com-verify-code .com-verify-conent .text {\n  margin-top: 15px;\n}\n.com-verify-code .com-verify-conent .text p {\n  font-size: 12px;\n  font-weight: 400;\n  color: #9F5217;\n  line-height: 17px;\n  margin-bottom: 2px;\n}\n.com-verify-code .com-verify-conent .text p a {\n  font-size: 12px;\n  font-family: PingFangSC-Regular, PingFang SC;\n  font-weight: 400;\n  color: #04A5FF;\n  line-height: 17px;\n}\n.com-verify-code .com-verify-conent .com-verify-btn {\n  width: 249px;\n  height: 40px;\n  line-height: 40px;\n  box-shadow: 0px 4px 12px -4px rgba(254, 89, 89, 0.5);\n  background: linear-gradient(135deg, #FCC6A6 0%, #FDA789 100%);\n  border-radius: 25px;\n  font-size: 16px;\n  font-weight: 500;\n  color: #FFFFFF;\n  text-align: center;\n  margin-top: 16px;\n}\n.com-verify-code .com-verify-conent .activied {\n  background: linear-gradient(135deg, #FF7900 0%, #FF1C54 100%);\n}\n.com-verify-code .df-close {\n  margin-left: -21/2px;\n  width: 50px;\n  height: 50px;\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAABACAMAAACZQlHRAAAAM1BMVEUAAAD///////////////////////////////////////////////////////////////+3leKCAAAAEHRSTlMAgDHsbHcIQVMZSCtgETokMbaimQAAAahJREFUWMO1mO12wyAIho0F1CQmuf+rnR/bmnqUJudt+bWhPiJghJqhyOI8EU8TE3m3iLkpYaapEZrD9fWy0dQV2uQawHFdsDoboiRFDNatVcfuAsQWAPu9Hdh9HbFvALHsRnaAp2Jd1AgLZ8CiTMgQVibM5bS6ncVT82jUZxPC23hnQ3x/bM1DciFkPjuka0NroH5g31c7c1Fcb7ulp9TtaOISeeAixfUcW1eS3EEINS61iRlu3uVktz0hWXGl4lKW839kbgud9hVu3Ut2cM1eg/g0Y2uN4ONhe4THwY0Z2/PPlxXhcfQYNqvDWfG/dUgGdSYXhq5MDgh/meaV6QrWl4yu59jNgKEbtv+eRFK6G6MwuoQqaanU2KxGYSgEs9ZscDlDhgyF8Fzrc0gVhkIwtkaCcmQUhkLI+UAVEY3CWMcEEyuCs1cVxjEmGKlZWWKqMBRCXYwjPnMQ3J14UPHU+kyC49cMv+z4Jwf/8H3o84s/AvhThD+I+LOMFwd4iYIXSni5hheNeOmKF9B4GY83E3hLgzdWeHsHN5l4qws33F9q+/EfH34Az/UURYgKgFQAAAAASUVORK5CYII=) no-repeat center center;\n  background-size: 60%;\n  position: static;\n  margin-top: 10px;\n}\n.mark126 {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 1005;\n}\n.mark126 .captcha {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  background: white;\n}\n",""]),e.exports=n}},n={};function t(o){var i=n[o];if(void 0!==i)return i.exports;var r=n[o]={id:o,exports:{}};return e[o](r,r.exports,t),r.exports}t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,{a:n}),n},t.d=function(e,n){for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.nc=void 0;var o={};return function(){"use strict";function e(e,n,t){var o=document.createElement("div");return document.getElementById("mcToast")&&document.body.removeChild(document.getElementById("mcToast")),o.innerText=e,o.setAttribute("id","mcToast"),o.style.position="fixed",o.style.left="50%",o.style.top="50%",o.style.transform="translate(-50%, -50%)",o.style.webkitTransform="translate(-50%, -50%)",o.style.background="rgba(0, 0, 0, 0.7)",o.style.zIndex="9999",o.style.padding="10px 20px",o.style.borderRadius="6px",o.style.textAlign="center",o.style.color="#ffffff",o.style.maxWidth="90%",o.style.minWidth="60%",o.style.fontSize="14px",o.style.lineHeight="1.5",n=n||2e3,document.body.appendChild(o),new Promise((function(e){setTimeout((function(){o.remove(),t&&t(),e()}),n)}))}function n(e){var n,t,o,i,r=Math.abs(parseInt((new Date).getTime()*Math.random()*1e4)).toString(),a=0;for(n=0;r.length>n;n++)a+=parseInt(r[n]);return t=[],o=a+=r.length,i=3-a.toString().length,a=i-""+o.length<=0?o:(t[i]||(t[i]=Array(i+1).join(0)))+o,e.toString()+r+a}function i(e){var n=[];for(var t in e)n.push(t+"="+encodeURIComponent(e[t]));return n.join("&")}function r(e,n){var t,o={};return n||(n=window.location.href),n.replace(/[#|?&]+([^=#|&]+)=([^#|&]*)/gi,(function(n,i,r){t=new RegExp(e,"gi"),o[i]=decodeURIComponent(r),e&&!i.match(t)&&delete o[i]})),o}t.d(o,{default:function(){return _}});var a=r();function c(e){var t=e.url,o=e.method,c=e.params;if(!t)return Promise.reject();var s,d,u={_r:n(1),_appUser:a._appUser||(d=localStorage.getItem("_appUser"),d||(d=(s=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)})()+s()+"-"+s()+"-"+s()+"-"+s()+"-"+s()+s()+s(),localStorage.setItem("_appUser",d)),d)};return"GET"===o&&(u=Object.assign(c,u)),t=function(e,n){var t=r(null,e);return t=Object.assign(t,n),(e=e.substring(0,e.indexOf("?")>0?e.indexOf("?"):e.length))+"?"+i(t)}(t,u),o=(o||"get").toUpperCase(),c=c||{},new Promise((function(e,n){var r=new XMLHttpRequest;r.open(o,t),r.onreadystatechange=function(){if(4===r.readyState){var t=r.response;"string"==typeof t&&(t=JSON.parse(t)),r.status<400?t.success?e(t):n(t):r.status>=400&&n(t)}},"POST"===o?(r.setRequestHeader("content-type","application/x-www-form-urlencoded; charset=UTF-8"),r.send(i(c))):r.send()}))}function s(e){return(new DOMParser).parseFromString(e,"text/html").body.childNodes[0]}function d(e,n){var t=e.className?e.className.split(" "):[];-1===t.indexOf(n)&&(t.push(n),e.className=t.join(" "))}function u(e,n){var t=e.className?e.className.split(" "):[],o=t.indexOf(n);-1!==o&&(t.splice(o,1),e.className=t.join(" "))}var l,f=t(72),p=t.n(f),m=t(865),h=t.n(m),g=(p()(h(),{insert:"head",singleton:!1}),h().locals,/\s/),v=/^\s+/,y=function(e){return e?e.slice(0,function(e){for(var n=e.length;n--&&g.test(e.charAt(n)););return n}(e)+1).replace(v,""):e},b=function(e){var n=typeof e;return null!=e&&("object"==n||"function"==n)},x="object"==typeof global&&global&&global.Object===Object&&global,j="object"==typeof self&&self&&self.Object===Object&&self,E=(x||j||Function("return this")()).Symbol,w=Object.prototype,A=w.hasOwnProperty,k=w.toString,C=E?E.toStringTag:void 0,I=Object.prototype.toString,S=E?E.toStringTag:void 0,N=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":S&&S in Object(e)?function(e){var n=A.call(e,C),t=e[C];try{e[C]=void 0;var o=!0}catch(e){}var i=k.call(e);return o&&(n?e[C]=t:delete e[C]),i}(e):function(e){return I.call(e)}(e)},F=/^[-+]0x[0-9a-f]+$/i,O=/^0b[01]+$/i,L=/^0o[0-7]+$/i,T=parseInt,M=function(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return null!=e&&"object"==typeof e}(e)&&"[object Symbol]"==N(e)}(e))return NaN;if(b(e)){var n="function"==typeof e.valueOf?e.valueOf():e;e=b(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=y(e);var t=O.test(e);return t||L.test(e)?T(e.slice(2),t?2:8):F.test(e)?NaN:+e},U=1/0,D=function(e){var n=function(e){return e?(e=M(e))===U||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}(e),t=n%1;return n==n?t?n-t:n:0},J=function(e,n){var t;if("function"!=typeof n)throw new TypeError("Expected a function");return e=D(e),function(){return--e>0&&(t=n.apply(this,arguments)),e<=1&&(n=void 0),t}}(2,(function(){return new Promise((function(e){var n,t;(n="https://cstaticdun.126.net/load.min.js?t="+(new Date).getTime(),t=document.createElement("script"),t.type="text/javascript",t.src=n,t.async=!1,document.head.appendChild(t),new Promise((function(e){t.onload=function(){e()}}))).then((function(){e()}))}))}));var B,P=t(625),R=t.n(P);function z(e){e=e||{},this.djs=-1,this.smsId=null,this.isDisabled=!1,this.callback=e.callback,this.loginOptions=e.loginOptions,this.$el=s(R()),document.body.appendChild(this.$el),e.frozen&&(document.getElementById("J-muLogin-close").style.display="none"),this.registerEvent(),this.djsControl()}function H(){window.scroll(0,0)}function _(e){new z(e)}z.prototype.onDjs=function(){var n=this;this.isDisabled||(/(1[3-9]\d|999)\d{8}/.test(this.getPhone())?function(){l=s('<div class="mark126"><div class="captcha" id="captcha"></div></div>'),document.body.appendChild(l);var e=document.getElementById("captcha");return new Promise((function(n){J().then((function(){window.initNECaptcha({captchaId:"6f92317b6e7d4f4faa77a360d65826c5",element:e,mode:"embed",width:"320px",onVerify:function(e,t){setTimeout((function(){e||void 0===t||(l&&l.parentNode&&l.parentNode.removeChild(l),n(t.validate))}),600)}},(function(e){console.log(e,"instance")}),(function(e){console.log(e,"errer")}))}))}))}().then((function(e){n.sendSms(e)})):e("手机号码格式不正确"))},z.prototype.sendSms=function(e){var n,t=this;e&&(n={...this.loginOptions,NECaptchaValidate:e,phoneNumber:this.getPhone()},c({url:"https://auth.kakamobi.com/api/web/v3/login-sms/check.htm",params:Object.assign({_appName:"jiakaobaodian",_platform:"web",_authVersion:"1.5"},n),method:"post"}).then((function(e){return e.data.smsId}))).then((function(e){t.smsId=e,t.onDjs2()}))},z.prototype.onDjs2=function(){var e=this;this.isDisabled=!0,this.djs=60,this.djsControl(),B=setInterval((function(){var n=e.djs;--n>=0?e.djs=n:(e.isDisabled=!1,clearInterval(B)),e.djsControl()}),1e3)},z.prototype.onSubmit=function(){var n,t=this,o=this.smsId,i=this.getPhone(),r=this.getCode();i&&r&&(/(1[3-9]\d|999)\d{8}/.test(i)?o?r?(n={...this.loginOptions,phoneNumber:i,smsCode:r,smsId:o},c({url:"https://auth.kakamobi.com/api/web/v3/login-sms/login.htm",params:Object.assign({_appName:"jiakaobaodian",_platform:"web",_authVersion:"1.5"},n),method:"post"}).then((function(e){var n=e.data;return localStorage.setItem("mucang_userInfo",JSON.stringify(n)),localStorage.setItem("mucang_authToken",n.authToken),localStorage.setItem("vip-share-authToken",n.authToken),n}))).then((function(){t.callback&&t.callback(),t.destroy()})).catch((function(n){e(n&&n.message||"登录校验失败，请重试")})):e("请填写验证码"):e("请先发送验证码"):e("手机号码格式不正确"))},z.prototype.getPhone=function(){return document.getElementById("J-muLogin-phone").value},z.prototype.getCode=function(){return document.getElementById("J-muLogin-code").value},z.prototype.destroy=function(){clearInterval(B),this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},z.prototype.djsControl=function(){var e=-1===this.djs?"获取验证码":0===this.djs?"重新获取":"重新发送("+this.djs+")",n=document.getElementById("J-muLogin-djs");n.innerHTML=e,this.isDisabled?d(n,"disabeld"):u(n,"disabeld")},z.prototype.btnControl=function(){var e=this.getCode(),n=document.getElementById("J-muLogin-btn");e?d(n,"activied"):u(n,"activied")},z.prototype.registerEvent=function(){document.getElementById("J-muLogin-djs").addEventListener("click",this.onDjs.bind(this)),document.getElementById("J-muLogin-btn").addEventListener("click",this.onSubmit.bind(this)),document.getElementById("J-muLogin-close").addEventListener("click",this.destroy.bind(this)),document.getElementById("J-muLogin-phone").addEventListener("blur",H),document.getElementById("J-muLogin-code").addEventListener("blur",H),document.getElementById("J-muLogin-code").addEventListener("input",this.btnControl.bind(this))}}(),o.default}()}));
