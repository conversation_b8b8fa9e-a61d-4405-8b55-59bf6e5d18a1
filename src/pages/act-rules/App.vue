<template>
    <div>
        <popup class="rules-popup" :position="'bottom'" :show.sync="visible" @closed="closed" v-if="activityRule" :isInDialog="true">
            <div
                class="wrap"
            >
                <div class="header">活动规则</div>
                <div class="content">
                    <div class="legal-image" v-html="activityRule"></div>
                </div>
            </div>
        </popup>
        <template v-else>
            <div class="close" @click="close"></div>
            <loading bgColor="#666" />
        </template>
    </div>
</template>

<script>
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {getPromotionActivityRule} from '../../server/active'
import popup from '../../components/dialog'
import loading from '../../components/loading'
import {URLParams, setEmbeddedHeight} from '../../utils/tools'
import {webClose} from '../../utils/jump'

let pageName = '直播间页'
export default {
    components: {popup, loading},
    data() {
        return {
            visible: true,
            activityRule: '',
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '直播间'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(558)
        this.getPromotionActivityRule()
    },
    mounted() {},
    methods: {
        closed() {
            this.close()
        },
        close() {
            webClose()
        },
        async getPromotionActivityRule() {
            const resData = await getPromotionActivityRule({
                activityId: URLParams.promotionActivityId,
            })
            this.activityRule = resData.ruleContent
        },
    },
}
</script>

<style lang="less">
.close {
    position: absolute;
    z-index: 2;
    right: 14px;
    top: 14px;
    width: 50px;
    height: 50px;
    background: url(../../assets/images/<EMAIL>) no-repeat;
    background-size: 50px 50px;
}

.rules-popup {
    .wrap {
        background-color: #fff;
        display: flex;
        border-radius: 20px 20px 0 0;
        overflow: hidden;
        height: 100%;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 40px);
    }
    .header {
        font-weight: bold;
        font-size: 40px;
        text-align: center;
        padding: 40px 0 20px;
    }
    .content {
        margin-top: 18px;
        overflow-y: auto;
        flex: 1;
        padding: 0 15px 20px 15px;
    }
}
</style>
