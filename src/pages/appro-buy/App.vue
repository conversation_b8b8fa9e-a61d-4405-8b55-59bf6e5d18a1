<template>
    <div class="appro-page">
        <div class="appro-home">
            <div @click="close" class="close"></div>
            <div class="scroll_view" v-if="goodsConfig.liveType">
                <div class="header">
                    <div class="title">{{ title }}</div>
                </div>
                <div class="content">
                    <div
                        v-if="singleGoodsDetail.groupKey"
                        class="goods-item"
                        @click="changeGoods('singleKey')"
                        :class="{
                            active: selectGoods === 'singleKey',
                        }"
                    >
                        <div class="cvh">
                            <div class="info">
                                <p class="name">{{ singleGoodsDetail.name }}</p>
                                <p class="dec">直播答疑 考点资料包 3套付费课</p>
                            </div>
                            <div class="price">
                                ¥ <span>{{ singleGoodsDetail.price }}</span>
                            </div>
                        </div>
                    </div>
                    <div
                        v-if="allGoodsDetail.groupKey"
                        class="goods-item"
                        @click="changeGoods('allKey')"
                        :class="{
                            active: selectGoods === 'allKey',
                        }"
                    >
                        <div class="mark">
                            考不过最高补偿140元
                        </div>
                        <div class="cvh">
                            <div class="info">
                                <p class="name">{{ allGoodsDetail.name }}</p>
                                <p class="dec">一起买更优惠</p>
                            </div>
                            <div class="price">
                                ¥ <span>{{ allGoodsDetail.price }}</span>
                            </div>
                        </div>
                        <div class="tab-content tab-content2" v-if="comparePrice.savePrice">
                            <div class="diff-box">
                                <div class="diff">
                                    <span>比分开买立省</span>
                                    <span class="unit">￥</span>
                                    <span class="price">{{ comparePrice.savePrice }}</span>
                                </div>
                            </div>
                            <div class="compare-box">
                                <div class="item" v-for="item in comparePrice.groupItems" :key="item.compareChannelCode">
                                    <div class="name">{{item.name}}</div>
                                    <div class="price-box">
                                        <template v-if="item.price">
                                            <span class="unit">￥</span>
                                                <span class="price">{{item.price}}</span>
                                        </template>
                                        <template>
                                            <div class="unit">{{item.description}}</div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="slg">
                            <div>独享千元付费课程</div>
                            <div>重点刷题班讲师带学</div>
                        </div>
                    </div>
                    <payList direction="hor" theme="hor3" :showPayForOther="true" v-if="isAndroid" />
                </div>
            </div>
            <div class="loading" v-else>
                <loading bgColor="#666" />
            </div>
            <div class="footer">
                <div class="main">
                    <div class="activity-btns">
                        <div class="btn1">
                            <div class="p1">
                                <span class="i1">¥&nbsp;</span>
                                <span class="b1">{{showPrice}}</span>
                                <span class="t1" v-if="currentGoodsDetail.validDays"
                                    >&nbsp;/{{currentGoodsDetail.validDays}}天</span
                                >
                            </div>
                        </div>
                        <div class="btn2" @click="buy()">确认协议并支付</div>
                    </div>
                </div>
                <div class="line">
                    <vipAgreement theme="brown3"></vipAgreement>
                    <couponItem :couponUsable="couponUsable" :goodsDetail="currentGoodsDetail" />
                </div>
            </div>
        </div>

        <detainmentPupop
            :show.sync="detainmentPupopVisible"
            @buy="buy"
            :isInDialog="true"
            :title="currentGoodsDetail.name"
            :price="showPrice"
            :groupKey="currentGoodsDetail.groupKey"
        />
    </div>
</template>

<script>
import {mapState, mapGetters, mapMutations} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {find} from 'lodash-es'
import {webClose} from '../../utils/jump'
import {getBestCoupon} from '../../utils/coupon'
import {replaceInviteUrl} from '../../utils/constant'
import {getGoodsDetail, comparePrice} from '../../server/goods'
import {getApproGoodsConfig} from '../../server/topLesson'
import {
    createMobileOrder,
    getPayAfterStrategy,
} from '../../utils/payHelper'
import {
    isAndroid,
    isIOS,
    URLParams,
    trackEvent,
    setEmbeddedHeight,
    openNewVip,
    getUrl,
} from '../../utils/tools'
import loading from '../../components/loading'
import payList from '../../components/payList'
import vipAgreement from '../../components/vipAgreement'
import couponItem from '../../components/couponItem'

window.webviewFeature = 'vipWebview'

let openFrom = URLParams.openFrom
let pageName = '直播间页'
export default {
    components: {
        loading,
        payList,
        vipAgreement,
        couponItem,
        detainmentPupop: () => import('../../components/detainmentPupop'),
    },
    data() {
        let title, fragmentName1
        if (openFrom) {
            title = '试看已结束，开通VIP跟着讲师在线练题'
            fragmentName1 = '试看结束支付弹窗'
        } else {
            title = '开通VIP跟着讲师在线练题'
            fragmentName1 = '试看中支付弹窗'
        }
        let selectGoods
        if (openFrom === 'singleKey') {
            selectGoods = 'singleKey'
        } else {
            selectGoods = 'allKey'
        }
        return {
            title,
            selectGoods,
            isAndroid,
            fragmentName1,
            singleGoodsDetail: {},
            allGoodsDetail: {
                price: null,
            },
            goodsConfig: {},
            detainmentPupopVisible: false,
            viewHeight: 0,
            comparePrice: {
                groupItems: [],
            },
        }
    },
    watch: {
        viewHeight(val) {
            setEmbeddedHeight(val)
        },
    },
    computed: {
        ...mapState(['selectCoupons']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed']),
        currentGoodsDetail() {
            if (this.selectGoods === 'singleKey') {
                return this.singleGoodsDetail
            } else {
                return this.allGoodsDetail
            }
        },
        couponUsable() {
            return this.selectCoupons[this.currentGoodsDetail.groupKey + '_selectCoupon'] || {}
        },
        showPrice() {
            let payPrice = this.currentGoodsDetail.price
            let couponPrice = this.couponUsable.priceCent
            if (couponPrice) {
                return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
            } else {
                return payPrice
            }
        },
    },
    created() {
        document.title = '精品课'
        stat.setPageName(pageName)
        let readed =
            URLParams.agreementAccept === 'true' || +URLParams.agreementAccept === 1 || this.readed
        this.$store.commit('updatePayConfig', {readed})
    },
    async mounted() {
        let goodsConfig = await getApproGoodsConfig({
            carType: URLParams.carStyle,
            kemu: +URLParams.kemuNum,
            liveType: URLParams.liveType,
            sessionId: URLParams.id,
        })
        this.goodsConfig = goodsConfig
        this.setPageHeight()
        if (goodsConfig.allKey) {
            this.getAllGoodsDetail(goodsConfig.allKey)
        }
        if (goodsConfig.singleKey && openFrom !== 'allKey') {
            this.getSingleGoodsDetail(goodsConfig.singleKey)
        }

        let fragmentName1 = this.fragmentName1
        let actionType = '出现'

        // 直播间页_试看结束支付弹窗_出现
        trackEvent({fragmentName1, actionType, payStatus: 2})
    },
    methods: {
        ...mapMutations([
            'updateSelectCoupons',
        ]),
        close(showDetainment = true) {
            if (isAndroid && showDetainment) {
                if (this.viewHeight < 430) {
                    this.viewHeight = 430
                }
                this.detainmentPupopVisible = !!showDetainment
            } else {
                webClose()
            }
        },
        async getAllGoodsDetail(goodsKey) {
            const goodsDetail = await getGoodsDetail({
                tiku: URLParams.carStyle,
                groupKey: goodsKey,
            })
            this.getCoupon(goodsDetail)
            if (goodsDetail.bought && !goodsDetail.upgrade) {
                this.selectGoods = 'singleKey'
            } else {
                this.allGoodsDetail = goodsDetail
                const comparePriceData = await comparePrice({
                    tiku: URLParams.carStyle,
                    sceneCode: URLParams.sceneCode,
                    patternCode: URLParams.patternCode,
                    groupKey: goodsDetail.groupKey,
                })
                this.comparePrice = comparePriceData
            }
        },
        async getSingleGoodsDetail(goodsKey) {
            const goodsDetail = await getGoodsDetail({
                tiku: URLParams.carStyle,
                groupKey: goodsKey,
            })
            this.getCoupon(goodsDetail)
            this.singleGoodsDetail = goodsDetail
        },
        async getCoupon(goodsDetail) {
            let coupon = await getBestCoupon(goodsDetail)

            if (coupon.couponCode) {
                this.updateSelectCoupons({[goodsDetail.groupKey + '_selectCoupon']: coupon})
            }
        },
        setPageHeight() {
            let viewHeight = 130
            if (isAndroid) {
                viewHeight += 25
            }
            if (this.goodsConfig.allKey) {
                viewHeight += 250
            }
            if (this.goodsConfig.singleKey && openFrom !== 'allKey') {
                viewHeight += 100
            }
            this.viewHeight = Math.max(viewHeight, 320)
        },
        changeGoods(type) {
            this.selectGoods = type
        },
        async buy() {
            let fragmentName1 = this.fragmentName1
            if (this.checkAgreement && !this.readed) {
                await this.$confirmProtocol()
            }
            this.payForVip(fragmentName1)

            let actionType = '点击'
            let actionName = '确认支付'

            let strs = {
                groupKey: this.currentGoodsDetail.groupKey,
                sessionId: URLParams.id,
                payPathType: 0,
                payStatus: 2,
            }
            // 直播间页_试看结束支付弹窗_点击确认支付
            trackEvent({fragmentName1, actionType, actionName, ...strs})
        },
        payForVip(fragmentName1) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            if (payType === 100) {
                openNewVip({
                    url: getUrl(replaceInviteUrl, {
                        channelCode: this.currentGoodsDetail.groupKey,
                    }),
                })
                return
            }
            createMobileOrder(
                {
                    sessionIds: this.currentGoodsDetail.sessionIdList.join(','),
                    appleId: this.currentGoodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    couponCode: this.couponUsable.couponCode,
                    activityType: this.currentGoodsDetail.activityType,
                    groupKey: this.currentGoodsDetail.groupKey,
                    squirrelGoodsInfo: this.currentGoodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this.currentGoodsDetail.groupKey,
                        lessonId: URLParams.lessonId,
                    },
                },
                getPayAfterStrategy(true, 'vip')
            )
        },
    },
}
</script>

<style lang="less">
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
}

.appro-page {
    background: linear-gradient(180deg,#ffeddc 1%, #fffaf0 38%, #ffffff 77%, #ffffff);
    height: 100%;
    .appro-home {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        .close {
            position: absolute;
            right: 24px;
            top: 18px;
            width: 40px;
            height: 40px;
            background: url(./images/<EMAIL>) no-repeat;
            background-size: 40px 40px;
            z-index: 1;

            &:after {
                content: '';
                position: absolute;
                left: -20%;
                right: -20%;
                top: -20%;
                bottom: -20%;
            }
        }
        .loading {
            flex: 1;
            position: relative;
        }
        .scroll_view {
            overflow-y: auto;
        }
        .header {
            padding: 30px 0 0 40px;
            .title {
                font-size: 28px;
                color: #9f5217;
            }
        }
        .content {
            margin-top: 30px;
            padding-bottom: 12px;
            .goods-item {
                margin: 26px 40px 0;
                padding: 28px 0;
                background: #fff;
                border: 4px solid #fff;
                border-radius: 16px;
                position: relative;
                box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.15);
                &.active {
                    &::after {
                        content: '';
                        position: absolute;
                        right: 0;
                        bottom: 0;
                        width: 60px;
                        height: 60px;
                        background: url(../../assets/images/<EMAIL>) no-repeat center / 60px 60px;
                    }
                    background-color: #FFF5E2;
                    border-color: #ED9964;
                }
                .cvh {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 40px 0 30px;
                    .name {
                        font-size: 36px;
                        font-weight: bold;
                        color: #681309;
                    }
                    .dec {
                        font-size: 26px;
                        color: #9f5217;
                    }
                    .price {
                        font-size: 36px;
                        white-space: nowrap;
                        color: #681309;
                        span {
                            font-size: 60px;
                            font-weight: bold;
                        }
                    }
                }
                .tab-content {
                    position: relative;
                    height: 200px;
                    border-radius: 12px;
                    box-sizing: border-box;
                    margin: 20px 18px 0;
                }
                .tab-content2 {
                    padding: 14px;
                    padding-left: 128px;
                    position: relative;
                    background: url(../../assets/images/compare-bg.png) no-repeat center center/cover;
                    .diff-box {
                        position: absolute;
                        left: 0;
                        height: 100%;
                        width: 128px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 14px;
                        box-sizing: border-box;
                        .diff {
                            line-height: 34px;
                            color: #692204;
                            font-size: 24px;
                            text-align: center;
                            .unit {
                                color: #f73b31;
                            }
                            .price {
                                font-size: 32px;
                                color: #f73b31;
                            }
                        }
                    }
                    .compare-box {
                        display: flex;
                        justify-content: space-between;
                        .item {
                            width: 130px;
                            height: 172px;
                            background: #fffbf6;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            border-radius: 10px;
                            border: 1px solid #fddec8;
                            color: #a03c1c;
                            position: relative;
                            flex: 1;
                            .name {
                                font-weight: bold;
                                font-size: 26px;
                                text-align: center;
                            }
                            .price-box {
                                margin-top: 16px;
                                font-weight: bold;
                                .unit {
                                    font-size: 24px;
                                }
                                .price {
                                    font-size: 32px;
                                }
                            }
                            &:not(:last-child)::after {
                                content: "";
                                width: 34px;
                                height: 34px;
                                background: url(../../assets/images/<EMAIL>) no-repeat center center/cover;
                                position: absolute;
                                z-index: 1;
                                top: 50%;
                                right: 0;
                                transform: translate(20px, -50%);
                            }
                        }
                    }
                }
                .mark {
                    position: absolute;
                    padding: 0 10px;
                    top: -20px;
                    right: 0;
                    height: 40px;
                    line-height: 40px;
                    font-size: 24px;
                    color: #fff;
                    background: linear-gradient(90deg, #ff7810 0%, #fe3c29 55%, #fe6164 100%);
                    border-radius: 16px 2px 16px 2px;
                    max-width: 600px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .slg {
                    display: flex;
                    margin: 12px 12px 0;
                    > div {
                        height: 62px;
                        flex: 1;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin: 0 6px;
                        border-radius: 8px;
                        background-color: #FBDCB7;
                        color: #692204;
                    }
                }
            }
            .pay-list {
                padding: 25px 0 10px 45px;
            }
        }
        .footer {
            padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
            padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
            .line {
                padding: 0 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .agreement {
                padding: 10px 0;
                .fontSizeWithElder(24px);
            }
            .main {
                margin-top: 10px;
                padding: 0 30px;
                position: relative;
            }
            .activity-btns {
                display: flex;
                border-radius: 44px;
                height: 88px;
                overflow: hidden;
                background: linear-gradient(103deg, #FFFFFF 0%, #F9DBC0 45%, #EFAF8B 100%);
                .btn1 {
                    width: 412px;
                    background: url(../../assets/images/buy-button2.png) no-repeat;
                    background-size: 100% 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .p1 {
                        display: flex;
                        align-items: flex-end;
                        .i1 {
                            font-size: 32px;
                            font-weight: bold;
                            color: #EDEAD2;
                            line-height: 1.1;
                        }
                        .b1 {
                            font-size: 52px;
                            color: #EDEAD2;
                            line-height: 1;
                        }
                        .t1 {
                            font-size: 24px;
                            font-weight: bold;
                            color: #EDEAD2;
                            line-height: 1.4;
                        }
                    }
                }
                .btn2 {
                    flex: 1;
                    font-size: 32px;
                    color: #692204;
                    line-height: 44px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 4px 10px 0 0;
                }
            }
        }
    }
}
</style>
