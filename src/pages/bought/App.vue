<template>
    <div id="app">
        <div class="sec-left">
            <div class="sec-left-body">
                <div class="panel">
                    <div class="sec1">
                        <div class="user">
                            <div class="avatar"><img :src="userinfo.avatar" /></div>
                            <div class="info">
                                <p class="user-name">
                                    {{ userinfo.nickname }}
                                </p>
                                <img
                                    v-if="currentBadge.icon && currentBadge.buyStatus === 1"
                                    :src="currentBadge.icon"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="sec2">
                        <div
                            class="switch"
                            :class="{one: badges.length <= 1, two: badges.length <= 2}"
                        >
                            <div
                                v-for="item in badges"
                                :key="item.kemu"
                                class="tab"
                                @click="wakeup"
                                :class="[{current: kemu == item.kemu}, 'kemu' + item.kemu]"
                            >
                                <div class="inner">
                                    <div class="title">{{ item.title }}</div>
                                    <template v-if="item.buyStatus === 1">
                                        <div class="tag done">已开通</div>
                                        <div class="expire">
                                            <span>{{ item.time }}到期</span>
                                        </div>
                                    </template>
                                    <template v-else-if="item.price">
                                        <div class="tag undone">未开通</div>
                                        <div class="btn">
                                            <span>{{ item.price / 100 }}元开通</span>
                                        </div>
                                    </template>
                                    <template v-if="item.time && item.buyStatus !== 1">
                                        <div class="tag expired">已过期</div>
                                        <div class="expire">
                                            <span>{{ item.time }}已过期</span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sec3">
                    <div class="sec3-title">{{ getKemuName }}VIP专属学习方案</div>
                    <div class="sec3-list">
                        <div class="item" v-for="item in steps" :key="item.title">
                            <div>
                                <div class="title">{{ item.title }}</div>
                                <div class="desc">{{ item.text1 }}&nbsp;</div>
                            </div>
                            <div class="btn" @click="goUse">
                                {{ item.hasPermission === false ? '未开通' : '去学习' }}
                            </div>
                        </div>
                    </div>
                    <div class="sec3-title">其他增值权益</div>
                    <div class="sec3-buchang">
                        <div class="sec3-buchang-title">{{ getKemuName }}考不过补偿</div>
                        <div class="sec3-buchang-content">破解高频考点，节省80%时间</div>
                        <div class="sec3-buchang-btn" @click="wakeup">
                            申请补偿
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {cloneDeep} from 'lodash-es'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {URLParams, formatDate, wakeup, trackEvent, trackPageShow} from '../../utils/tools'
import {LOCATION} from '../../utils/constant'
import {getVipBages} from '../../server/permission'
let _badges = [
    {
        kemu: 1,
        title: '科一速成VIP',
    },
    {
        kemu: 2,
        title: '科二速成VIP',
    },
    {
        kemu: 3,
        title: '科三速成VIP',
    },
    {
        kemu: 4,
        title: '科四速成VIP',
    },
]
let kemuNames = {
    1: '科一',
    2: '科二',
    3: '科三',
    4: '科四',
}

let pageName = '站外H5直播间VIP已购页'
export default {
    data() {
        return {
            kemu: 1,
            isSuperVip: false,
            originalBadges: [],
            badges: cloneDeep(_badges),
            userinfo: {
                avatar:
                    'https://jiakao-audit.image.mucang.cn/jiakao-audit/2022/01/24/11/8a66545b8d3a49f5af0b3dc1c5fd87f5.png',
                nickname: '***',
            },
            badge: {},
            steps: [
                {title: '只练精简题库', type: 'quick500', text1: '500题全部做对为达标'},
                {title: '真实考场模拟', type: 'realroom', text1: '达到10次95分以上为达标'},
                {title: '临考冲刺检查', type: 'kqmj', text1: '考前秘卷全部做对'},
            ],
        }
    },
    computed: {
        getKemuName: function() {
            return kemuNames[this.kemu]
        },
        currentBadge() {
            let kemu = this.kemu
            if (this.isSuperVip) {
                kemu = 0
            }
            let badge = this.originalBadges.find(item => {
                return item.kemu === kemu
            })
            return badge || {}
        },
    },
    created() {
        document.title = 'VIP已购买页'
        stat.setPageName(pageName)
        trackPageShow({
            actionType: '展示',
        })
    },
    mounted() {
        this.getUserinfo()
        this.getBadgesData()
    },
    methods: {
        wakeup,
        goUse() {
            let actionType = '点击'
            let actionName = '去学习'

            // 精品课直播间页_退出全屏播放_点击
            trackEvent({actionType, actionName})

            wakeup()
        },
        getUserinfo() {
            let obj = localStorage.getItem(LOCATION.USERINFO) || '{}'
            let userinfo = JSON.parse(obj)
            if (userinfo.nickname) {
                this.userinfo.nickname = userinfo.nickname
                if (userinfo.avatar) {
                    this.userinfo.avatar = userinfo.avatar
                }
            }
        },
        async getBadgesData() {
            let resData = await getVipBages({
                carType: URLParams.carStyle || 'car',
                sceneCode: URLParams.sceneCode || '101',
                kemuAllPriority: true,
            })
            let list = resData.itemList
            list.forEach(item => {
                if (item.expireTime) {
                    item.time = formatDate(item.expireTime, 'yyyy.MM.dd')
                }
            })
            let isSuperVip = list.some(item => {
                return item.kemu === 0 && item.buyStatus === 1
            })
            this.isSuperVip = isSuperVip
            this.originalBadges = list
            let badges = []
            list.forEach(item => {
                let badge = _badges.find(item2 => {
                    return item2.kemu === item.kemu
                })
                if (badge) {
                    badges.push(Object.assign({}, badge, item))
                }
            })
            this.badges = badges
        },
    },
}
</script>

<style lang="less">
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
}

#app {
    height: 100%;

    .sec-left {
        position: relative;
        box-sizing: border-box;
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #f4f4f4;
        height: 100%;
        overflow-y: hidden;
    }
    .sec-left-body {
        flex: 1;
    }
    .panel {
        width: 100%;
        height: 360px;
        margin: 0 auto;
        background: url(./images/4.png) no-repeat;
        background-size: cover;
        position: relative;
    }
    .sec1 {
        position: absolute;
        left: 30px;
        top: 30px;
        .user {
            display: flex;
            color: #fff;
            .avatar {
                width: 98px;
                height: 98px;
                margin-right: 10px;
                border: 1px solid #ffffff;
                border-radius: 100%;
                img {
                    width: 100%;
                    display: block;
                    border-radius: 100%;
                }
            }
            .user-name {
                font-size: 40px;
                line-height: 56px;
            }
            .info {
                img {
                    width: auto;
                    height: 36px;
                }
            }
        }
    }
    .sec2 {
        overflow: auto;
        position: absolute;
        left: 0;
        top: 165px;
        width: 100%;
        height: 160px;
        .switch {
            font-size: 30px;
            display: flex;
            align-items: center;
            padding: 0 30px;
            position: absolute;
            top: 0;

            .tab {
                line-height: 60px;
                text-align: center;
                color: #2f3646;
                cursor: pointer;
                width: 280px;
                height: 140px;
                margin-right: 14px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .inner {
                    position: relative;
                    width: 280px;
                    height: 140px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    border-radius: 10px;
                    padding-top: 40px;
                }

                .title {
                    font-size: 28px;
                    font-weight: 500;
                    line-height: 40px;
                }
                .expire {
                    margin-top: 10px;
                    color: #3e424b;
                    line-height: 26px;
                    font-size: 22px;

                    span {
                        display: block;
                    }
                }
                .tag {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 88px;
                    height: 32px;
                    border-radius: 10px 0 10px 0;
                    font-size: 22px;
                    color: #fff;
                    text-align: center;
                    line-height: 32px;
                    &.done {
                        background: #e5641d;
                    }
                    &.undone {
                        background: linear-gradient(125deg, #6ab8ff 4%, #34a2ff 92%);
                    }
                    &.expired {
                        background: linear-gradient(122deg, #87a0b5 6%, #7893aa 89%);
                    }
                }
                .btn {
                    margin-top: 6px;
                    padding: 0 10px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(99deg, #ffd493 8%, #e5ad4c 93%);
                    border-radius: 20px;
                    border: 1px solid rgba(255, 255, 255, 0.8);
                    font-size: 22px;
                    font-weight: 500;
                    color: #701e06;
                }

                .expired + .expire {
                    position: absolute;
                    right: 8px;
                    top: 6px;
                    color: #5c6476;
                    margin-top: 0 !important;
                    white-space: nowrap;
                    font-size: 18px;
                }
            }

            .tab.current {
                width: 320px;
                height: 160px;
                .inner {
                    width: 320px;
                    height: 160px;
                }
            }

            .kemu1 .inner {
                background: url(./images/<EMAIL>) no-repeat;
                background-size: cover;
            }
            .kemu2 .inner {
                background: url(./images/<EMAIL>) no-repeat;
                background-size: cover;
            }
            .kemu3 .inner {
                background: url(./images/<EMAIL>) no-repeat;
                background-size: cover;
            }
            .kemu4 .inner {
                background: url(./images/<EMAIL>) no-repeat;
                background-size: cover;
            }
        }

        .switch.two {
            .tab {
                width: 448px;
                height: 200px;

                .inner {
                    width: 448px;
                    height: 200px;
                }

                .title {
                    font-size: 38px;
                    line-height: 52px;
                }

                .expire {
                    margin-top: 16px;
                    font-size: 32px;
                    line-height: 44px;
                }

                .btn {
                    margin-top: 10px;
                    width: 216px;
                    height: 64px;
                    border-radius: 32px;
                    font-size: 32px;
                }
            }

            .tab.current {
                width: 560px;
                height: 250px;
            }
        }

        .switch.one {
            justify-content: flex-start;
        }
    }
    .sec3 {
        padding: 0 30px 40px;

        .sec3-title {
            margin-top: 40px;
            margin-bottom: 40px;
            font-size: 40px;
            font-weight: bold;
        }
        .sec3-list {
            .item {
                background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
                box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.09);
                border-radius: 8px;
                display: flex;
                padding: 0 30px 0 30px;
                justify-content: space-between;
                align-items: center;
                position: relative;
                height: 130px;
                margin-top: 30px;
                cursor: pointer;
                &:nth-child(1) {
                    background: url(./images/1.png) top left no-repeat #fff;
                    background-size: 130px 96px;
                    .title {
                        &::before {
                            content: '第一步';
                            padding-right: 10px;
                        }
                    }
                }
                &:nth-child(2) {
                    background: url(./images/2.png) top left no-repeat #fff;
                    background-size: 130px 96px;
                    .title {
                        &::before {
                            content: '第二步';
                            padding-right: 10px;
                        }
                    }
                }
                &:nth-child(3) {
                    background: url(./images/3.png) top left no-repeat #fff;
                    background-size: 130px 96px;
                    .title {
                        &::before {
                            content: '第三步';
                            padding-right: 10px;
                        }
                    }
                }
            }
            .title {
                font-size: 34px;
                font-weight: 500;
            }
            .desc {
                font-size: 28px;
                color: #666;
                span {
                    color: #2390fe;
                }
            }
            .btn {
                width: 160px;
                height: 54px;
                border-radius: 40px;
                border: 1px solid #2390fe;
                font-size: 32px;
                font-weight: 500;
                color: #2390fe;
                text-align: center;
                line-height: 54px;
            }
            .tag {
                position: absolute;
                right: 0;
                top: 0;
                width: 120px;
                height: 48px;
                border-radius: 0 8px 0 8px;
                font-size: 30px;
                text-align: center;
                line-height: 48px;
                &.done {
                    background-color: #fff0e6;
                    color: #c43634;
                }
                &.undone {
                    background-color: #e5f7ff;
                    color: #4d74ce;
                }
            }
        }
        .sec3-buchang {
            height: 140px;
            background: url(./images/12.png) no-repeat;
            background-size: cover;
            position: relative;

            &-title {
                position: absolute;
                left: 30px;
                top: 20px;
                font-size: 34px;
                font-weight: 500;
                color: #8f5333;
                line-height: 56px;
            }

            &-content {
                position: absolute;
                left: 30px;
                bottom: 20px;
                font-size: 28px;
                font-weight: 400;
                color: #b46938;
                line-height: 44px;
            }

            &-btn {
                position: absolute;
                width: 200px;
                height: 68px;
                right: 30px;
                top: 36px;
                background: linear-gradient(114deg, #ffffff 12%, #fff6e9 91%);
                border-radius: 40px;
                font-size: 30px;
                font-weight: 500;
                text-align: center;
                color: #8f5333;
                line-height: 68px;
                cursor: pointer;
            }
        }
    }
}
</style>
