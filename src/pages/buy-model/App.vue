<template>
    <div>
        <payPopup
            v-if="advertDetail.goodsKey"
            :key="advertDetail.icon + 'payPopup'"
            :advertDetail="advertDetail"
            :motionDetailProp="motionDetail"
            :remainSotckInfo="remainSotckInfo"
            :show.sync="payPopupVisible"
            :fragmentName1="fragmentName1"
            :fullScreen="false"
            :isInDialog="true"
            @showDetainmentPupop="showDetainmentPupop"
        />
        <template v-else>
            <div class="close" @click="close"></div>
            <loading bgColor="#666" />
        </template>
    </div>
</template>

<script>
import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    getBatchSellRate,
    getSellApplyScene,
    getAdvertDetail,
    getResourceDetail,
} from '../../server/topLesson'
import {getTrainingRecommendChannelCode} from '../../server/training'
import payPopup from '../../components/payPopup'
import loading from '../../components/loading'
import {URLParams, trackEvent, webOpen, setEmbeddedHeight, getAuthToken, goLogin} from '../../utils/tools'
import {RESOURCE_TYPE} from '../../utils/constant'
import {webClose} from '../../utils/jump'

let pageName = '直播间页'
export default {
    components: {payPopup, loading},
    data() {
        return {
            payPopupVisible: false,
            sellRateTimer: null,
            advertDetail: {},
            remainSotckInfo: {
                sellRateEnable: false,
                sellRateNum: 100,
                applyScene: '',
            },
            index: isNaN(URLParams.index) ? 0 : +URLParams.index,
            fragmentName1: '',
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    async created() {
        document.title = '直播间'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        this.fragmentName1 = URLParams.fragmentName1 || '未知片段'
        setEmbeddedHeight(558, 28)
        this.$EventBus.$on('setOrientation', (orientation, callback) => {
            callback && callback()
        })
        if (URLParams.carStyle === 'car' && URLParams.advertId) {
            this.initStockInfo()
        }
        let motionDetail
        if (URLParams.resourceId) {
            motionDetail = await getResourceDetail({
                resourceId: URLParams.resourceId,
                carType: URLParams.carStyle,
                sessionId: URLParams.id,
            })
            motionDetail.advertType = RESOURCE_TYPE[motionDetail.type]
        } else {
            motionDetail = await getAdvertDetail({
                advertId: URLParams.advertId,
                carType: URLParams.carStyle,
                sessionId: URLParams.id,
            })
            motionDetail.advertType = motionDetail.type
        }
        if (motionDetail.advertType === 'training') {
            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                await new Promise(resolve => {
                    pageSwitch.onPageShow(resolve)
                })
                const authToken = await getAuthToken()
                if (!authToken) {
                    webClose()
                }
                return
            }
            const {value} = await getTrainingRecommendChannelCode({
                carType: URLParams.carStyle || 'car',
                kemu: URLParams.kemuNum || '1',
                sceneCode: URLParams.sceneCode || '101',
            })
            motionDetail.goodsKey = value
        }
        motionDetail.detailUrl = motionDetail.detailUrl || motionDetail.jumpUrl
        motionDetail.goodsKey = motionDetail.goodsKey || motionDetail.goodsUniqueKey
        motionDetail.groupKey = motionDetail.goodsUniqueKey = motionDetail.goodsKey

        this.advertDetail = motionDetail
        this.motionDetail = motionDetail
        this.$nextTick(() => {
            this.payPopupVisible = true
        })
        this.log()
    },
    mounted() {
        this.$EventBus.$on('openHelpPopup', (success) => {
            if (success) {
                webOpen({
                    url: 'https://laofuzi.kakamobi.com/jkbd-qa/faq.html?qaCode=zbk',
                    titleBar: true,
                })

                let fragmentName1 = '支付弹窗'
                let actionName = '客服图标'
                let actionType = '点击'

                // 精品课直播间页_支付弹窗_点击客服图标
                trackEvent({fragmentName1, actionName, actionType, eventId: 'customer_service'})
            }
        })
    },
    methods: {
        close() {
            webClose()
        },
        showDetainmentPupop() {
            setEmbeddedHeight(420, 28)
        },
        async initStockInfo() {
            clearInterval(this.sellRateTimer)
            this.sellRateTimer = null
            this.getSellRate()
            this.sellRateTimer = setInterval(() => {
                // 安卓客户端 通过协议轮询接口 会影响登录成功callback，原因待查
                if (document.visibilityState === 'visible') {
                    this.getSellRate()
                }
            }, 10000)

            const resData = await getSellApplyScene({
                lessonItemId: URLParams.id,
                carType: URLParams.carStyle,
            })
            this.remainSotckInfo.applyScene = resData.value || ''
        },
        async getSellRate() {
            const resData = await getBatchSellRate(
                {
                    liveSessionId: URLParams.id,
                },
                {noConsole: true}
            )
            let dataList = resData.itemList.map(item => {
                let sellRateNum
                if (item.value === 0) {
                    sellRateNum = 0
                } else {
                    sellRateNum = Math.floor(item.value * 100)
                    sellRateNum = Math.max(sellRateNum, 1)
                }
                return {
                    sellRateEnable: item.enable,
                    sellRateNum,
                }
            })
            let saleData = dataList[this.index]
            this.remainSotckInfo.sellRateEnable = saleData.sellRateEnable
            this.remainSotckInfo.sellRateNum = saleData.sellRateNum
        },
        log() {
            let fragmentName1 = this.fragmentName1
            let actionType = '点击'
            let actionName = '去支付'

            // 精品课直播间页_直播间大挂件_点击去支付
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                payPathType: 0,
                groupKey: this.advertDetail.goodsKey,
                goodsUniqueKey: this.advertDetail.goodsKey,
            })
        },
    },
}
</script>

<style lang="less" scoped>
.close {
    position: absolute;
    z-index: 2;
    right: 14px;
    top: 14px;
    width: 50px;
    height: 50px;
    background: url(../../assets/images/<EMAIL>) no-repeat;
    background-size: 50px 50px;
}
</style>
