<template>
    <div class="app">
        <Header :title="title" />
        <div class="main" v-if="collectionData">
            <div class="intro">
                <div class="title">{{ collectionData.title }}</div>
                <div
                    :class="['handle', {collapsed}]"
                    @click="collapsed = !collapsed"
                    v-if="collapsed !== null"
                ></div>
                <div class="desc" ref="desc">{{ collectionData.desc }}</div>
            </div>
            <div class="tabs" ref="tabs">
                <div class="tab" :class="{active: section === 1}" @click="goView(1)">专项课程讲解</div>
                <div
                    class="tab"
                    :class="{active: section === 2}"
                    @click="goView(2)"
                    v-if="collectionData.coursewareDataList.length"
                >课件资料</div>
                <div class="tab" :class="{active: section === 3}" @click="goView(3)">经典例题</div>
            </div>
            <div class="section" id="section1">
                <div class="title">专项课程讲解</div>
                <div class="subtitle">讲师带学学知识 重点难点一网打尽</div>
                <div class="card">
                    <div class="video">
                        <img
                            class="cover"
                            :src="collectionData.lesson.coverImg[0].image"
                            @click="goDetailPage()"
                            v-if="collectionData.lesson.hasPermission"
                        />
                        <template v-else-if="collectionData.lesson.trialLesson">
                            <div class="media">
                                <video
                                    ref="video"
                                    type="video/mp4"
                                    :src="collectionData.lesson.trialLesson.url"
                                    autoplay
                                    controls
                                    webkit-playsinline
                                    playsinline="true"
                                    x5-playsinline
                                    :poster="collectionData.lesson.trialLesson.image"
                                    @ended="showMask = true"
                                />
                            </div>
                            <!-- <div class="tag">试听课</div> -->
                            <div class="mask" v-if="showMask && payData">
                                <div class="title">您未开通当前课程</div>

                                <div
                                    class="btn"
                                    @click="openModal('vip', '试看完成')"
                                    v-if="
                                        payData.popupDetail.groupKey && !payData.popupDetail.bought
                                    "
                                >
                                    <div>
                                        ￥{{ payData.vipGoodsDetail.price }}元{{
                                            collectionData.buttonTitle
                                        }}
                                    </div>
                                    <div class="mark" v-if="payData.lessonDetail.cornerMark">
                                        {{ payData.lessonDetail.cornerMark }}
                                    </div>
                                </div>
                                <div class="btn" @click="openModal('lesson', '试看完成')">
                                    <span>￥{{ payData.lessonDetail.price }}元直接购买</span>
                                </div>
                            </div>
                        </template>
                        <img
                            class="cover"
                            :src="collectionData.lesson.coverImg[0].image"
                            @click="openModal('lesson', '课程封面')"
                            v-else
                        />
                    </div>
                    <div class="info" @click="goDetailPage()">
                        <div class="name">{{ collectionData.lesson.subLessonCount }}个课时</div>
                        <div class="time">
                            {{ collectionData.lesson.duration | formatDuration }}
                        </div>
                        <div class="btn" v-if="collectionData.lesson.hasPermission">去上课</div>
                        <div class="btn btn2" v-else>查看完整课程</div>
                    </div>
                </div>
            </div>
            <div class="section" id="section2" v-if="collectionData.coursewareDataList.length">
                <div class="title">课件资料</div>
                <div class="subtitle">点击图片可以查看大图</div>
                <div class="notes">
                    <img
                        class="note"
                        v-for="(item, index) in collectionData.coursewareDataList"
                        :src="item"
                        :key="index"
                        @click="previewImage(index)"
                    />
                    <div class="more" @click="onMoreClick">
                        <div class="txt">查看全部资料</div>
                        <div class="pic" />
                    </div>
                </div>
            </div>
            <div id="section3" />
            <div id="_section3" class="section">
                <div class="title">经典例题</div>
                <div class="practice" @click="goPracticePage()">
                    <div class="txt" v-if="collectionData.questionCount">
                        可学会{{ collectionData.questionCount }}道题
                    </div>
                    <div class="txt" v-else>抓紧时间去练习</div>
                    <div class="btn">去做题</div>
                </div>
                <div
                    class="detail"
                    v-html="collectionData.questionDesc"
                    @click="onDetailClick"
                    ref="detail"
                />
            </div>
        </div>

        <div class="footer" v-if="payData">
            <div class="placeholder safearea" />
            <div class="inner">
                <div class="btns">
                    <div class="btn" @click="buyGoods('lesson', '底部吸底左侧按钮')">
                        ￥{{ payData.lessonDetail.price }}元直接购买
                    </div>
                    <div
                        class="btn"
                        @click="buyGoods('vip', '底部吸底右侧按钮')"
                        v-if="payData.popupDetail.groupKey && !payData.popupDetail.bought"
                    >
                        <div>
                            ￥{{ payData.vipGoodsDetail.price }}元{{ collectionData.buttonTitle }}
                        </div>
                        <div class="mark" v-if="payData.lessonDetail.cornerMark">
                            {{ payData.lessonDetail.cornerMark }}
                        </div>
                    </div>
                </div>
                <vipAgreement theme="red" :showAgreement="false"></vipAgreement>
                <div class="safearea" />
            </div>
        </div>

        <div
            class="modal"
            v-show="showModal"
            v-if="collectionData && !collectionData.lesson.hasPermission"
        >
            <div :style="{height: (payData ? payData.viewHeight : 0) + 'px'}">
                <BuyModal
                    :isRoot="false"
                    :lessonId="collectionData.lesson.lessonId"
                    :fragmentName1="fragmentName1"
                    @payData="onPayData"
                    @close="close"
                    ref="buyModal"
                />
            </div>
        </div>
    </div>
</template>

<script>
/* eslint-disable object-curly-spacing */
import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {getUrl, trackEvent, URLParams, webOpen, trackPageShow} from '../../utils/tools'
import BuyModal from '../lesson/App.vue'
import Header from './header.vue'
import vipAgreement from '../../components/vipAgreement'
import {getSpecialStrengthen} from '../../server/collection'
import * as Stickyfill from 'stickyfilljs'
import {MCProtocol} from '@simplex/simple-base'

MCProtocol.register('jiakao-global.showImage', config => {
    return config
})

MCProtocol.register('data.tag.questionCount', config => {
    return config
})

function skipUntil(arr, fn) {
    const ret = []
    let found = false
    for (let i = 0; i < arr.length; i++) {
        if (found || fn(arr[i])) {
            found = true
            ret.push(arr[i])
        }
    }
    return ret
}

function formatDuration(_seconds) {
    const seconds = _seconds % 60
    const _minutes = (_seconds - seconds) / 60
    const minutes = _minutes % 60
    const hours = (_minutes - minutes) / 60

    return skipUntil([hours, minutes, seconds], num => num > 0)
        .map(num => (num < 10 ? '0' + num : num))
        .join(':')
}

const pageName = '强化专项合集页'
export default {
    components: {
        BuyModal,
        Header,
        vipAgreement,
    },
    filters: {
        formatDuration,
    },
    data() {
        return {
            title: '驾考宝典',
            collapsed: null,
            section: 1,
            showMask: false,

            fragmentName1: '',
            showModal: false,

            tagId: URLParams.id,
            payData: null,
            collectionData: null,
            autoPause: false,
        }
    },
    watch: {},
    created() {
        stat.setPageName(pageName)
    },
    mounted() {
        getSpecialStrengthen({
            tagId: this.tagId,
            carType: URLParams.carStyle,
            kemu: URLParams.kemuStyle,
            sceneCode: URLParams.sceneCode,
        }).then(res => {
            if (res.lesson && res.lesson.coverImg) {
                res.lesson.coverImg = JSON.parse(res.lesson.coverImg)
            }

            this.collectionData = res
            this.title = res.title

            URLParams.id = this.collectionData.lesson.lessonId
            URLParams.lessonId = ''

            trackPageShow({
                actionType: '展示',
                payStatus: this.collectionData.lesson.hasPermission ? '1' : '2',
            })

            this.$nextTick(() => {
                // 是否显示展开按钮
                const baseFontSize = parseInt(document.documentElement.style.fontSize)
                const fiveLinesHeight = 5.5 * baseFontSize * (40 / 100)
                if (this.$refs.desc.clientHeight > fiveLinesHeight) {
                    this.collapsed = true
                }

                // sticky定位低版本兼容
                Stickyfill.addOne(this.$refs.tabs)
            })

            this.getQuestionCount()
        })
        pageSwitch.onPageHide(() => {
            if (this.$refs.video && !this.$refs.video.paused) {
                this.$refs.video.pause()
                this.autoPause = true
            }
        })
        pageSwitch.onPageShow(() => {
            if (this.$refs.video && this.autoPause) {
                this.$refs.video.play()
                this.autoPause = false
            }
        })
    },
    methods: {
        goView(num) {
            this.section = num
            document.getElementById('section' + num).scrollIntoView()
        },
        onPayData(payData) {
            this.payData = payData
        },
        openModal(selectGoods, fragmentName1) {
            trackEvent({
                fragmentName1,
                actionType: '点击',
                actionName: '去支付',
                payPathType: 0,
                payStatus: this.collectionData.lesson.hasPermission ? '1' : '2',
            })
            this.$refs.buyModal.changeGoods(selectGoods)
            this.fragmentName1 = fragmentName1
            this.showModal = true
        },

        buyGoods(selectGoods, fragmentName1) {
            trackEvent({
                fragmentName1,
                actionType: '点击',
                actionName: '去支付',
                payPathType: 1,
                payStatus: this.collectionData.lesson.hasPermission ? '1' : '2',
            })
            this.$refs.buyModal.changeGoods(selectGoods)
            this.fragmentName1 = fragmentName1
            this.showModal = true
        },

        close() {
            this.showModal = false
        },

        goDetailPage(tab = '') {
            webOpen({
                url: getUrl('http://jiakao.nav.mucang.cn/topLesson/detail', {
                    id: this.collectionData.lesson.lessonId,
                    from: URLParams.from,
                    fromItemCode: URLParams.fromItemCode || '',
                    tab,
                }),
            })
        },

        getQuestionCount() {
            return new Promise(resolve =>
                MCProtocol.data.tag.questionCount({
                    tagId: this.tagId,
                    callback: res => {
                        console.log('MCProtocol.data.tag.questionCount', res)
                        this.collectionData.questionCount = +res.data.count
                        this.$forceUpdate()
                        resolve()
                    },
                })
            )
        },

        async goPracticePage() {
            trackEvent({
                actionType: '点击',
                actionName: '去练习',
                payStatus: this.collectionData.lesson.hasPermission ? '1' : '2',
            })

            if (!this.collectionData.questionCount) {
                await this.getQuestionCount()
            }

            if (!this.collectionData.questionCount) {
                webOpen({
                    url: 'http://jiakao.nav.mucang.cn/update-tiku',
                })
            } else {
                webOpen({
                    url: getUrl('http://jiakao.nav.mucang.cn/topic-dispersed-training-practice', {
                        tagId: this.tagId,
                        kemu: URLParams.kemu,
                    }),
                })
            }
        },

        previewImage(index) {
            MCProtocol['jiakao-global'].showImage({
                images: this.collectionData.coursewareDataList.join(','),
                index,
            })
        },

        onMoreClick() {
            if (this.collectionData.lesson.hasPermission) {
                this.goDetailPage('courseware')
            } else {
                this.openModal('lesson', '课件资料')
            }
        },

        onDetailClick(e) {
            const target = e.target
            if (target instanceof HTMLImageElement) {
                const src = target.src
                const images = Array.from(this.$refs.detail.querySelectorAll('img')).map(
                    ele => ele.src
                )
                MCProtocol['jiakao-global'].showImage({
                    images: images.join(','),
                    index: images.indexOf(src),
                })
            }
        },
    },
}
</script>

<style lang="less">
html,
body {
    height: 100vh;
    overflow: hidden;
}
</style>

<style lang="less" scoped>
@import '../../assets/styles/main';
@import '../../assets/styles/variables';

.app {
    background: #f4f7f7;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.main {
    flex: 1 0 0;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

.intro {
    background: #ffffff;
    padding: 20px 30px 48px;
    position: relative;

    .title {
        font-size: 40px;
        font-weight: bold;
        color: #333333;
        line-height: 56px;
    }

    .desc {
        margin-top: 20px;
        font-size: 28px;
        color: #666666;
        line-height: 40px;
    }

    .handle {
        position: absolute;
        right: 30px;
        bottom: 10px;
        display: flex;
        align-items: center;
        font-size: 26px;
        color: #0e3669;
        line-height: 36px;

        &:before {
            content: '收起';
        }

        &:after {
            content: '';
            background: url(./images/<EMAIL>) no-repeat;
            background-size: cover;
            width: 28px;
            height: 18px;
            transform: rotate(180deg);
        }

        &.collapsed {
            &:before {
                content: '展开';
            }

            &:after {
                transform: none;
            }

            & + .desc {
                display: -webkit-box;
                text-overflow: ellipsis;
                overflow: hidden;
                -webkit-line-clamp: 5;
                -webkit-box-orient: vertical;
            }
        }
    }
}

.tabs {
    position: sticky;
    z-index: 1;
    top: 0;
    margin-top: 16px;
    height: 114px;
    line-height: 114px;
    background: #ffffff;
    display: flex;
    align-items: center;

    .tab {
        flex: 1 0 0;
        font-size: 30px;
        text-align: center;
        color: #999999;

        &.active {
            font-size: 36px;
            font-weight: bold;
            color: #333333;
            position: relative;

            &:after {
                position: absolute;
                content: '';
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 6px;
                background: #333333;
                border-radius: 4px;
            }
        }
    }
}

.section {
    background: #ffffff;
    padding: 30px 30px 40px;

    .title {
        font-size: 36px;
        font-weight: bold;
        color: #333333;
        line-height: 50px;
    }

    .subtitle {
        font-size: 28px;
        color: #999999;
        line-height: 40px;
        margin-top: 2px;
    }

    .card {
        margin-top: 20px;
        width: 690px;
        background: #ffffff;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.09);

        .video {
            position: relative;
            height: 386px;
            z-index: 0;

            .media {
                position: relative;
                z-index: 0;
            }

            video,
            .cover {
                width: 100%;
                height: 100%;
            }

            .tag {
                position: absolute;
                z-index: 1;
                left: 10px;
                bottom: 10px;
                width: 90px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                background: rgba(0, 0, 0, 0.7);
                border-radius: 8px;
                font-size: 22px;
                font-weight: bold;
                color: #ffffff;
            }
        }

        .mask {
            position: absolute;
            z-index: 1;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.75);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;

            .title {
                font-size: 28px;
                color: #efd893;
                line-height: 40px;
                margin-bottom: 30px;
            }

            > .btn:last-child {
                width: 480px;
                height: 76px;
                line-height: 76px;
                border: 1px solid #979797;
                border-radius: 40px;
                font-size: 28px;
                color: #ffffff;
            }

            > .btn:nth-child(2) {
                margin-bottom: 20px;
                position: relative;
                width: 480px;
                height: 76px;
                line-height: 76px;
                background: linear-gradient(103deg, #f9dbc0 35%, #efaf8b 90%);
                border-radius: 40px;
                font-size: 28px;
                font-weight: bold;
                color: #692204;
            }

            > .btn:last-child:nth-child(2) {
                border: none;
            }
        }

        .info {
            height: 116px;
            display: flex;
            align-items: center;

            .name {
                margin-left: 30px;
                font-size: 34px;
                font-family: PingFangSC, PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                color: #333333;
                line-height: 48px;
            }

            .time {
                flex: 1;
                margin-left: 20px;
                font-size: 26px;
                color: #666666;
                margin-top: 4px;
            }

            .btn {
                margin-right: 16px;
                width: 166px;
                height: 60px;
                background: #04a5ff;
                border-radius: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 28px;
                font-weight: bold;
                color: #ffffff;

                &:after {
                    content: '';
                    background: url(./images/<EMAIL>) no-repeat;
                    background-size: cover;
                    width: 30px;
                    height: 30px;
                    margin-top: -3px;
                }
            }

            .btn2 {
                width: 240px;
                height: 60px;
                background: linear-gradient(315deg, #ff4a40, #ff7d76);
            }
        }
    }

    .notes {
        margin-top: 30px;
        display: flex;

        > *:not(:first-child) {
            margin-left: 10px;
        }

        .note {
            width: 226px;
            height: 226px;
            border: 1px solid #f4f4f9;
            border-radius: 16px;
            object-fit: cover;
        }

        .more {
            width: 218px;
            height: 226px;
            background: #f4f9ff;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .txt {
                font-size: 26px;
                font-weight: bold;
                color: #04a5ff;
                line-height: 36px;
            }

            .pic {
                margin-top: 20px;
                width: 56px;
                height: 38px;
                background: url(./images/arrow.png) no-repeat;
                background-size: cover;
            }
        }
    }

    .practice {
        margin: 20px auto 30px;
        width: 690px;
        height: 90px;
        background: url(./images/<EMAIL>) no-repeat;
        background-size: cover;
        position: relative;
        display: flex;
        align-items: center;
        font-size: 30px;
        color: #0e3669;
        line-height: 42px;
        padding-left: 30px;

        .btn {
            position: absolute;
            top: 16px;
            right: 28px;
            width: 140px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #ffffff;
            border-radius: 30px;
            box-shadow: 0px 2px 8px 0px #9bccff;
            font-size: 24px;
            color: #103b6f;

            &:after {
                content: '';
                background: url(./images/<EMAIL>) no-repeat;
                background-size: cover;
                width: 28px;
                height: 18px;
                transform: rotate(-90deg);
                margin-top: -2px;
            }
        }
    }
}

#section2,
#_section3 {
    margin-top: 20px;
}

.footer {
    .agreement {
        padding-left: 30px;
    }
    .placeholder {
        padding-top: 164px;
        box-sizing: content-box;
    }

    .inner {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        background: #ffffff;
        box-shadow: 0px 2px 0px 0px #e8e8e8 inset;
    }

    .btns {
        padding: 24px 30px 16px;
        display: flex;
        height: 88px;
        box-sizing: content-box;
        line-height: 88px;
        text-align: center;

        > .btn:first-child {
            flex: 1;
            background: linear-gradient(113deg, #3d445a 10%, #262c38 91%);
            border-radius: 44px;
            font-size: 28px;
            color: #ffffff;
        }

        > .btn:last-child {
            margin-left: 24px;
            position: relative;
            width: 402px;
            background: linear-gradient(103deg, #f9dbc0 35%, #efaf8b 90%);
            border-radius: 44px;
            font-size: 28px;
            font-weight: bold;
            color: #692204;
        }

        > .btn:first-child:last-child {
            margin-left: 0;
        }
    }

    .safearea {
        min-height: 30px;
        height: constant(safe-area-inset-bottom);
        height: env(safe-area-inset-bottom);
    }
}

.mark {
    position: absolute;
    padding: 0 10px;
    top: -20px;
    right: 0;
    height: 40px;
    line-height: 40px;
    .fontSizeWithElder(24px);
    color: #fff;
    background: linear-gradient(90deg, #ff7810 0%, #fe3c29 55%, #fe6164 100%);
    border-radius: 60px 60px 60px 8px;
    max-width: 600px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.modal {
    position: fixed;
    z-index: 5;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    background: rgba(0, 0, 0, 0.6);
}
</style>
