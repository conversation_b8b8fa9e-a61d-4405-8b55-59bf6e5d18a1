<template>
    <div class="container" @click="$emit('close')">
        <swiper ref="swiper" :options="{autoHeight: true, initialSlide: index}">
            <swiper-slide v-for="(item, index) in images" :key="index">
                <img class="pic" :src="item" />
            </swiper-slide>
        </swiper>
        <div class="download" @click.stop="saveImage()" />
    </div>
</template>

<script>
/* eslint-disable object-curly-spacing */
import 'swiper/css/swiper.css'
import {MCProtocol} from '@simplex/simple-base'
let Swiper = (resolve, reject) => {
    import('vue-awesome-swiper').then(sss => {
        resolve(sss.Swiper)
    })
}
let SwiperSlide = (resolve, reject) => {
    import('vue-awesome-swiper').then(sss => {
        resolve(sss.SwiperSlide)
    })
}

export default {
    components: {
        Swiper,
        SwiperSlide,
    },
    props: {
        images: {
            type: Array,
            default: () => [],
        },
        index: {
            type: Number,
            default: 0,
        },
    },
    methods: {
        saveImage() {
            MCProtocol.Core.Native.saveImage({
                data: this.images[this.$refs.swiper.$swiper.activeIndex],
                callback(res) {
                    if (!res.success) {
                        console.log('图片保存失败', res)
                    }
                },
            })
        },
    },
}
</script>

<style scoped>
.container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 40px;
}

.pic {
    width: 100%;
}

.download {
    position: absolute;
    z-index: 1;
    right: 60px;
    bottom: 80px;
    width: 80px;
    height: 80px;
    background: red no-repeat;
    background-size: cover;
}
</style>
