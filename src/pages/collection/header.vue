<template>
    <div class="top-header" :style="{paddingTop, height}">
        <div class="header white" :style="{paddingTop, height}">
            <div class="navbar">
                <div class="back" @click="back"></div>
                <div class="title">
                    <div>{{ title }}</div>
                </div>
                <div class="right" />
            </div>
        </div>
    </div>
</template>
<script>
import {MCProtocol} from '@simplex/simple-base'
import {isAndroid} from '../../utils/tools'
import {webClose} from '../../utils/jump'

MCProtocol.Core.Web.setting({
    titleBar: false,
    menu: false,
    noTopInset: true,
    fullScreen: true,
})

export default {
    props: {
        title: {
            type: String,
            default: '驾考宝典',
        },
    },
    data() {
        return {
            paddingTop: '30px',
            height: '45px',
        }
    },
    mounted() {
        this.setStatusBarHeight()
    },
    methods: {
        back() {
            webClose()
        },
        setStatusBarHeight() {
            MCProtocol.Core.System.env(data => {
                let statusBarHeight = data.data.statusBarHeight

                if (statusBarHeight) {
                    statusBarHeight += 'px'
                } else {
                    statusBarHeight = 'env(safe-area-inset-top)'
                }

                this.paddingTop = statusBarHeight
                this.height = isAndroid ? '48px' : '44px'
            })
        },
    },
}
</script>

<style lang="less" scoped>
.top-header {
    height: 90px;
    box-sizing: content-box;

    .header {
        position: fixed;
        z-index: 999;
        top: 0;
        left: 0;
        right: 0;
        height: 90px;
        box-sizing: content-box;

        .navbar {
            display: flex;
            align-items: center;
            padding: 0 30px;
            max-width: 750px;
            height: 100%;
            margin: 0 auto;
        }

        .back {
            width: 50px;
            height: 50px;
            background-size: 50px 50px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        .title {
            flex: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;

            > div {
                width: 580px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .right {
            width: 44px;
            height: 44px;
            background-size: 44px 44px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        &.white {
            background-color: white;

            .title {
                color: #333;
            }

            .back {
                background-image: url(./images/black_back.png);
            }
        }
    }
}
</style>
