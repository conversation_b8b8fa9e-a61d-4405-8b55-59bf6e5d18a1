<template>
    <examAnswerSheetPopup :show.sync="visible" :isInDialog="true" :sessionId="sessionId" :activityId="activityId" />
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import examAnswerSheetPopup from '../../components/examAnswerSheetPopup'
import {URLParams, setEmbeddedHeight} from '../../utils/tools'

MCProtocol.register('Vip.enableOutsideCancel', function(config) {
    return config
})

let pageName = '答题卡'
export default {
    components: {examAnswerSheetPopup},
    data() {
        return {
            visible: false,
            sessionId: URLParams.id,
            activityId: URLParams.activityId,
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '答题卡'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(558)
    },
    mounted() {
        MCProtocol.Vip.enableOutsideCancel({
            enable: true,
        })
        this.visible = true
    },
}
</script>

<style lang="less">
</style>
