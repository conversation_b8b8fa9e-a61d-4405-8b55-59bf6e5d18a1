<template>
    <div class="goods-list-page">
        <div class="goods-list-home" v-show="visible">
            <div @click="close" class="close"></div>
            <div class="wrap">
                <div class="header">主播推荐</div>
                <goodsList v-if="goodsDataList && goodsDataList.length" :isInDialog="true" :dataList="goodsDataList" @close="hide" />
                <div class="loading" v-else-if="loading">
                    <loading bgColor="#666" />
                </div>
                <div v-else class="no-data">暂无数据</div>
            </div>
        </div>
    </div>
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {getGoodsList} from '../../server/topLesson'
import goodsList from '../../components/goodsList'
import loading from '../../components/loading'
import {URLParams, trackEvent, setEmbeddedHeight, webOpen} from '../../utils/tools'
import {webClose} from '../../utils/jump'

MCProtocol.register('Vip.enableHalfScroll', function(config) {
    return config
})

let pageName = '直播间页'
export default {
    components: {goodsList, loading},
    data() {
        return {
            visible: true,
            goodsDataList: [],
            loading: true,
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '直播间'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(558)
        MCProtocol.Vip.enableHalfScroll({
            enable: false,
        })
        this.$EventBus.$on('setOrientation', (orientation, callback) => {
            callback && callback()
        })
        this.getGoodsList()
        this.log()
    },
    mounted() {
        this.$EventBus.$on('openHelpPopup', (success) => {
            if (success) {
                webOpen({
                    url: 'https://laofuzi.kakamobi.com/jkbd-qa/faq.html?qaCode=zbk',
                    titleBar: true,
                })

                let fragmentName1 = '主播推荐商品弹窗'
                let fragmentName2 = '支付弹窗'
                let actionName = '客服图标'
                let actionType = '点击'

                // 精品课直播间页_主播推荐商品弹窗_支付弹窗_点击客服图标
                trackEvent({fragmentName1, fragmentName2, actionName, actionType, eventId: 'customer_service'})
            }
        })
    },
    methods: {
        closed() {
            this.close()
        },
        close() {
            webClose()
        },
        hide() {
            this.visible = false
        },
        async getGoodsList() {
            let resData = await getGoodsList({
                sessionId: URLParams.id,
                resourceId: URLParams.resourceId,
            })
            this.loading = false
            resData = resData.itemList || resData
            this.goodsDataList = resData.map(item => {
                return {
                    ...item,
                    advertType: item.type,
                    icon: item.img,
                    popupBuyTag: (item.tag || '').split('|').filter(item => item),
                    detailUrl: item.jumpUrl,
                }
            })
        },
        log() {
            let fragmentName1 = '主播推荐商品弹窗'
            let actionType = '出现'

            // 精品课直播间页_主播推荐商品弹窗_出现
            trackEvent({fragmentName1, actionType})
        },
    },
}
</script>

<style lang="less">
.goods-list-page {
    background: #ffffff;
    height: 100%;
    .goods-list-home {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        .close {
            position: absolute;
            right: 24px;
            top: 18px;
            width: 50px;
            height: 50px;
            background: url(../../assets/images/<EMAIL>) no-repeat;
            background-size: 50px 50px;
            z-index: 1;

            &:after {
                content: '';
                position: absolute;
                left: -20%;
                right: -20%;
                top: -20%;
                bottom: -20%;
            }
        }
        .loading {
            flex: 1;
            position: relative;
        }
        .header {
            height: 98px;
            padding-left: 30px;
            font-size: 36px;
            line-height: 98px;
            font-weight: bold;
            color: #000;
        }
        .wrap {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .no-data {
            background: url(../../assets/images/no-data.png) no-repeat center 40px;
            background-size: 440px 334px;
            font-size: 28px;
            text-align: center;
            padding-top: 402px;
            padding-bottom: 80px;
        }
    }
}
</style>
