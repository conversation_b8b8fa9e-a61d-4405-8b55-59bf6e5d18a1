<template>
    <div>
        <lessonRecommendPop :show.sync="visible" :isInDialog="true" />
    </div>
</template>

<script>
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import lessonRecommendPop from '../index/components/lessonRecommendPop'
import {URLParams, trackEvent, setEmbeddedHeight} from '../../utils/tools'

let pageName = '直播间页'
export default {
    components: {lessonRecommendPop},
    data() {
        return {
            visible: false,
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '直播间'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(558)
        this.$EventBus.$on('setOrientation', (orientation, callback) => {
            callback && callback()
        })
        this.log()
    },
    mounted() {
        this.visible = true
    },
    methods: {
        log() {
            let fragmentName1 = '好课推荐弹窗'
            let actionType = '出现'

            // 精品课直播间页_好课推荐弹窗_出现
            trackEvent({fragmentName1, actionType})
        },
    },
}
</script>

<style lang="less">
</style>
