<template>
    <div @click.prevent class="page-container" id="J-page-container" :key="roomDetail.timestamp">
        <button class="h-back" v-if="isLandscape" @click="toggleRrientation"></button>
        <verticalTop v-if="roomDetail.timestamp && bizConfig.playStatus" :showTitle="true" />
        <loading v-if="!roomDetail.timestamp && !showErrorTips" />
        <videoPlayer v-if="roomDetail.timestamp" :isShowFullScreen="isShowFullScreen" v-show="bizConfig.playStatus">
            <!-- <template v-slot:default>
                <barrage
                    ref="barrage"
                    :isPause="bulletChat.isPause"
                    :isOpen="isShowDanmu"
                    :percent="90"
                />
            </template> -->
            <!-- <question /> -->
        </videoPlayer>
        <template v-if="bizConfig.playStatus === 1 || bizConfig.playStatus === 2 || showErrorTips">
            <videoBanner
                :showBg="true"
                fragmentName1="横条运营位"
                v-if="roomDetail.orientation !== 1 && (bizConfig.saleMode || bizConfig.stMode) && roomResource.cardDetail.advertType"
                :advertDetail="roomResource.cardDetail"
            />
            <div class="interact-wrap">
                <majorChatTab
                    v-if="roomDetail.timestamp || showErrorTips"
                    :startPlay="startPlay"
                    :showErrorTips="showErrorTips"
                />
            </div>
            <autoPopup v-if="roomDetail.timestamp && isMucang" />
        </template>
        <div class="showcase" v-else-if="bizConfig.playStatus === 3">
            <warmupMotion
                v-if="(bizConfig.saleMode || bizConfig.stMode) && warmupDetail.advertType"
                :advertDetail="warmupDetail"
            />
        </div>
    </div>
</template>

<script>
import {mapMutations, mapState} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {debounce} from 'lodash-es'
import {MCProtocol} from '@simplex/simple-base'
import {
    isMucang,
    URLParams,
    isAndroid,
    isIOS,
    isHarmony,
    isTablet,
    trackEvent,
    compareVersionComplex,
    addClass,
    removeClass,
    trackPageShow,
    updateSessionId,
    toast,
    getAuthToken,
    goLogin,
} from '../../utils/tools'
import roomStatusMixin from '../../utils/roomStatusMixin'
import {ROOM_TYPE, RESOURCE_TYPE} from '../../utils/constant'
import {reload, webClose} from '../../utils/jump'
import {queryOrderStatus} from '../../utils/payHelper'
import {getLiveDetail, inform, getResource} from '../../server/topLesson'
import {getTrainingRecommendChannelCode} from '../../server/training'
import {getPromotionActivityRule} from '../../server/active'
import loading from '../../components/loading.vue'
// import barrage from '../../components/barrage'
import verticalTop from './components/verticalTop.vue'
import videoBanner from '../../components/videoBanner.vue'
import majorChatTab from './components/majorChatTab.vue'
import warmupMotion from '../../components/warmupMotion'
import autoPopup from './components/autoPopup.vue'
// import passRate from './components/passRate.vue'
import videoPlayer from '../../components/videoPlayer.vue'
// import question from './components/question.vue'

// TODO 加载优化，进入直播页时，客户端只请求播放地址，而不是全部直播间信息

// http://jiakao.nav.mucang.cn/topLesson/live-channel?type=free
// 常规直播 NORMAL
// 66学车节 66ACTIVITY
// 公共直播 COMMON
// VIP专项直播 VIP
// 长辈版专属直播 ELDER
// 刷题班 ST
// 科二科三直播 firstRoom
// http://jiakao.nav.mucang.cn/topLesson/vip-exclusive-live
// http://jiakao.nav.mucang.cn/topLesson/live?id=3110
// http://jiakao.nav.mucang.cn/live/room?anchorId=78
// http://jiakao.nav.mucang.cn/live/record?anchorId=125&sessionId=17589
// 安卓8.3.2&ios8.3.6 新增参数透传

let pageName = '精品课直播间页'
export default {
    mixins: [roomStatusMixin],
    components: {
        loading,
        // barrage,
        verticalTop,
        videoBanner,
        majorChatTab,
        warmupMotion,
        autoPopup,
        // passRate,
        videoPlayer,
        // question,
    },
    data() {
        return {
            isMucang,
            showErrorTips: false,
            // tabList: [
            //     {
            //         name: '互动',
            //         key: 'majorChat',
            //     },
            // ],
            persuade: false,
            isIOS,
            // bulletChat: {
            //     isPause: false,
            //     direction: 'default',
            // },
            anchorIdentity: 'official',
            liveRoomMode: !!URLParams.anchorId,
            pupopList: [],
            waitPupopList: [],
            startPlay: false,
        }
    },
    provide() {
        return {
            anchorIdentity: this.anchorIdentity,
            liveRoomMode: this.liveRoomMode,
            pupopList: this.pupopList,
            waitPupopList: this.waitPupopList,
        }
    },
    computed: {
        ...mapState([
            'roomDetail',
            'bizConfig',
            'roomStatus',
            'pendantResource',
            'isLandscape',
            'roomResource',
            // 'isShowDanmu',
        ]),
        warmupDetail() {
            return this.roomResource.warmupDetail
        },
        isShowFullScreen() {
            if (this.roomDetail.orientation === 1) {
                return false
            } else if (isTablet || isHarmony) {
                return false
            } else if (isIOS) {
                return compareVersionComplex('8.3.1') > 0
            } else {
                return compareVersionComplex('8.2.8') > 0
            }
        },
        // showPassRate() {
        //     if (URLParams.kemu === 'kemu1' && URLParams.carStyle === 'car') {
        //         if (isIOS) {
        //             return compareVersionComplex('8.13.0') >= 0
        //         } else {
        //             return compareVersionComplex('8.18.0') >= 0
        //         }
        //     } else {
        //         return false
        //     }
        // },
    },
    watch: {
        pupopList() {
            this.autoPopup()
        },
        waitPupopList() {
            this.autoPopup()
        },
        'bizConfig.playStatus': {
            handler(val, oldVal) {
                if (oldVal === 0) {
                    if (val === 3 && this.roomDetail.orientation === 2) {
                        addClass(document.body, 'vertical')
                        removeClass(document.body, 'portrait')
                    }
                } else {
                    if (val === 3 || oldVal === 3) {
                        reload()
                    }
                }
            },
        },
        'roomStatus.sessionId': {
            handler(val) {
                if (this.liveRoomMode) {
                    updateSessionId(val)
                    this.init()
                }
            },
        },
    },
    created() {
        // 不展示弹幕
        // this.$EventBus.$on('sendBullet', item => {
        //     this.$refs['barrage'].receiveDanmu(item)
        // })
        let isLandscape = Math.abs(window.orientation) === 90
        if (isHarmony && isTablet) {
            isLandscape = false
        }
        this.setLandscape(isLandscape)
        stat.setPageName(pageName)

        this.$EventBus.$on('startPlay', val => {
            this.startPlay = val
        })
        MCProtocol.Core.Web.setting({
            titleBar: false,
            menu: false,
        })

        MCProtocol.Core.Web.requestInterceptClose()

        MCProtocol.Listener.onWebviewClose(() => {
            this.beforeClose()
        })
        if (!this.liveRoomMode) {
            this.init()
        }

        // 玄学代码，当从直播间，进入一个横屏的原生页面后，页面会异常，iso和安卓都会
        // window.addEventListener('orientationchange', () => {
        //     switch (window.orientation) {
        //         case 90:
        //         case -90:
        //             this.setLandscape(true)
        //             // this.isLandscape = true
        //             break
        //         default:
        //             this.setLandscape(false)
        //         // this.isLandscape = false
        //     }
        // })

        window.addEventListener('resize', () => {
            this.$EventBus.$emit('resize')
        })

        this.$EventBus.$on('webClose', () => {
            if (isAndroid) {
                setTimeout(() => {
                    webClose()
                }, 300)
                this.$EventBus.$emit('disconnect')
            } else {
                webClose()
            }
        })

        this.$EventBus.$on('beforeClose', () => {
            this.beforeClose()
        })

        this.$EventBus.$on('setOrientation', (orientation, callback) => {
            this.setOrientation(orientation, callback)
        })
        this.$EventBus.$on('toggleRrientation', () => {
            this.toggleRrientation()
        })
        // this.$EventBus.$on('toggleDanmu', () => {
        //     this.toggleDanmu()
        // })
        this.$EventBus.$on(['onLogin', 'updatePendantResource'], async () => {
            await this.getRoomDetail(URLParams.id)
        })
    },
    mounted() {
        queryOrderStatus('vip', {
            toStatus: false,
            closeSelf: false,
            reFresh: true,
            toLogin: true,
            sendMessage: false,
            useNewApi: false,
        })
    },
    methods: {
        ...mapMutations([
            'setLandscape',
            // 'setShowDanmu',
        ]),
        pingErrorHandler(error) {
            if (!this.roomStatus.sessionId) {
                if (!this.showErrorTips && error && error.message === '直播场次不存在') {
                    toast('直播尚未开始，请稍后重试')
                }
                this.showErrorTips = true
            }
        },
        async init() {
            inform({
                id: URLParams.id,
            })
            if (!isMucang) {
                const authToken = await getAuthToken()
                if (!authToken) {
                    await goLogin({
                        frozen: true,
                    })
                }
            }
            this.getRoomDetail(URLParams.id)
        },
        autoPopup: debounce(function() {
            if (this.pupopList.length === 0 && this.waitPupopList.length > 0) {
                let pupop = this.waitPupopList.pop()
                pupop.callback()
            }
        }, 50),
        // changeTab(tabKey) {
        //     let actionType = '点击'
        //     let actionName
        //     if (tabKey === 'majorChat') {
        //         actionName = '互动tab'
        //         // 埋点梳理-驾考宝典-1129
        //         // 精品课直播间页_点击互动tab
        //     } else if (tabKey === 'lessonRecommend') {
        //         actionName = '好课推荐tab'
        //         // 埋点梳理-驾考宝典-1129
        //         // 精品课直播间页_点击好课推荐tab
        //     }

        //     if (actionName) {
        //         trackEvent({actionType, actionName})
        //     }
        // },
        beforeClose() {
            if (this.pupopList.length) {
                if (isAndroid) {
                    let pupop = this.pupopList.pop()
                    pupop.callback()
                } else {
                    this.$EventBus.$emit('webClose')
                }
            } else if (this.taskList.length && !this.persuade) {
                this.taskList.sort((a, b) => {
                    return a.priority - b.priority
                })
                this.persuade = true
                let task = this.taskList.pop()
                task.callback()
            } else {
                this.$EventBus.$emit('webClose')
            }
        },
        async getRoomDetail(id) {
            try {
                const resData = await getLiveDetail({
                    id,
                    carType: URLParams.carStyle,
                })
                document.title = resData.title
                resData.sessionId = id
                resData.roomType = resData.roomType || 1
                // resData.orientation = 1
                // resData.roomId = 7881
                if (resData.orientation === 1) {
                    addClass(document.body, 'vertical')
                    removeClass(document.body, 'portrait')
                }
                let {roomType, free} = resData
                let saleMode =
                    roomType === ROOM_TYPE.CGZB ||
                    roomType === ROOM_TYPE.HYZX ||
                    roomType === ROOM_TYPE.ZBZB ||
                    roomType === ROOM_TYPE.ZBDH ||
                    roomType === ROOM_TYPE.ZDSTB_FREE ||
                    roomType === ROOM_TYPE.ZBMK
                let stMode =
                    roomType === ROOM_TYPE.KQFD ||
                    roomType === ROOM_TYPE.ZXGK ||
                    roomType === ROOM_TYPE.ZDSTB_OLD ||
                    roomType === ROOM_TYPE.ZDSTB
                this.$store.commit('updateBizConfig', {
                    saleMode: saleMode || (free && stMode),
                    stMode: stMode,
                })
                this.$store.commit('setRoomDetail', resData)
                this.showErrorTips = false
            } catch (error) {
                this.showErrorTips = true
            }
            if (!this.showErrorTips) {
                await this.getResource(this.roomDetail.anchorId, this.roomDetail.timestamp)
            }

            let payStatus = 0
            if (
                this.pendantResource.advertType === 'vip' &&
                this.pendantResource.goodsKey
            ) {
                if (this.pendantResource.bought && !this.pendantResource.upgrade) {
                    // 已付费
                    payStatus = 1
                } else {
                    // 待付费
                    payStatus = 2
                }
            } else {
                // 免费
                payStatus = 0
            }

            // 埋点梳理-驾考宝典-0610
            // 精品课直播间页_展示
            trackPageShow({
                actionType: '展示',
                payStatus: payStatus,
                groupKey: this.pendantResource.goodsKey,
                goodsUniqueKey: this.pendantResource.goodsKey,
            })

            if (this.roomDetail.promotionActivityId) {
                let resData = await getPromotionActivityRule({
                    activityId: this.roomDetail.promotionActivityId,
                })

                this.$store.commit('updateRoomDetail', {
                    activityRule: resData.ruleContent || '',
                })
                this.$store.commit('updateBizConfig', {
                    hasActivityRule: !!resData.ruleContent,
                })
            }
        },
        async dealResourceWithTraining(resource, timestamp) {
            let cardDetail = this.dealResource(resource?.cardResource, timestamp)
            let bannerDetail = this.dealResource(resource?.bannerResource, timestamp)
            let pendantDetail = this.dealResource(resource?.pendantResource, timestamp)
            let warmupDetail = this.dealResource(resource?.warmUpResource, timestamp)
            if ([cardDetail, bannerDetail, pendantDetail, warmupDetail].some(res => res.advertType === 'training')) {
                const authToken = await getAuthToken()
                let goodsKey = ''
                if (authToken) {
                    const {value} = await getTrainingRecommendChannelCode({
                        carType: URLParams.carStyle || 'car',
                        kemu: URLParams.kemuNum || '1',
                        sceneCode: URLParams.sceneCode || '101',
                    })
                    goodsKey = value
                }
                [cardDetail, bannerDetail, pendantDetail, warmupDetail].forEach(res => {
                    if (res.advertType === 'training') {
                        res.goodsKey = goodsKey
                    }
                })
            }
            return {
                cardDetail, bannerDetail, pendantDetail, warmupDetail,
            }
        },
        async getResource(anchorId, timestamp) {
            const roomResource = await getResource({
                anchorId,
                carType: URLParams.carStyle,
                kemu: +URLParams.kemuNum,
                sessionId: URLParams.id,
            })
            const {cardDetail, bannerDetail, pendantDetail, warmupDetail} = await this.dealResourceWithTraining(roomResource, timestamp)

            this.$store.commit('setRoomResource', {
                cardDetail,
                bannerDetail,
                pendantDetail,
                warmupDetail,
            })
            this.$store.commit('setPendantResource', {})
            this.$nextTick(() => {
                this.$store.commit('setPendantResource', pendantDetail)
            })
        },
        dealResource(resource, timestamp) {
            if (!resource || !resource.length) return {}
            resource = resource.filter(item => {
                return item.durationList.some(item => item.beginTime < timestamp && item.endTime > timestamp)
            })
            let r = resource[0] || {}
            if (r.resourceId) {
                r.advertType = RESOURCE_TYPE[r.type]
            }
            return r
        },
        setOrientation(orientation, callback) {
            if (!this.isShowFullScreen) {
                callback && callback()
                return
            }
            let isLandscape = this.isLandscape
            let currentOrientation = isLandscape ? 'landscape' : 'portrait'
            if (orientation === currentOrientation) {
                callback && callback()
                return
            }
            this.$EventBus.$emit('pause')
            let setOrientation = () => {
                MCProtocol.lesson.setOrientation({
                    orientation: orientation,
                    callback: data => {
                        if (orientation === 'landscape') {
                            removeClass(document.body, 'portrait')
                            addClass(document.body, 'landscape')
                        } else {
                            removeClass(document.body, 'landscape')
                            addClass(document.body, 'portrait')
                        }
                        console.log('setOrientation', data)
                        this.$EventBus.$emit('play')
                        // 小米平版，横竖屏切换，有些场景下 不会切换角度，手动切换横竖屏状态
                        this.setLandscape(!isLandscape)
                        if (isAndroid) {
                            setTimeout(() => {
                                callback && callback()
                                this.$EventBus.$emit('orientationChange')
                            }, 200)
                        } else {
                            callback && callback()
                            this.$EventBus.$emit('orientationChange')
                        }
                    },
                })
            }

            // TODO 检测软键盘是否展开
            // 安卓客户端，在横屏状态，并且软键盘展开，点击会触发竖屏的按钮，会切换不成功，
            // 因此在此场景下，延迟200ms，待软键盘关闭后，再执行横屏切换方法
            if (isAndroid && orientation === 'portrait') {
                setTimeout(() => {
                    setOrientation()
                }, 200)
            } else {
                setOrientation()
            }
        },
        // toggleDanmu() {
        //     let isOpen = this.isShowDanmu
        //     this.setShowDanmu(!isOpen)

        //     if (isOpen) {
        //         let actionName = '关闭弹幕'
        //         let actionType = '点击'

        //         // 埋点梳理-驾考宝典-V8.8.0
        //         // 精品课直播间页_点击关闭弹幕
        //         trackEvent({actionName, actionType})
        //     } else {
        //         let actionName = '开启弹幕'
        //         let actionType = '点击'

        //         // 埋点梳理-驾考宝典-V8.8.0
        //         // 精品课直播间页_点击开启弹幕
        //         trackEvent({actionName, actionType})
        //     }
        // },
        toggleRrientation() {
            let orientation = this.isLandscape ? 'portrait' : 'landscape'
            this.setOrientation(orientation)

            if (orientation === 'portrait') {
                let fragmentName1 = '退出全屏播放'
                let actionType = '点击'

                // 埋点梳理-驾考宝典-1012
                // 精品课直播间页_退出全屏播放_点击
                trackEvent({fragmentName1, actionType})
            } else {
                let fragmentName1 = '全屏播放'
                let actionType = '点击'

                // 埋点梳理-驾考宝典-1012
                // 精品课直播间页_全屏播放_点击
                trackEvent({fragmentName1, actionType})
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
    background: #000;
}
body:not(.tablet) {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
}

.page-container {
    height: 100%;
    display: flex;
    // background-image: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2023/02/15/11/389ab047ac1a402582e7d7e2c69a4420.png);
    background-image: linear-gradient(90deg, #251d5a 0%, #5f1d22 100%);
    background-repeat: repeat-y;
    background-size: 100% auto;
    background-color: #1b1d22;
    .h-back {
        position: fixed;
        left: 10px;
        top: 0;
        width: 100px;
        height: 80px;
        background: url(../../assets/images/<EMAIL>) no-repeat center;
        background-size: 40px 30px;
        z-index: 2;
    }
}

@media (min-width: 689px) {
    .landscape .interact-wrap {
        flex: 1;
        overflow: hidden;
    }
}

/* 540+150=690 */
@media (max-width: 690px) {
    .landscape .interact-wrap {
        width: 4.5rem;
    }
}
.showcase {
    position: absolute;
    bottom: 138px;
    left: 18px;
    z-index: 1;
    width: 576px;
}
.interact-wrap {
    display: flex;
    flex-direction: column;
    position: relative;
    .pass-rate {
        position: absolute;
        right: 0;
    }
}
.vertical {
    .interact-wrap {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 520px !important;
    }
}
.portrait {
    // portrait
    body {
        background: url(../../assets/images/page-bg.png) repeat-y;
        background-size: 100% auto;
    }
    .page-container {
        flex-direction: column;
    }
    .interact-wrap {
        height: 0;
        flex: 1;
    }
}
.landscape {
    // landscape
    .interact-wrap {
        background: url(../../assets/images/page-bg.png) repeat-y;
        background-size: 100% auto;
    }
}
</style>
