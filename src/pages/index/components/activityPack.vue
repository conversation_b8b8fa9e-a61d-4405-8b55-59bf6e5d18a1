<template>
    <div class="activity-pack">
        <couponMotion v-if="showCouponMotion && false" />
        <redPacketPopup v-if="motionCanUse1 && false" />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {URLParams, isAndroid} from '../../../utils/tools'
import couponMotion from '../../../components/couponMotion.vue'
import redPacketPopup from '../../../components/redPacketPopup.vue'

export default {
    components: {
        couponMotion,
        redPacketPopup,
    },
    data() {
        return {
            kemu: URLParams.kemu,
            carStyle: URLParams.carStyle,
            activityType: 0,
        }
    },
    computed: {
        ...mapState([
            'bizConfig',
            'pendantResource',
            'pushCoupon',
            'isLandscape',
        ]),
        showCouponMotion() {
            return (
                isAndroid &&
                this.pendantResource.advertType === 'vip' &&
                this.bizConfig.motionVisible &&
                this.pendantResource.bought === false &&
                URLParams.carStyle === 'car' &&
                URLParams.kemu === 'kemu1'
            )
        },
        motionCanUse1() {
            // 未购买 && 没有优惠券
            return (
                this.pendantResource.advertType === 'vip' &&
                this.bizConfig.motionVisible &&
                this.pendantResource.bought === false &&
                URLParams.kemu === 'kemu1' &&
                // TODO 时间差
                (!this.showCouponMotion || !this.pushCoupon.couponPrice)
            )
        },
    },
}
</script>

<style lang="less" scoped>
.activity-pack {
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-end;
}
.landscape {
    .activity-pack {
        flex-direction: row-reverse;
        align-items: flex-end;
    }
}
.portrait,
.vertical {
    .activity-pack {
        flex-direction: row;
        align-items: flex-start;
    }
}
</style>
