<template>
    <div>
        <confirm
            :show.sync="nextVipLiveConfirmVisible"
            :message="'是否为您跳转至该直播间？'"
            :title="nextVipLiveTitle"
            :showCancelButton="true"
            :showConfirmButton="true"
            @cancel="stopCheckNextLive"
            @confirm="goNextVipLive"
        >
            <div class="confirm-image"><img src="../../../assets/images/confirm-bg.png" /></div>
        </confirm>
        <!-- <template v-if="bizConfig.saleMode">
            <checkNotOrderPopup v-if="kemu === 'kemu1' && carStyle === 'car' && false" />
        </template> -->
        <storeReview />
        <activityWinPopup
            :shipList="shipList"
            :show.sync="activityWinPopupVisible"
        />
        <vipGuidePopup :show.sync="vipGuidePopupVisible" />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {URLParams, webOpen, getAuthToken} from '../../../utils/tools'
import {getNextVipLive, getLatestLive} from '../../../server/topLesson'
import {getNoShipPresent, getGuidedConditionMatch} from '../../../server/active'
import confirm from '../../../components/confirm.vue'
import storeReview from '../../../components/storeReview.vue'
import activityWinPopup from '../../../components/activityWinPopup.vue'
import vipGuidePopup from '../../../components/vipGuidePopup'
// import checkNotOrderPopup from './checkNotOrderPopup.vue'

let localDataKey2 = 'jiaokaobaodian-zhibojian-activity-win-popup'

let checkNextLiveTimer = null

export default {
    components: {
        confirm,
        // checkNotOrderPopup,
        storeReview,
        activityWinPopup,
        vipGuidePopup,
    },
    data() {
        return {
            kemu: URLParams.kemu,
            carStyle: URLParams.carStyle,
            nextVipLiveConfirmVisible: false,
            activityWinPopupVisible: false,
            nextVipLiveTitle: '',
            nextVipLiveId: 0,
            shipList: [],
            vipGuidePopupVisible: false,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig']),
    },
    created() {
        if (this.bizConfig.stMode && this.bizConfig.playStatus !== 1) {
            checkNextLiveTimer = setInterval(() => {
                this.checkNextVipLive()
            }, 15000)
            this.checkNextVipLive()
        }
        this.getNoShipPresent()
        this.getGuidedConditionMatch()
        this.$EventBus.$on(['onLogin', 'winPrize', 'winingNotify'], async () => {
            this.getNoShipPresent()
            this.getGuidedConditionMatch()
        })
    },
    methods: {
        async checkNextVipLive() {
            if (this.roomDetail.free) {
                let resData = await getLatestLive(
                    {
                        carType: URLParams.carStyle,
                        liveType: 'ST',
                        kemu: URLParams.kemuNum,
                    },
                    {noConsole: true}
                )
                if (resData.lessonItemId && resData.lessonItemId !== +URLParams.id && resData.status === 1) {
                    this.nextVipLiveTitle = `最新直播已经开始`
                    this.nextVipLiveId = resData.lessonItemId
                    this.nextVipLiveConfirmVisible = true
                }
            } else {
                let resData = await getNextVipLive(
                    {
                        currentLiveSessionId: URLParams.id,
                    },
                    {noConsole: true}
                )
                if (resData.liveSessionId) {
                    this.nextVipLiveTitle = `第${resData.index}场直播已经开始`
                    this.nextVipLiveId = resData.liveSessionId
                    this.nextVipLiveConfirmVisible = true
                }
            }
        },
        async getNoShipPresent() {
            const authToken = await getAuthToken()
            if (!authToken) return
            let res = await getNoShipPresent({
                sessionId: URLParams.id,
            })
            res = res.itemList || res
            if (res.length > 0) {
                let actObj = localStorage.getItem(localDataKey2)
                actObj = JSON.parse(actObj || '{}')
                actObj[URLParams.id] = actObj[URLParams.id] || []
                let list = actObj[URLParams.id]
                if (list.length) {
                    res = res.filter(item => {
                        return list.indexOf(item.presentShipId) === -1
                    })
                }
                if (res.length > 0) {
                    this.shipList = res
                    this.activityWinPopupVisible = true
                    list.push(...res.map(item => item.presentShipId))
                    localStorage.setItem(localDataKey2, JSON.stringify(actObj))
                }
            }
        },
        async getGuidedConditionMatch() {
            const authToken = await getAuthToken()
            if (!authToken) return
            let res = await getGuidedConditionMatch({
                sessionId: URLParams.id,
                carType: URLParams.carStyle,
                kemu: URLParams.kemuNum,
            })
            if (res.conditionMatched) {
                this.vipGuidePopupVisible = true
            }
        },
        goNextVipLive() {
            webOpen({
                url: `http://jiakao.nav.mucang.cn/topLesson/live?id=${this.nextVipLiveId}`,
                closeCurrent: 1,
            })
        },
        stopCheckNextLive() {
            clearInterval(checkNextLiveTimer)
        },
    },
}
</script>

<style lang="less" scoped></style>
