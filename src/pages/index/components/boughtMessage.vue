<template>
    <div class="vip-enter-wrap">
        <transition appear name="slide-fade" mode="out-in">
            <div :key="currentBoughtMessage.id">
                <div v-if="currentBoughtMessage.id" class="vip-user-enter">
                    <div class="sol">
                        <div class="tit">
                            <div class="name">{{ currentBoughtMessage.name }}</div>
                            <div class="num">
                                + <span>{{ currentBoughtMessage.num }}</span>
                            </div>
                        </div>
                        <div class="text">{{ currentBoughtMessage.tip }}</div>
                    </div>
                    <div class="btn" @click="buyVip">立即抢购</div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import {trackEvent} from '../../../utils/tools'
let t = null
export default {
    data() {
        return {
            currentBoughtMessage: {
                num: 0,
            },
            isExpired: true,
            expiredTime: 5016,
        }
    },
    props: {
        boughtMessageList: Array,
    },
    watch: {
        boughtMessageList() {
            if (this.boughtMessageList.length) {
                let num = this.boughtMessageList.length
                this.changeboughtMessage(num)

                let fragmentName1 = '热卖提醒'
                let actionType = '出现'

                // v8.17.0-埋点文档
                // 精品课直播间页_热卖提醒_出现
                trackEvent({fragmentName1, actionType})
            }
        },
    },
    methods: {
        changeboughtMessage(num) {
            if (this.currentBoughtMessage.id) {
                this.currentBoughtMessage.num = this.currentBoughtMessage.num + num
            } else {
                this.currentBoughtMessage = {
                    id: +new Date(),
                    name: this.boughtMessageList[0].name,
                    tip: this.boughtMessageList[0].tip,
                    num: this.currentBoughtMessage.num + num,
                }
            }

            clearTimeout(t)
            t = setTimeout(() => {
                this.currentBoughtMessage = {
                    num: 0,
                }
            }, this.expiredTime)
            this.$emit('update:boughtMessageList', [])
        },
        buyVip() {
            this.$EventBus.$emit('openMotion', {
                report: true,
                fragmentName1: '热卖提醒',
            })
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.slide-fade-enter-active {
    transition: all 0.3s ease;
}
.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter {
    transform: translateX(40px);
    opacity: 0;
}
.slide-fade-leave-to {
    transform: translateX(-40px);
    opacity: 0;
}
.vip-enter-wrap {
    position: absolute;
    bottom: 0;
    left: 30px;
    z-index: 5;
}
.vip-user-enter {
    background: url(../images/<EMAIL>) no-repeat;
    width: 690px;
    height: 154px;
    background-size: 690px 154px;
    margin: 0 auto;
    display: flex;
    padding: 50px 20px 0 150px;
    .sol {
        flex: 1;
    }
    .icon {
        background: url(../images/+<EMAIL>) no-repeat;
        margin-top: 6px;
        width: 54px;
        height: 40px;
        background-size: 54px 40px;
    }
    .tit {
        display: flex;
        color: #fff;
    }
    .name {
        font-size: 30px;
        line-height: 1.5;
        height: 45px;
        overflow: hidden;
    }
    .num {
        font-size: 32px;
        line-height: 1;
        margin-left: 10px;
        white-space: nowrap;
        span {
            font-size: 52px;
            font-weight: bold;
        }
    }
    .text {
        background: url(../images/<EMAIL>) left center no-repeat;
        background-size: 24px 24px;
        padding-left: 30px;
        color: #fff;
        font-size: 24px;
        line-height: 1.5;
        height: 36px;
        overflow: hidden;
        // margin-top: 6px;
    }
    .btn {
        background: url(../images/<EMAIL>) no-repeat;
        width: 220px;
        height: 82px;
        background-size: 220px 82px;
        color: #ee1651;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-bottom: 6px;
        margin-top: 10px;
        font-weight: bold;
    }
}
.landscape /deep/ {
    .vip-enter-wrap {
        bottom: 0;
        right: 10px;
        left: unset;
    }
    // landscape
    .vip-user-enter {
        background: url(../images/<EMAIL>) no-repeat;
        width: 472px;
        height: 140px;
        background-size: 472px 140px;
        padding: 42px 8px 0 114px;
        .name {
            font-size: 26px;
        }
        .num {
            font-size: 28px;
            span {
                font-size: 48px;
            }
        }
        .text {
            background-size: 20px 20px;
            padding-left: 24px;
            font-size: 22px;
            white-space: nowrap;
        }
        .btn {
            background: url(../images/<EMAIL>) no-repeat;
            width: 156px;
            height: 76px;
            background-size: 156px 76px;
            margin-top: 12px;
            font-size: 24px;
        }
    }
}
</style>
