<template>
    <div class="chat">
        <div v-if="!roomDetail.timestamp && showErrorTips" class="error-tips">
            <div class="content">
                加载出了点小问题<br>
                请重新进入直播间
                <div @click="refresh" class="btn">刷新</div>
            </div>
        </div>
        <template v-else>
            <div class="room-wrap">
                <div class="chat-area">
                    <div class="inner">
                        <div class="vertical-inner">
                            <room :items="items" :userEnterMessageList="userEnterMessageList" />
                        </div>
                    </div>
                </div>

                <div class="motion-wrap">
                    <slot name="motion"> </slot>
                </div>
                <slot name="bottom"> </slot>
            </div>
            <div class="rolling-message">
                <vipEnterMessage :vipUserEnterMessageList="vipUserEnterMessageList" />
                <showGradeMessage :showGradeMessageList="showGradeMessageList" />
            </div>
            <div class="comment-wrap" :class="{record: recordPlay, inputing: inputIng}">
                <!-- 小班教学 没有点击事件 -->
                <div v-if="isXBJX">
                    <div class="comment">
                        <div class="input">
                            <div class="input2">
                                {{ inputVal || textPlaceholder }}
                            </div>
                            <button></button>
                        </div>
                    </div>
                </div>
                <template v-else>
                    <div class="quick-comment" v-show="quickCommentVisible">
                        <div
                            @click="sendQuickCommon(item)"
                            v-for="item in quickCommentList"
                            :key="item"
                        >
                            {{ item }}
                        </div>
                    </div>
                    <div class="comment">
                        <form
                            action="javascript:void(0)"
                            class="input"
                            @submit.prevent="beforeSend"
                        >
                            <div
                                class="input2"
                                v-if="useFakeInput || ios12AndDisabled"
                                @click="iphoneFocus"
                            >
                                {{ inputVal || textPlaceholder }}
                            </div>
                            <input
                                ref="input"
                                v-else
                                type="text"
                                class="input-hook"
                                :readonly="inputDisabled"
                                @click="inputOn"
                                @blur="blur"
                                @focus="focus"
                                v-model="inputVal"
                                :placeholder="textPlaceholder"
                            />
                            <button @click="beforeSend"></button>
                            <wakeupBtn v-if="!isMucang && anchorIdentity === 'thirdAnchor'" :targetUrl="targetUrl" @trigger="appInLook" />
                        </form>
                        <slot name="icons"> </slot>
                        <!-- <button @click="showGrade">成绩</button> -->
                    </div>
                </template>
            </div>
            <div class="comment-wrap iphone" style="bottom: 10000px" ref="coment-wrap-iphone">
                <div class="quick-comment" v-show="quickCommentVisible">
                    <div
                        @click="sendQuickCommon(item)"
                        v-for="item in quickCommentList"
                        :key="item"
                    >
                        {{ item }}
                    </div>
                </div>
                <div class="comment">
                    <div class="input">
                        <input
                            type="text"
                            ref="iphone-input"
                            @blur="iphoneBlur"
                            v-model="inputVal"
                            :placeholder="textPlaceholder"
                        />
                        <button @click="beforeSend"></button>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {find, uniqWith, orderBy, debounce} from 'lodash-es'
import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    URLParams,
    isAndroid,
    IOSVersion,
    isMucang,
    trackEvent,
    toast,
    goLogin,
    getMucangId,
} from '../../../utils/tools'
import {liveRoomURL, ROOM_TYPE} from '../../../utils/constant'
import createMqtt from '../../../utils/mqtt_room'
import {
    getRoomInfo,
    sendIntoSendSysMsg,
    getQuicklyBarrages,
    sendInteractionIfNeeded,
} from '../../../server/chat'
import room from './room'
import vipEnterMessage from './vipEnterMessage.vue'
import showGradeMessage from './showGradeMessage.vue'
import wakeupBtn from '../../../components/wakeupBtn'

let el = document.documentElement
let originHeight = el.clientHeight
let mqttInstance

export default {
    inject: ['anchorIdentity'],
    components: {
        room,
        vipEnterMessage,
        showGradeMessage,
        wakeupBtn,
    },
    data() {
        return {
            isMucang,
            kemu: URLParams.kemu,
            carStyle: URLParams.carStyle,
            sendLock: false,
            inputVal: '',
            quickCommentList: [],
            quickCommentInitVisible: false,
            inputIng: false,
            useFakeInput: IOSVersion >= 13,

            mqtt: null,
            isLogin: false,
            items: [],
            sendLimitSeconds: 0,
            showGradeMessageList: [],
            vipUserEnterMessageList: [],
            userEnterMessageList: [],
            loadHistoryMessage: false,
            targetUrl: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
        }
    },
    props: {
        showErrorTips: Boolean,
        recordPlay: Boolean,
    },
    watch: {
        quickCommentVisible: {
            handler(val) {
                this.$store.commit('updateBizConfig', {
                    quickCommentVisible: val,
                })
            },
            immediate: true,
        },
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'roomStatus']),
        quickCommentVisible() {
            return (
                this.quickCommentList.length &&
                (this.quickCommentInitVisible ||
                    this.inputIng ||
                    this.isLiveVideo)
            )
        },
        isLiveVideo() {
            // 预录
            return this.roomDetail.liveMode === 1
        },
        inputDisabled() {
            if (this.anchorIdentity === 'thirdAnchor') return true
            return !(!this.isLiveVideo && this.bizConfig.playStatus === 1 && this.roomStatus.danmuStatus !== 1)
        },
        textPlaceholder() {
            if (this.anchorIdentity === 'thirdAnchor') {
                return '打开驾考宝典参与互动'
            }
            if (!this.isLiveVideo && this.bizConfig.playStatus === 1 && this.roomStatus.danmuStatus !== 1) {
                return '说点什么...'
            } else {
                return '当前仅支持快捷弹幕'
            }
        },
        ios12AndDisabled() {
            return this.inputDisabled && IOSVersion < 13
        },
        isXBJX() {
            return this.roomDetail.roomType === ROOM_TYPE.XBJX
        },
    },
    beforeDestroy() {
        this.disconnect()
    },
    created() {
        if (isAndroid) {
            // 解决安卓键盘收起表单无法失去焦点问题
            window.addEventListener('resize', () => {
                const resizeHeight = el.clientHeight
                if (!(resizeHeight < originHeight)) {
                    const likeArray = document.getElementsByClassName('input-hook')
                    Array.from(likeArray, input => input.blur())
                }
            })
            this.$EventBus.$on('orientationChange', () => {
                originHeight = el.clientHeight
            })
        }
        this.$EventBus.$on('disconnect', () => {
            this.disconnect()
        })
        pageSwitch.onPageShow(debounce(() => {
            this.initChat()
            console.log('reconnect')
        }))
        pageSwitch.onPageHide(() => {
            this.disconnect()
            console.log('disconnect')
        })
        this.initChat()
        this.getQuicklyBarrages()
        this.$EventBus.$on('sendComment', content => {
            this.beforeSend(content)
        })
        this.$EventBus.$on('showMsgInChat', msg => {
            this.insertMsg(msg)
        })
    },
    methods: {
        refresh() {
            window.location.reload()
        },
        iphoneFocus() {
            this.inputOn()
            if (this.inputDisabled) return
            this.$refs['iphone-input'].focus()
            setTimeout(() => {
                this.$refs['coment-wrap-iphone'].style.bottom =
                    window.innerHeight - window.visualViewport.height + 'px'
                this.inputIng = true
            }, 300)
        },
        iphoneBlur() {
            setTimeout(() => {
                this.$refs['coment-wrap-iphone'].style.bottom = '10000px'
                this.inputIng = false
            }, 100)
        },
        disconnect() {
            try {
                mqttInstance.disconnect()
            } catch (error) {}
        },
        reconnect() {
            this.initChat()
        },
        async sendIntoSendSysMsg(options) {
            await sendIntoSendSysMsg({
                danmuRoomId: this.roomDetail.roomId || this.roomDetail.danmuRoomId,
                clientId: options.clientId,
                kemu: URLParams.kemuNum,
                carType: URLParams.carStyle,
            })
        },
        sendInteractionIfNeeded(options) {
            sendInteractionIfNeeded({
                sessionId: URLParams.id,
                clientId: options.clientId,
            })
        },
        async initChat() {
            let mqttOptions = {
                cleansession: true,
                roomId: this.roomDetail.roomId || this.roomDetail.danmuRoomId,
                reconnectTimeout: 3000,
                debug: true,
                messageExtraInfo: {sessionId: URLParams.id},
            }
            const resData = await getRoomInfo({
                danmuRoomId: mqttOptions.roomId,
                anchor: false,
                carType: URLParams.carStyle,
            })
            this.isLogin = resData.isLogin
            this.sendLimitSeconds = resData.sendLimitSeconds
            this.disconnect()
            createMqtt(resData, mqttOptions, this.onMessage.bind(this), mqtt => {
                mqttInstance = mqtt
            })
            this.sendIntoSendSysMsg(resData)
            if (this.anchorIdentity === 'official') {
                // 延时1s执行
                setTimeout(() => {
                    this.sendInteractionIfNeeded(resData)
                }, 1000)
            }
        },
        parseIdentity(extraInfo) {
            extraInfo = uniqWith(extraInfo, (objValue, othValue) => {
                let v1 = objValue.carType + objValue.groupKey + objValue.kemu
                let v2 = othValue.carType + othValue.groupKey + othValue.kemu
                return v1 === v2
            })
            return extraInfo.filter(item => {
                return !!item.icon
            })
        },
        handleTags(tags) {
            tags = tags.filter(item => {
                // 过滤有carType字段，但是carType字段 != 当前carStyle的对象
                return !(item.carType && item.carType !== (URLParams.carStyle || 'car'))
            })

            // 去重，相同kemu徽章只显示一个
            tags = uniqWith(tags, (objValue, othValue) => {
                return objValue.kemu === othValue.kemu
            })

            // let vip = find(tags, {kemu: 0})
            // if (!!vip && !vip.expire) {
            //     tags = tags.filter(item => {
            //         // 过滤有kemu字段，但是kemu字段 >0 的对象
            //         return !(item.kemu && item.kemu > 0)
            //     })
            // }
            tags = orderBy(tags, ['expire', 'kemu'], ['asc', 'asc'])
            if (tags.length > 3) {
                tags.length = 3
            }
            return tags
        },
        getUserTags(item) {
            let tags = []
            if (item.user && item.user.extraInfos) {
                let extraInfo = []
                try {
                    if (item.user.extraInfos && item.user.extraInfos.isAdmin === 'true') {
                        tags = ['gf']
                        return tags
                    }
                    extraInfo = JSON.parse(item.user.extraInfos.userBadgeInfo)
                    extraInfo = extraInfo.list
                } catch (error) {
                    // console.log(error)
                }
                tags = this.parseIdentity(extraInfo)
                tags = this.handleTags(tags)
            }
            return tags
        },
        onMessage(message) {
            try {
                let enters = []
                let vipEnters = []
                let showGrades = []
                let loadHistoryMessage = false
                message.forEach(item => {
                    let tags = this.getUserTags(item)
                    if (item.type === 'message') {
                        if (!this.loadHistoryMessage || item.content.type !== 'history') {
                            if (item.content.type === 'history') {
                                loadHistoryMessage = true
                            }
                            try {
                                let replyMessage = item.content.replyMessage
                                let quote
                                if (replyMessage) {
                                    quote = {
                                        msg: replyMessage.content.text,
                                        name: replyMessage.user.nickname,
                                        tags: this.getUserTags(replyMessage),
                                    }
                                }
                                this.insertMsg({
                                    type: 'userMessage',
                                    msg: item.content.text,
                                    name: item.user.nickname,
                                    quote,
                                    tags,
                                })
                            } catch (error) {
                                console.log(error)
                            }
                        }
                    } else if (item.type === 'status') {
                        if (item.content.connect) {
                            let vip = find(tags, {kemu: 0})
                            if (!!vip && !vip.expire) {
                                vipEnters.push({
                                    id: +new Date(),
                                    name: item.user.nickname,
                                    tags: tags,
                                })
                            } else {
                                enters.push({
                                    id: +new Date(),
                                    name: item.user.nickname,
                                    tags: tags,
                                })
                            }
                        } else if (item.messageType === 'winPrize') {
                            getMucangId().then(mucangId => {
                                if (item.content.userId === mucangId) {
                                    this.$EventBus.$emit('winPrize')
                                }
                            })
                        } else if (item.messageType === 'command') {
                            if (item?.messageExtraInfo?.subType === 'winingNotify') {
                                this.$EventBus.$emit('winingNotify')
                            }
                        } else {
                            let type = item.content.type
                            if (type === 'sysMsg') {
                                this.insertMsg({
                                    type: 'systemMessage',
                                    msg: item.content.text || item.content.content,
                                })
                            } else if (type === 'shareExamScore') {
                                showGrades.push({
                                    id: +new Date(),
                                    name: item.user.nickname,
                                    tags: tags,
                                    kemu: item.content.kemu,
                                    score: item.content.score,
                                })
                            }
                        }
                        // else if (item.messageType === 'liveExam') {
                        //     let {isRealLive, webrtcStream} = this.bizConfig
                        //     if ((isRealLive && webrtcStream) || this.isLiveVideo) {
                        //         this.$nextTick(() => {
                        //             this.$EventBus.$emit('liveExam', item.content)
                        //         })
                        //     }
                        // }
                        // else if (item.messageType === 'stopCurrentQuestion') {
                        //     this.$EventBus.$emit('stopCurrentQuestion', item.content)
                        // }
                        // else if (item.messageType === 'stopLiveExam') {
                        //     this.$EventBus.$emit('stopLiveExam', item.content)
                        // }
                    }
                })
                if (vipEnters.length) {
                    this.vipUserEnterMessageList.push(...vipEnters)
                }
                if (enters.length) {
                    this.userEnterMessageList.push(...enters)
                }
                if (showGrades.length) {
                    this.showGradeMessageList.push(...showGrades)
                }
                if (loadHistoryMessage) {
                    this.loadHistoryMessage = loadHistoryMessage
                }
                this.$EventBus.$emit('mqttMessage', message)
            } catch (error) {
                this.$EventBus.$emit('mqttMessage', message)
                console.error(error)
            }
        },
        insertMsg(msgObj) {
            this.items.push(Object.assign({id: Math.random()}, msgObj))

            if (msgObj.type === 'userMessage') {
                this.$EventBus.$emit('sendBullet', {
                    content: msgObj.msg,
                    direction: 'default',
                    isSelf: false,
                })
            }
        },
        showGrade() {
            this.onMessage([
                {
                    type: 'status',
                    content: {
                        type: 'shareExamScore',
                        kemu: 1,
                        score: 98,
                    },
                    user: {
                        extraInfo:
                            '[{"carType":"car","expire":false,"expireTime":1657681904000,"groupKey":"channel_ke1","kemu":1}]',
                        nickname: '看看车',
                    },
                },
            ])
        },
        async getQuicklyBarrages() {
            const resDate = await getQuicklyBarrages({
                anchorId: this.roomDetail.anchorId,
                carStyle: URLParams.carStyle,
            })
            this.quickCommentList = resDate.itemList
                .sort((itemA, itemB) => {
                    return itemB.order - itemA.order
                })
                .map(item => item.content)
            if (this.bizConfig.playStatus === 1) {
                this.quickCommentInitVisible = true
                // 考前辅导 专项攻克 重点刷题班
                // 快捷弹幕 常驻
                if (!this.bizConfig.stMode || this.roomDetail.free) {
                    setTimeout(() => {
                        this.quickCommentInitVisible = false
                    }, 5000)
                }
            }
        },
        inputOn() {
            if (this.anchorIdentity === 'official') {
                this.quickCommentInitVisible = true
                setTimeout(() => {
                    this.quickCommentInitVisible = false
                }, 5000)
            }
        },
        appInLook() {
            let actionType = '点击'
            let actionName = '弹幕输入'

            // v8.24.0-埋点文档
            // 直播间页_点击弹幕输入
            trackEvent({actionType, actionName})
        },
        blur() {
            setTimeout(() => {
                // document.documentElement.scrollTop = 0
                // document.body.scrollTop = 0
                document.body.scrollIntoView()
                this.inputIng = false
            }, 100)
        },
        focus() {
            this.inputIng = true
            if (!isMucang && isAndroid) {
                setTimeout(() => {
                    let target = this.$refs['input']
                    target && target.scrollIntoView(true)
                }, 150)
            }
        },
        sendQuickCommon(comment) {
            this.beforeSend({type: 'quick', comment})

            let fragmentName1 = '快捷弹幕'
            let actionType = '点击'
            let actionName = '任意快捷弹幕'

            // 埋点梳理-驾考宝典-0713
            // 精品课直播间页_快捷弹幕_点击任意快捷弹幕
            trackEvent({fragmentName1, actionType, actionName})
        },
        async beforeSend(content) {
            if (!this.isLogin) {
                if (isAndroid) {
                    const likeArray = document.getElementsByClassName('input-hook')
                    Array.from(likeArray, input => input.blur())
                }
                this.$EventBus.$emit('setOrientation', 'portrait', async () => {
                    await goLogin()
                    if (!isMucang) {
                        this.disconnect()
                        setTimeout(() => {
                            this.reconnect()
                        }, 100)
                    }
                })
            } else {
                this.send(content)
            }
        },
        send(content = {}) {
            if (this.sendLock) {
                toast('发言过于频繁，请稍后再试！')
                return
            }
            let {type, comment} = content
            if (type !== 'quick') {
                if (!this.inputVal) return
                comment = this.inputVal
            }
            if (comment.length > 100) {
                toast('请发送不超过100个字符')
                return
            }

            mqttInstance.sendMessage(comment)
            this.sendLock = true
            if (type !== 'quick') {
                this.inputVal = ''
            }
            setTimeout(() => {
                this.sendLock = false
            }, (this.sendLimitSeconds || 1) * 1000)
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.chat {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0;
}
.slide-fade-enter-active {
    transition: all 0.3s ease;
}
.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter {
    transform: translateX(40px);
    opacity: 0;
}
.slide-fade-leave-to {
    transform: translateX(-40px);
    opacity: 0;
}

.error-tips {
    flex: 1;
    position: relative;
    .content {
        padding-top: 130px;
        width: 8rem;
        position: absolute;
        left: 50%;
        top: 40%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: rgba(255, 251, 251, 0.8);
        .fontSizeWithElder(26px);
        background: url(../../../assets/images/error-tips-icon.png) no-repeat top center;
        background-size: 128px auto;
    }
    .btn {
        margin: 26px auto 0;
        text-align: center;
        width: 300px;
        height: 72px;
        line-height: 72px;
        background: linear-gradient(135deg, #a74f93 0%, #865dc2 100%);
        border-radius: 40px;
        .fontSizeWithElder(32px);
        color: #ffffff;
    }
}
.motion-wrap {
    pointer-events: none;
    display: flex;
    z-index: 2;
}

.room-wrap {
    flex: 1;
    display: flex;
    position: relative;
    height: 0;
    .chat-area {
        display: flex;
        flex: 1;
        flex-direction: column;
    }
    .inner {
        position: relative;
        height: 100%;
        flex: 1;
    }
    .vertical-inner {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 100%;
    }
}
.comment-wrap {
    position: relative;
    // z-index: 3;
    &.inputing {
        // background-image: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2023/02/15/11/389ab047ac1a402582e7d7e2c69a4420.png);
        background-image: linear-gradient(90deg, #251d5a 0%, #5f1d22 100%);
        background-repeat: repeat-y;
        background-size: 100% auto;
        z-index: 1;
    }
    &.iphone {
        // background-image: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2023/02/15/11/389ab047ac1a402582e7d7e2c69a4420.png);
        background-image: linear-gradient(90deg, #251d5a 0%, #5f1d22 100%);
        background-repeat: repeat-y;
        background-size: 100% auto;
        position: fixed;
        left: 0;
        width: 100%;
        z-index: 998;
    }
}
.quick-comment {
    padding: 20px;
    overflow-x: auto;
    white-space: nowrap;
    > div {
        display: inline-block;
        margin: 0 7px;
        color: #fff;
        .fontSizeWithElder(28px);
        padding: 0 40px;
        height: 56px;
        line-height: 56px;
        background: #553451;
        border-radius: 28px;
        border: 1px solid #7c5a77;
    }
}
.question-list {
    padding: 20px;
    display: flex;
    .options {
        flex: 1;
        overflow-x: auto;
        white-space: nowrap;
    }
    .item {
        width: 112px;
        height: 56px;
        display: inline-block;
        margin: 0 10px;
        color: #fff;
        font-size: 28px;
        line-height: 56px;
        background: rgba(0,0,0,0.25);
        border-radius: 28px;
        text-align: center;
    }
}
.vertical {
    .quick-comment {
        > div {
            color: #fff;
            background: rgba(0, 0, 0, 0.4);
            border-color: transparent;
        }
    }
    .motion-wrap {
        flex-direction: column-reverse;
    }
}

.comment {
    display: flex;
    padding: 10px 20px 50px;
    .input {
        display: flex;
        flex: 1;
        border: 0;
        border-radius: 40px;
        height: 76px;
        background-color: rgba(0, 0, 0, 0.3);
        padding: 0 10px 0 30px;
        color: #fff;
        align-items: center;
        div.input2 {
            width: 100px;
            flex: 1;
            color: rgba(255, 255, 255, 0.5);
            white-space: nowrap;
            overflow: hidden;
        }
        input {
            width: 100px;
            background: transparent;
            border: none;
            flex: 1;
        }
        input::-webkit-input-placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        button {
            background: transparent;
            border: none;
            background: url(../../../assets/images/<EMAIL>) no-repeat center;
            background-size: 32px 32px;
            width: 32px;
            height: 32px;
            padding: 15px;
            margin: 0;
            box-sizing: content-box;
        }
    }
    button {
        padding: 0 30px;
        border: 0;
        border-radius: 40px;
        height: 76px;
        background-color: rgba(0, 0, 0, 0.3);
        margin-left: 20px;
        color: #fff;
    }
}

.rolling-message {
    position: absolute;
    z-index: 2;
}
.portrait /deep/ {
    // portrait
    .chat {
        padding-bottom: calc(constant(safe-area-inset-bottom) - 50px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 50px);
    }

    .motion-wrap {
        flex-direction: column-reverse;
        align-items: flex-end;
        position: relative;
    }
}
.landscape /deep/ {
    // landscape
    .room-wrap {
        flex-direction: column;
        position: relative;
    }

    .rolling-message {
        position: fixed;
        z-index: 2;
        left: 56px;
        top: 0;
    }
    .motion-wrap {
        flex-direction: row-reverse;
        align-items: flex-end;
        padding-top: 6px;
        padding-bottom: 6px;
    }

    .comment {
        .input {
            height: 60px;
        }
        button {
            height: 60px;
        }
    }
    .quick-comment {
        z-index: 3;
    }
}
.vertical {
    .chat {
        padding-bottom: calc(constant(safe-area-inset-bottom) - 50px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 50px);
    }
    .comment-wrap {
        z-index: 1;
    }
    .comment-wrap.record {
        margin-top: 46px;
    }
    .room-wrap {
        z-index: 1;
    }
}
</style>
<style lang="less">
.motion-wrap {
    > * {
        pointer-events: auto;
    }
}
</style>
