<!-- 检测到未支付的订单 -->
<template>
    <popup
        class="not-pay-order-popup"
        :position="'center'"
        :bottomCloseIcon="true"
        :show.sync="show"
    >
        <div class="content">
            <div class="title"></div>
            <p>离开直播间将无法使用了哦!</p>
            <div class="coupon"></div>
            <div class="btns">
                <div class="leave" @click="webClose">暂不需要</div>
                <div class="confirm" @click="confirm">享受优惠</div>
            </div>
        </div>
    </popup>
</template>

<script>
import {mapState} from 'vuex'
import {findIndex, find} from 'lodash-es'
import {URLParams, toast, trackEvent} from '../../../utils/tools'
import popup from '../../../components/dialog'
import {getUserPromotion} from '../../../server/goods'

export default {
    components: {popup},
    data() {
        let seconds = 60
        return {
            monitor: null,
            show: false,
            stayTime: seconds * 1000,
            liveList: [],
        }
    },
    computed: {
        ...mapState(['bizConfig']),
    },
    mounted() {
        this.$EventBus.$once('weizhifu', () => {
            this.taskList.push({
                _uid: this._uid,
                priority: 110,
                callback: () => {
                    if (this.bizConfig.motionVisible) {
                        this.show = true

                        let fragmentName1 = '未支付订单挽留弹窗'
                        let actionType = '出现'

                        // v8.17.0-埋点文档
                        // 精品课直播间页_未支付订单挽留弹窗_出现
                        trackEvent({fragmentName1, actionType})
                    }
                },
            })
        })
    },
    methods: {
        clearTask() {
            let index = findIndex(this.taskList, {_uid: this._uid})
            if (index > -1) {
                this.taskList.splice(index, 1)
            }
        },
        close() {
            this.show = false
        },
        webClose() {
            this.$EventBus.$emit('webClose')
        },
        async confirm() {
            try {
                let resData = {itemList: []}
                try {
                    resData = await getUserPromotion({
                        tiku: URLParams.carStyle,
                        promotionType: 6,
                    })
                } catch (error) {}
                this.close()
                let data = find(resData.itemList, {groupKey: 'channel_kemuall_new'})
                if (!data) {
                    toast('该活动已下架，请联系工作人员', 4500)
                    return
                }
                // let promotionPriceInfo = data.promotionDetail.promotionPriceInfo
                // delete data.promotionDetail

                // let vipGoodsDetail = {
                //     ...data,
                //     ...promotionPriceInfo,
                // }
                // todo 未支付订单优惠购买
            } catch (error) {
                this.close()
            }

            let fragmentName1 = '未支付订单挽留弹窗'
            let actionType = '点击'
            let actionName = '去支付'

            // v8.17.0-埋点文档
            // 精品课直播间页_未支付订单挽留弹窗_点击去支付
            trackEvent({fragmentName1, actionType, actionName, payPathType: -1})
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.not-pay-order-popup {
    .content {
        border-radius: 40px;
        width: 600px;
        min-height: 692px;
        background: url(../../../assets/images/<EMAIL>) no-repeat;
        background-size: cover;
        padding: 60px 0;
    }
    .title {
        width: 514px;
        height: 52px;
        background: url(../../../assets/images/10yuantitle.png) no-repeat;
        background-size: cover;
        margin: 0 auto;
    }
    p {
        font-size: 28px;
        text-align: center;
        color: #6f2117;
        margin: 18px auto 0;
    }
    .coupon {
        width: 504px;
        height: 162px;
        background: url(../../../assets/images/10yuancode.png) no-repeat;
        background-size: cover;
        margin: 110px auto 0;
    }
    .btns {
        display: flex;
        justify-content: space-around;
        margin-top: 110px;
        padding: 0 35px;
        .leave {
            width: 240px;
            height: 88px;
            border: 1px solid #fc5977;
            border-radius: 46px;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
            color: #fa0431;
            line-height: 88px;
        }
        .confirm {
            width: 240px;
            height: 88px;
            background: linear-gradient(307deg, #fb2b35 11%, #fd5a3e);
            line-height: 88px;
            border-radius: 46px;
            font-size: 32px;
            text-align: center;
            color: #ffffff;
        }
    }
}
</style>
