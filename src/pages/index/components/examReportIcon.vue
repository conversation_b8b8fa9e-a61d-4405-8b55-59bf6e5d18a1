<template>
    <div>
        <div class="exam-report-icon" @touchstart="ontouchstart" @touchmove="ontouchmove" :style="pos" v-if="bizConfig.playStatus === 1 && isShowExamProgressIcon && !isLandscape">
            <swiper :options="swiperOptions" :key="swiperKey" ref="mySwiper">
                <swiper-slide>
                    <div class="cr" name="progress">已答<span class="c1">&nbsp;{{answeredCount}}&nbsp;</span> | 剩余<span class="c1">&nbsp;{{surplusQuestionCount}}&nbsp;</span></div>
                </swiper-slide>
                <swiper-slide>
                    <div class="cr" name="rank">您的本场排名<span class="c1">&nbsp;{{rank}}&nbsp;</span></div>
                </swiper-slide>
            </swiper>
        </div>
        <div class="exam-report-icon cr" @touchstart="ontouchstart" @touchmove="ontouchmove" :style="pos" v-else-if="isShowExamReporIcon && !isLandscape" @click="openReport">查看本场答题报告</div>
        <examAnswerSheetPopup :show.sync="examProgressVisible" :sessionId="sessionId" :activityId="activityId" />
        <examRankPopup :show.sync="examRankVisible" :sessionId="sessionId" :activityId="activityId" />
        <examReportPopup :show.sync="examReportVisible" :sessionId="sessionId" :activityId="activityId" />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import 'swiper/css/swiper.css'
import {URLParams, getAuthToken, trackEvent, convertPixelFromUI} from '../../../utils/tools'
import examAnswerSheetPopup from '../../../components/examAnswerSheetPopup'
import examReportPopup from '../../../components/examReportPopup'
import examRankPopup from '../../../components/examRankPopup'
import {getActivityId, getExamOverview, getExamSituation} from '../../../server/active'

let Swiper = (resolve, reject) => {
    import('vue-awesome-swiper').then(sss => {
        resolve(sss.Swiper)
    })
}
let SwiperSlide = (resolve, reject) => {
    import('vue-awesome-swiper').then(sss => {
        resolve(sss.SwiperSlide)
    })
}

export default {
    components: {examAnswerSheetPopup, examRankPopup, examReportPopup, Swiper, SwiperSlide},
    data() {
        let $vm = this
        let windowWidth = window.innerWidth
        let windowHeight = document.body.clientHeight
        let defaultX = 0
        let defaultY = windowHeight - parseFloat(convertPixelFromUI(756 + 50))

        let maxPlaceX = windowWidth - parseFloat(convertPixelFromUI(222))
        let maxPlaceY = windowHeight - parseFloat(convertPixelFromUI(50))
        // let x = parseFloat(localStorage.getItem('exam_icon_x')) || defaultX
        // let y = parseFloat(localStorage.getItem('exam_icon_y')) || defaultY

        // let placeX = Math.min(x, maxPlaceX)
        // let placeY = Math.min(y, maxPlaceY)

        let placeX = defaultX
        let placeY = defaultY

        return {
            isShowExamProgressIcon: false,
            isShowExamReporIcon: false,
            examProgressVisible: false,
            examRankVisible: false,
            examReportVisible: false,
            sessionId: URLParams.id,
            activityId: 0,
            progressTimer: null,
            swiperKey: +new Date(),
            swiperOptions: {
                direction: 'vertical',
                autoplay: true,
                loop: true,
                allowTouchMove: false,
                on: {
                    click: function(sw) {
                        setTimeout(() => {
                            $vm.openPopup(sw)
                        }, 150)
                    },
                },
            },

            rank: '',
            questionCount: 0,
            answeredCount: 0,
            questionNum: 0,

            placeX,
            placeY,
            minPlaceX: 0,
            maxPlaceX,
            minPlaceY: 0,
            maxPlaceY,
        }
    },
    computed: {
        ...mapState(['isLandscape', 'bizConfig']),
        surplusQuestionCount() {
            if (this.questionNum > 0) {
                let num = this.questionCount - this.questionNum
                return num >= 0 ? num : '--'
            } else {
                return '--'
            }
        },
        pos() {
            return {
                right: this.placeX + 'px',
                bottom: this.placeY + 'px',
            }
        },
    },
    watch: {
        isShowExamProgressIcon(val) {
            if (val) {
                this.progressTimer = setInterval(() => {
                    this.getExamProgress()
                }, 15000)
            } else {
                clearInterval(this.progressTimer)
            }
        },
    },
    created() {
        this.$EventBus.$on('liveExam', (content) => {
            this.activityId = content.activityId
            this.questionNum = content.questionNum
            this.isShowExamProgressIcon = true
        })
        this.$EventBus.$on('stopLiveExam', (content) => {
            this.isShowExamProgressIcon = false
        })
        this.$EventBus.$on('setExamReporIcon', ({visible, activityId}) => {
            this.activityId = activityId
            this.isShowExamReporIcon = visible
        })
        this.$EventBus.$on('submitQuestion', () => {
            this.getExamProgress()
        })
    },
    mounted() {
        this.getExamStatus()
    },
    methods: {
        ontouchstart(e) {
            this.lastX = e.changedTouches[0].pageX
            this.lastY = e.changedTouches[0].pageY
        },
        ontouchmove(e) {
            e.preventDefault()
            e.stopPropagation()
            let currentX = e.changedTouches[0].pageX
            let currentY = e.changedTouches[0].pageY
            let moveX = currentX - this.lastX
            let moveY = currentY - this.lastY
            this.move(moveX, moveY)
            this.lastX = currentX
            this.lastY = currentY
        },
        move(moveX, moveY) {
            // this.placeX = Math.min(Math.max(this.minPlaceX, this.placeX - moveX), this.maxPlaceX)
            this.placeY = Math.min(Math.max(this.minPlaceY, this.placeY - moveY), this.maxPlaceY)
            // localStorage.setItem('exam_icon_x', this.placeX)
            // localStorage.setItem('exam_icon_y', this.placeY)
        },
        openPopup(sw) {
            let name = sw.target.getAttribute('name')
            if (name === 'progress') {
                this.openProgress()
            } else if (name === 'rank') {
                this.openRank()
            }
        },
        openProgress() {
            this.examProgressVisible = true
            // 精品课直播间页_点击答题进度
            trackEvent({
                actionType: '点击',
                actionName: '答题进度',
            })
        },
        openRank() {
            this.examRankVisible = true
            // 精品课直播间页_点击实时排名
            trackEvent({
                actionType: '点击',
                actionName: '实时排名',
            })
        },
        openReport() {
            this.examReportVisible = true
            // 精品课直播间页_点击查看答题报告
            trackEvent({
                actionType: '点击',
                actionName: '查看答题报告',
            })
        },
        async getExamStatus() {
            const authToken = await getAuthToken()
            if (!authToken) return
            let activityId = await getActivityId({sessionId: URLParams.id})
            activityId = activityId.value
            console.log('activityId', activityId)
            if (activityId) {
                this.activityId = activityId
                let res1 = await this.getExamProgress()
                if (res1.examStatus === 1) {
                    return
                }

                let res2 = await getExamOverview({sessionId: URLParams.id, activityId})
                let overviewData = {
                    rightCount: res2.rightQuestionIds?.length || 0,
                    wrongCount: res2.wrongQuestionIds?.length || 0,
                }
                if (overviewData.wrongCount || overviewData.rightCount) {
                    this.isShowExamReporIcon = true
                }
            }
        },
        async getExamProgress() {
            let res = await getExamSituation({sessionId: URLParams.id, activityId: this.activityId})
            this.rank = res.rank
            this.questionCount = res.questionCount
            this.answeredCount = res.answeredCount
            this.isShowExamProgressIcon = res.examStatus === 1
            return res
        },
    },
}
</script>

<style lang="less" scoped>
.exam-report-icon {
    position: fixed;
    width: 222px;
    background: rgba(110,110,110,0.5);
    border-radius: 100px 0px 0px 100px;
    color: #fff;
    font-size: 24px;
    z-index: 3;
    .swiper-container {
        height: 50px;
    }
    &.cr,
    .cr {
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        .c1 {
            color: #51FFF6;
        }
        .c2 {
            color: #FFE972;
        }
    }
}
</style>
