<template>
    <div class="header-container" v-show="showHeader" id="J-header">
        <div class="m-header">
            <div class="back" @click="back"></div>
            <div class="title">{{ title }}</div>
            <div class="more" v-if="publicLive" @click="share">分享</div>
            <div class="more" v-else @click="more">更多课程</div>
        </div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {MCProtocol} from '@simplex/simple-base'
import {
    trackEvent,
    URLParams,
    webOpen,
    getUrl,
    isMajorApp,
} from '../../../utils/tools'
import {ROOM_TYPE} from '../../../utils/constant'
let pageContainer = null
export default {
    data() {
        return {
            statusBarHeight: '',
            showHeader: false,
            // 公共直播
            publicLive: false,
        }
    },
    props: {
        title: String,
    },
    computed: {
        ...mapState(['roomDetail', 'isLandscape']),
    },
    watch: {
        isLandscape: {
            handler(val) {
                if (val) {
                    this.showHeader = false
                    if (pageContainer) {
                        pageContainer.style.paddingTop = ''
                    }
                } else {
                    this.showHeader = true
                    if (pageContainer) {
                        pageContainer.style.paddingTop = this.statusBarHeight
                    }
                    // MCProtocol.Core.Web.setting({
                    //     fullScreen: true,
                    // })
                }
            },
            immediate: true,
        },
        'roomDetail.roomType': {
            handler(val) {
                if (val === ROOM_TYPE.GGZB && !this.publicLive) {
                    setTimeout(() => {
                        this.initShare()
                        this.publicLive = true
                    }, 1000)
                }
            },
            immediate: true,
        },
    },
    created() {
        MCProtocol.Core.Web.setStatusBarTheme({theme: 'light'})
        MCProtocol.Core.System.env(data => {
            let {statusBarHeight} = data.data
            if (statusBarHeight) {
                statusBarHeight = statusBarHeight + 'px'
            } else {
                statusBarHeight = 'env(safe-area-inset-top)'
            }
            this.statusBarHeight = statusBarHeight
            pageContainer.style.paddingTop = statusBarHeight
        })
    },
    mounted() {
        pageContainer = document.getElementById('J-page-container')
    },
    methods: {
        initShare() {
            MCProtocol.Core.Share.setting({
                channel: 'weixin_moment,weixin_friend,sina,qq',
                type: '',
                shareData: {
                    title: this.roomDetail.title,
                    description: '主播：' + this.roomDetail.teacherName,
                    url: getUrl(
                        'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-zhibojian/pure-live.html',
                        {
                            id: URLParams.id,
                            carStyle: URLParams.carStyle,
                            kemuStyle: URLParams.kemuNum,
                        }
                    ),
                    iconUrl: this.roomDetail.teacherHeadImg,
                },
            })
        },
        share() {
            MCProtocol.Core.Web.menu()
        },
        back() {
            this.$EventBus.$emit('beforeClose')
        },
        more() {
            if (URLParams.kemu === 'kemu3') {
                if (isMajorApp) {
                    webOpen({
                        url: `http://jiakao.nav.mucang.cn/famousTeacher?from=${URLParams.from}`,
                    })
                } else {
                    webOpen({
                        url: `https://laofuzi.kakamobi.com/jiakaobaodian-zhiboke/index.html?kemu=${URLParams.kemuNum}`,
                        // orientation不能没值，也不能传正确的值
                        orientation: '1',
                        titleBar: false,
                    })
                }
            } else {
                webOpen({
                    url: getUrl(
                        'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-must-learning/index.html',
                        {
                            from: URLParams.from,
                            fromItemCode: URLParams.id,
                            carStyle: URLParams.carStyle,
                            kemuStyle: URLParams.kemuNum,
                            sceneCode: URLParams.sceneCode,
                            patternCode: URLParams.patternCode,
                            orientation: 'portrait',
                        }
                    ),
                    // orientation不能没值，也不能传正确的值
                    // orientation: '1',
                    titleBar: true,
                    toolbar: true,
                    menu: false,
                    button: false,
                })
            }

            let actionName = '更多课程'
            let actionType = '点击'
            // 埋点梳理-驾考宝典-1112
            // 精品课直播间页_点击更多课程
            trackEvent({actionName, actionType})
        },
    },
}
</script>
<style>
.m-header {
    display: flex;
    align-items: center;
    color: #fff;
    height: 0.88rem;
    padding: 0.1rem;
}
.m-header .back {
    background: url(../../../assets/images/<EMAIL>) no-repeat center;
    background-size: 0.4rem 0.3rem;
    width: 0.6rem;
    height: 0.5rem;
}

.m-header .title {
    padding: 0 0.35rem 0 0.55rem;
    flex: 1;
    text-align: center;
    font-size: 0.3rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.elder .m-header .title {
    font-size: 0.36rem;
}

.m-header .more {
    font-size: 0.26rem;
}
.elder .m-header .more {
    font-size: 0.312rem;
}
</style>
