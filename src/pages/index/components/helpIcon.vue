<template>
    <div class="help-icon" v-show="isShowHelpIcon" @click="openHelp">
        <helpPop :show.sync="helpPopupVisible" :fragmentName1="helpFragmentName1" @setHelpIcon="(val) => isShowHelpIcon = val" />
    </div>
</template>

<script>
import {trackEvent} from '../../../utils/tools'
import helpPop from '../../../components/helpPop.vue'

export default {
    components: {helpPop},
    data() {
        return {
            helpPopupVisible: false,
            isShowHelpIcon: false,
            helpFragmentName1: '',
        }
    },
    methods: {
        openHelp() {
            this.helpFragmentName1 = ''
            this.helpPopupVisible = true

            let actionName = '在线客服'
            let actionType = '点击'

            // 埋点梳理-驾考宝典-1210
            // 精品课直播间页_点击在线客服

            trackEvent({actionName, actionType, eventId: 'customer_service'})
        },
    },
}
</script>

<style lang="less" scoped>
.help-icon {
    width: 76px;
    height: 76px;
    margin-left: 20px;
    background: url(../../../assets/images/<EMAIL>) no-repeat center;
    background-size: cover;
}
</style>
