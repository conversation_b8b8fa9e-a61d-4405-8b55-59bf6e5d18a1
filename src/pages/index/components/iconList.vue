<template>
    <span :class="{small: small}">
        <template v-for="(tag, index) in tags">
            <img class="tag gf" v-if="tag == 'gf'" src="../images/<EMAIL>" :key="tag" />
            <template v-if="tag">
                <img class="tag" v-if="tag.icon && !tag.expire" :src="tag.icon" :key="index" />
                <img
                    class="tag"
                    v-else-if="tag.iconExpire && tag.expire"
                    :src="tag.icon"
                    :key="index"
                />
            </template>
        </template>
    </span>
</template>

<script>
export default {
    props: {
        tags: Array,
        small: Boolean,
    },
}
</script>

<style lang="less" scoped>
.tag {
    margin-right: 8px;
}
.tag:not(.gf) {
    width: auto;
    height: 28px;
}
.gf {
    width: 72px;
}
.small {
    .tag {
        margin-right: 10px;
    }
    .tag:not(.gf) {
        height: 24px;
    }
    .gf {
        width: 60px;
    }
}
</style>
