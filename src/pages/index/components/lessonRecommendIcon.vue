<template>
    <div
        class="lesson-recommend-icon"
        v-show="isShowLessonRecommendIcon"
        @click="openLessonRecommend"
    >
        <lessonRecommendPop :show.sync="lessonRecommedVisible" />
    </div>
</template>

<script>
import {trackEvent} from '../../../utils/tools'
import lessonRecommendPop from './lessonRecommendPop.vue'

export default {
    components: {lessonRecommendPop},
    data() {
        return {
            lessonRecommedVisible: false,
            isShowLessonRecommendIcon: false,
        }
    },
    created() {
        this.$EventBus.$on('setLessonRecommedIcon', val => {
            this.isShowLessonRecommendIcon = val
        })
    },
    mounted() {},
    methods: {
        openLessonRecommend() {
            this.lessonRecommedVisible = true

            let fragmentName1 = '好课推荐弹窗'
            let actionType = '出现'

            // v8.27.0-埋点文档
            // 精品课直播间页_好课推荐弹窗_出现
            trackEvent({fragmentName1, actionType})
        },
    },
}
</script>

<style lang="less" scoped>
.lesson-recommend-icon {
    width: 76px;
    height: 76px;
    margin-left: 20px;
    background: url(../../../assets/images/<EMAIL>) no-repeat center;
    background-size: 72px auto;
}
</style>
