<template>
    <popup
        class="lesson-recommend-popup"
        @closed="closed"
        :isInDialog="isInDialog"
        :position="'bottom'"
        :show.sync="visible"
        v-if="showLessonRecommend"
    >
        <div class="wrap">
            <div class="header">精选好课</div>
            <lessonRecommend :isInDialog="isInDialog" :hktjLessonData="hktjLessonData" />
        </div>
    </popup>
</template>

<script>
import {URLParams} from '../../../utils/tools'
import {webClose} from '../../../utils/jump'
import {getLessonList} from '../../../server/topLesson'
import popup from '../../../components/dialog'
import lessonRecommend from '../../../components/lessonRecommendList'

export default {
    data() {
        return {
            showLessonRecommend: false,
            visible: false,
            hktjLessonData: {},
        }
    },
    components: {popup, lessonRecommend},
    watch: {
        show(val) {
            this.visible = val
        },
    },
    props: {
        show: Boolean,
        isInDialog: <PERSON><PERSON><PERSON>,
    },
    async mounted() {
        this.getLessonList()
    },
    methods: {
        closed() {
            if (this.isInDialog) {
                webClose()
            } else {
                this.close()
            }
        },
        close() {
            this.$emit('update:show', false)
        },
        async getLessonList() {
            const resData = await getLessonList({
                kemu: URLParams.kemuNum,
                tagKey: 'HKTJ',
                page: 1,
                limit: 1000,
                carType: URLParams.carStyle,
            })
            this.hktjLessonData = resData
            this.showLessonRecommend = resData.itemList.length > 0

            if (this.showLessonRecommend) {
                this.$EventBus.$emit('setLessonRecommedIcon', true)
            }
        },
    },
}
</script>

<style lang="less" scoped>
.lesson-recommend-popup {
    /deep/ .dialog {
        height: 1030px;
    }
    .header {
        height: 98px;
        padding-left: 30px;
        font-size: 36px;
        line-height: 98px;
        font-weight: 600;
        text-align: center;
    }
    .wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        border-radius: 24px 24px 0px 0px;
        background-color: #f2f2f2;
    }
}
</style>
