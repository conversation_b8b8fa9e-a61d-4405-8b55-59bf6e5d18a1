<template>
    <div class="major-chat">
        <rules
            v-show="!isLandscape"
            v-if="roomDetail.orientation !== 1 && bizConfig.hasActivityRule && isMucang && bizConfig.playStatus === 1"
        />
        <chat :showErrorTips="showErrorTips" :recordPlay="!startPlay">
            <template v-slot:motion>
                <div class="stretch">
                    <advert
                        v-if="(bizConfig.saleMode || bizConfig.stMode || isXCJ) && pendantResource.advertType"
                        v-show="(!recommendVisualStatus || bizConfig.nopayMotionVisible) && !isLandscape"
                        :advertDetail="pendantResource"
                        ref="motion"
                    />
                    <template v-if="isXCJ && isMucang">
                        <buyLottery
                            v-if="lotteryInfo"
                            @setLotteryInfo="setLotteryInfo"
                            v-show="!recommendVisualStatus"
                            :lotteryInfo="lotteryInfo"
                            :liveId="xueche66LiveId"
                        />
                        <award
                            v-if="lotteryInfo"
                            v-show="!recommendVisualStatus"
                            :lotteryInfo="lotteryInfo"
                            :liveId="xueche66LiveId"
                        />
                    </template>
                    <recommendBuy
                        v-if="bizConfig.saleMode || bizConfig.stMode"
                        @noticeRecommendVisualStatus="noticeRecommendVisualStatus"
                        ref="recommendBuy"
                        :recommendContent="recommendContent"
                    />
                    <!-- <examReportIcon /> -->
                    <!-- <template v-if="roomDetail.orientation === 1">
                        <div class="gap"></div>
                        <div class="pass-rate"><passRate /></div
                    ></template> -->
                </div>
            </template>
            <template v-slot:bottom>
                <boughtMessage :boughtMessageList.sync="boughtMessageList" />
            </template>
            <template v-slot:icons v-if="isMucang">
                <div class="icon-list" v-show="!isLandscape">
                    <xrzxIcon />
                    <lessonRecommendIcon />
                    <helpIcon />
                    <menuIcon id="J-menu-icon" />
                </div>
                <!-- <button @click="bought">购买</button> -->
                <!-- <button @click="recommendBuy">推荐</button> -->
            </template>
        </chat>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {
    isMucang,
    URLParams,
    dataWebpHelper,
} from '../../../utils/tools'
import {ROOM_TYPE} from '../../../utils/constant'
import {getBatchSellRate, getSellApplyScene} from '../../../server/topLesson'
import {getNextLotteryData} from '../../../server/xueche66'
import advert from '../../../components/advert'
import recommendBuy from '../../../components/recommendBuy.vue'
import boughtMessage from './boughtMessage.vue'
import rules from './rules.vue'
// import examReportIcon from './examReportIcon.vue'
import lessonRecommendIcon from './lessonRecommendIcon.vue'
import xrzxIcon from './xrzxIcon.vue'
import helpIcon from './helpIcon.vue'
import menuIcon from './menuIcon.vue'
// import passRate from './passRate.vue'
import chat from './chat.vue'

// https://shangzhibo.tv/watch/10446311
window.buySuccess = function() {
    console.log('buySuccess')
}

export default {
    components: {
        advert,
        recommendBuy,
        // examReportIcon,
        award: () => import('../../../components/award.vue'),
        buyLottery: () => import('../../../components/buyLottery.vue'),
        boughtMessage,
        rules,
        lessonRecommendIcon,
        xrzxIcon,
        helpIcon,
        menuIcon,
        // passRate,
        chat,
    },
    data() {
        return {
            isMucang,
            kemu: URLParams.kemu,
            carStyle: URLParams.carStyle,
            boughtMessageList: [],
            recommendContent: {},
            recommendVisualStatus: false,
            lotteryInfo: null,
            cityCode: URLParams._userCity || '420100',
            sellRateTimer: null,
            xueche66LiveId: +URLParams.xueche66LiveId,
            qaModelVisible: false,
        }
    },
    props: {
        showErrorTips: Boolean,
        startPlay: Boolean,
    },
    computed: {
        ...mapState([
            'roomDetail',
            'bizConfig',
            'pendantResource',
            'isLandscape',
            'roomResource',
        ]),
        isXCJ() {
            return this.roomDetail.roomType === ROOM_TYPE.XCJ66
        },
    },
    created() {
        if (this.showErrorTips) return
        if (URLParams.carStyle === 'car') {
            this.initStockInfo()
        }
        if (this.isXCJ) {
            this.initLotteryInfo()
        }
        this.$EventBus.$on('mqttMessage', this.onMessage)
        this.$EventBus.$on('updateLotteryData', () => {
            this.initLotteryInfo()
        })
    },
    methods: {
        async initStockInfo() {
            clearInterval(this.sellRateTimer)
            this.sellRateTimer = null
            this.getSellRate()
            this.sellRateTimer = setInterval(() => {
                // 安卓客户端 通过协议轮询接口 会影响登录成功callback，原因待查
                if (document.visibilityState === 'visible') {
                    this.getSellRate()
                }
            }, 10000)

            const resData = await getSellApplyScene({
                lessonItemId: URLParams.id,
                carType: URLParams.carStyle,
            })
            this.$store.commit('updateRemainSotckInfo', {
                applyScene: resData.value || '',
            })
        },
        async getSellRate() {
            const resData = await getBatchSellRate(
                {
                    liveSessionId: URLParams.id,
                },
                {noConsole: true}
            )
            let dataList = resData.itemList.map(item => {
                let sellRateNum
                if (item.value === 0) {
                    sellRateNum = 0
                } else {
                    sellRateNum = Math.floor(item.value * 100)
                    sellRateNum = Math.max(sellRateNum, 1)
                }
                return {
                    sellRateEnable: item.enable,
                    sellRateNum,
                }
            })

            this.$store.commit('updateRemainSotckInfo', {
                dataList,
            })
        },
        setLotteryInfo(lotteryInfo) {
            this.lotteryInfo = lotteryInfo
        },
        async initLotteryInfo() {
            const resData = await getNextLotteryData({
                cityCode: this.cityCode,
                liveId: this.xueche66LiveId,
            })
            this.lotteryInfo = resData
        },
        noticeRecommendVisualStatus(val) {
            this.recommendVisualStatus = val
        },
        onMessage(message) {
            try {
                let boughts = []
                message.forEach(item => {
                    if (item.type === 'status') {
                        if (item.content.connect) return

                        let type = item.content.type
                        if (
                            type === 'boughtVip' &&
                            URLParams.carStyle === 'car' &&
                            this.pendantResource.popupTitle &&
                            this.pendantResource.hotSaleTip
                        ) {
                            boughts.push({
                                id: +new Date(),
                                name: this.pendantResource.popupTitle,
                                tip: this.pendantResource.hotSaleTip,
                            })
                        } else if (
                            type === 'recommendGoods' &&
                            URLParams.carStyle === item.content.carType &&
                            item.content.sceneCode.indexOf(URLParams.sceneCode) > -1 &&
                            (!this.pendantResource.bought || this.pendantResource.upgrade) &&
                            !this.bizConfig.nopayMotionVisible
                        ) {
                            this.openRecommendBuy(dataWebpHelper(item.content))
                        } else if (
                            type === 'closeRecommendGoods' &&
                            URLParams.carStyle === item.content.carType &&
                            item.content.sceneCode.indexOf(URLParams.sceneCode) > -1
                        ) {
                            this.recommendContent = {}
                            this.$refs['recommendBuy'].closeRecommendBuy()
                        }
                    }
                })
                if (boughts.length) {
                    this.boughtMessageList.push(...boughts)
                }
            } catch (error) {
                console.error(error)
            }
        },
        bought() {
            this.onMessage([
                {
                    content: {
                        goodsKey: 'channel_ke1',
                        nickName: 'vip全科潘',
                        type: 'boughtVip',
                    },
                    id: 0,
                    messageType: 'messageBought',
                    type: 'status',
                    uniqueId: '71fdb3aa7de74aeeb8c8f6b31551d25e',
                },
            ])
        },
        recommendBuy() {
            this.onMessage([
                {
                    type: 'status',
                    content: {
                        carType: 'car',
                        recommendType: 'vip',
                        group: 1,
                        type: 'recommendGoods',
                        goodsUniqueKey: 'channel_kemuall_new',
                        buyTag: '考不过补偿140元',
                        imgUrl:
                            'http://jiakao-web.mc-cdn.cn/jiakao-web/2022/09/01/16/b9fa7a2502824556b7a0c0fff4e88031.png',
                        iconUrl:
                            'http://jiakao-web.mc-cdn.cn/jiakao-web/2022/09/01/16/0aba5f3072d54ef9bf3ea43af4614cf7.png',
                        itemId: 100267,
                        popupTime: 60,
                        sceneCode: '101',
                    },
                },
            ])
        },
        openRecommendBuy(content) {
            // 刷新库存
            if (URLParams.carStyle === 'car') {
                this.getSellRate()
            }
            this.recommendContent = content
            this.$refs['recommendBuy'].openRecommendBuy()
        },
        buyGroupGoods(groupId) {
            this.$refs['motion'].buyGroupGoods(groupId, 'groupPush')
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.major-chat {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    height: 0;
}
.major-chat {
    .pass-rate {
        position: absolute;
        bottom: 100%;
        right: -20px;
        margin-bottom: 6px;
    }
    .stretch {
        display: flex;
        flex-direction: column-reverse;
        align-items: flex-end;
        flex: 1;
        position: relative;
        padding-right: 20px;
        padding-bottom: 20px;
    }
    .gap {
        flex: 1;
    }
}

.icon-list {
    display: flex;
    flex-wrap: nowrap;
}
.confirm-image {
    padding: 30px 0 20px;
    img {
        width: 250px;
    }
}
.landscape {
    .stretch {
        position: unset;
    }
}
.portrait {
    .stretch {
        // autoprefixer 自动添加的代码
        // -webkit-box-direction: reverse;
        // 这行代码会导致，从横屏切回竖屏，运营位区域宽度撑不开
        // https://jira.mucang.cn/browse/JKBD-15935
        -webkit-box-direction: initial;
    }
}
</style>
