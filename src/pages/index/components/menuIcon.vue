<template>
    <div class="menu-icon" @click="openMenu">
        <popup class="menu-popup" :position="'bottom'" :show.sync="visible">
            <div class="content">
                <div v-if="isMainLive" @click="openAwardList">
                    我的奖品
                </div>
                <div @click="goFeedback">举报</div>
            </div>
        </popup>

        <iframePopup :show.sync="awardListPopupVisible" :src="awardPage" />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import popup from '../../../components/dialog'
import iframePopup from '../../../components/iframePopup'
import {URLParams, webOpen} from '../../../utils/tools'
import {ROOM_TYPE, awardListURL} from '../../../utils/constant'

export default {
    components: {popup, iframePopup},
    data() {
        return {
            visible: false,
            carType: URLParams.carStyle,
            awardListPopupVisible: false,
            awardPage: `${awardListURL}?tabType=prizeList`,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig']),
        isMainLive() {
            return (
                this.roomDetail.roomType !== ROOM_TYPE.XCJ66 &&
                this.roomDetail.roomType !== ROOM_TYPE.GGZB
            )
        },
    },
    mounted() {
        this.$EventBus.$on('openAwardList', () => {
            this.openAwardList()
        })
    },
    methods: {
        close() {
            this.visible = false
        },
        openAwardList() {
            this.awardListPopupVisible = true
            this.close()
        },
        goFeedback() {
            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                webOpen({
                    url:
                        'https://feedback.nav.mucang.cn/send-feedback?application=jiakaobaodian&category=zhibokejubao',
                })
            })
        },
        openMenu() {
            this.visible = true
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.menu-icon {
    width: 76px;
    height: 76px;
    margin-left: 20px;
    background: url(../../../assets/images/<EMAIL>) no-repeat center;
    background-size: cover;
}
.menu-popup {
    .content {
        background-color: #fff;
        display: flex;
        border-radius: 40px 40px 0 0;
        flex-direction: column;
        justify-content: space-between;
    }
    .header {
        .fontSizeWithElder(40px);
        text-align: center;
        span {
            color: #ff3632;
        }
    }
    .content {
        text-align: center;
        padding: 20px 0;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
        > div {
            padding: 20px 0;
            .fontSizeWithElder(32px);
            line-height: 60px;
        }
    }
}
</style>
