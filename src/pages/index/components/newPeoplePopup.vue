<template>
    <div>
        <popup
            class="newpeople-popup"
            @closed="closed"
            :position="'center'"
            :bottomCloseIcon="true"
            :show.sync="visible"
        >
            <div class="wrap" :class="isAndroid && 'android-dialog'">
                <div class="modal-head">
                    <div class="time">
                        <timeDown :time="validityTime" />&nbsp;&nbsp;&nbsp;后结束
                    </div>
                    <p>仅限{{ getExpireTime }}小时内新安装用户使用</p>
                </div>
                <img :src="cartImg" alt="" />
                <div class="pay_list" v-if="isAndroid">
                    <div
                        :class="item.checked ? 'checked' : ''"
                        @click="checkPayType(index)"
                        v-for="(item, index) in payList"
                        :key="item.name"
                    >
                        {{ item.name }}
                    </div>
                </div>
                <div class="buy-button" @click="buyVip('新人专享弹窗')">
                    {{ vipGoodsDetail.price }}元立即抢
                    <div class="tip">比分开买更省钱</div>
                </div>
                <vipAgreement theme="light"></vipAgreement>
            </div>
        </popup>
        <popup
            class="newpeople-popup"
            :bottomCloseIcon="true"
            :position="'center'"
            :show.sync="showLeave"
        >
            <div class="wrap leave" :class="isAndroid && 'android-dialog'">
                <div class="leave-head">
                    <p>
                        您的优惠将在 &nbsp;<timeDown :time="validityTime" theme="red" />
                        &nbsp;后将被取消<br />
                    </p>
                    <p>
                        请尽快使用
                    </p>
                </div>
                <img :src="cartImg" alt="" />
                <div class="pay_list" v-if="isAndroid">
                    <div
                        :class="item.checked ? 'checked' : ''"
                        @click="checkPayType(index)"
                        v-for="(item, index) in payList"
                        :key="item.name"
                    >
                        {{ item.name }}
                    </div>
                </div>
                <div class="buy-button" @click="buyVip('新人专享挽留弹窗')">
                    {{ vipGoodsDetail.price }}元立即抢
                    <div class="tip">比分开买更省钱</div>
                </div>
                <vipAgreement theme="light"></vipAgreement>
            </div>
        </popup>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {find, get} from 'lodash-es'
import timeDown from '../../../components/timeDown'
import {getUserPromotion, getPromotionExtraInfo} from '../../../server/goods'
import {createMobileOrder, getPayAfterStrategy} from '../../../utils/payHelper'
import {
    getData,
    isAndroid,
    isIOS,
    URLParams,
    checkPayChannels,
    trackEvent,
    getProvide,
} from '../../../utils/tools'
import popup from '../../../components/dialog'
import vipAgreement from '../../../components/vipAgreement'
let localDataKey = 'jiaokaobaodian-zhibojian-new-people-popup'

export default {
    components: {timeDown, popup, vipAgreement},
    inject: ['waitPupopList'],
    data() {
        return {
            isAndroid: isAndroid,
            vipGoodsDetail: {
                price: null,
            },
            payList: [
                {
                    channelName: 'alipay_mobile',
                    type: 1,
                    name: '支付宝支付',
                    checked: 0,
                },
            ],
            validityTime: -1,
            expireTime: 0,
            showLeave: false,
            cartImg: '',
            visible: false,
        }
    },
    computed: {
        ...mapGetters(['checkAgreement', 'readed']),
        getExpireTime() {
            return this.expireTime / 60 / 60
        },
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    props: {
        show: Boolean,
    },
    created() {
        if (isAndroid) {
            checkPayChannels().then(data => {
                if (data && data.wx) {
                    this.payList.unshift({
                        channelName: 'weixin_mobile',
                        type: 2,
                        name: '微信支付',
                        checked: 0,
                    })
                }
                this.payList[0].checked = 1
            })
        }
    },
    async mounted() {
        this.checkIsNewPeople()
    },
    methods: {
        closed() {
            this.$emit('update:show', false)
        },
        webClose() {
            this.$EventBus.$emit('webClose')
        },
        checkPayType(index) {
            if (this.payList[index].checked) return false
            this.payList.forEach(item => {
                item.checked = 0
            })
            this.payList[index].checked = 1
        },
        async buyVip(fragmentName1) {
            if (this.checkAgreement && !this.readed) {
                await this.$confirmProtocol()
            }
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {checked: 1})
                payType = pay.type
                payChannel = pay.channelName
            }
            createMobileOrder(
                {
                    sessionIds: this.vipGoodsDetail.sessionIdList.join(','),
                    appleId: this.vipGoodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    activityType: this.vipGoodsDetail.activityType,
                    groupKey: this._groupKey,
                    squirrelGoodsInfo: this.vipGoodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this._groupKey,
                    },
                },
                getPayAfterStrategy(false, 'vip')
            )

            let actionType = '点击'
            let actionName = '去支付'

            // v8.17.0-埋点文档
            // 精品课直播间页_新人专享弹窗_点击去支付
            // 精品课直播间页_新人专享挽留弹窗_点击去支付
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                payPathType: 0,
                groupKey: this._groupKey,
            })
        },
        async judge() {
            const {itemList} = await getUserPromotion({
                tiku: URLParams.carStyle,
                promotionType: 5,
            })
            let data = itemList[0]
            try {
                this._groupKey = data.groupKey
                if (await getProvide()) {
                    this.cartImg = get(data, 'promotionDetail.uiConfig.img')
                } else {
                    getPromotionExtraInfo({
                        groupKey: this._groupKey,
                        promotionType: 5,
                    }).then(resData => {
                        let v = JSON.parse(resData.value || '{}')
                        this.cartImg = v.img
                    })
                }

                let promotionPriceInfo = data.promotionDetail.promotionPriceInfo
                delete data.promotionDetail

                let vipGoodsDetail = {
                    ...data,
                    ...promotionPriceInfo,
                }
                this.vipGoodsDetail = vipGoodsDetail
                if (!this.vipGoodsDetail.bought || this.vipGoodsDetail.upgrade) {
                    this.$EventBus.$emit('setXrzxIcon', true)

                    let actObj = localStorage.getItem(localDataKey)
                    actObj = JSON.parse(actObj)
                    actObj = actObj || {}
                    actObj.autoPopupItemIds = actObj.autoPopupItemIds || []
                    let isShow = actObj.autoPopupItemIds.indexOf(URLParams.id)
                    if (isShow < 0) {
                        console.log('waitPupopList', 'newpeople')
                        this.waitPupopList.push({
                            callback: () => {
                                this.$EventBus.$emit('setOrientation', 'portrait', () => {
                                    this.visible = true
                                    actObj.autoPopupItemIds.push(URLParams.id)
                                    localStorage.setItem(localDataKey, JSON.stringify(actObj))
                                })

                                let fragmentName1 = '新人专享弹窗'
                                let actionType = '出现'

                                // v8.17.0-埋点文档
                                // 精品课直播间页_新人专享弹窗_出现
                                trackEvent({fragmentName1, actionType})
                            },
                        })

                        this.taskList.push({
                            _uid: this._uid,
                            priority: 109,
                            callback: () => {
                                this.showLeave = true

                                let fragmentName1 = '新人专享挽留弹窗'
                                let actionType = '出现'

                                // v8.17.0-埋点文档
                                // 精品课直播间页_新人专享挽留弹窗_出现
                                trackEvent({fragmentName1, actionType})
                            },
                        })
                    }
                }
            } catch (error) {}
        },
        async checkIsNewPeople() {
            let data = await getData('jkFirstSettingTime')
            // 用户首次安装时间
            const {time: installTime = new Date().getTime()} = data

            data = await getData('jkNewVipConfig')
            // 活动有效期，一般为24小时
            const {expireTime = 0} = data
            const endTime = installTime + expireTime * 1000
            const validityTime = endTime - new Date().getTime()
            this.validityTime = validityTime
            this.expireTime = expireTime

            if (validityTime > 0) {
                this.judge()
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.newpeople-popup {
    .wrap {
        width: 610px;
        height: 794px;
        padding: 114px 43px 0;
        background: url(../../../assets/images/new-people-bg.png) no-repeat;
        background-size: cover;
        padding-bottom: calc(constant(safe-area-inset-bottom) -40px);
        padding-bottom: calc(env(safe-area-inset-bottom) -40px);
        &.leave {
            background-image: url(../../../assets/images/new-people-leave-bg.png);
        }
        .modal-head {
            .time {
                display: flex;
                justify-content: center;
                font-size: 28px;
                font-weight: 600;
                text-align: center;
                color: #ffffff;
            }
            p {
                font-size: 22px;
                text-align: center;
                color: #ffffff;
                margin-top: 20px;
            }
        }
        .leave-head {
            p {
                font-size: 26px;
                text-align: center;
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 6px;
            }
        }
        img {
            width: 524px;
            height: 390px;
            margin: 20px auto 0;
        }
        .buy-button {
            width: 360px;
            height: 114px;
            line-height: 85px;
            background: url(../../../assets/images/btn.png) no-repeat;
            background-size: cover;
            font-size: 38px;
            font-weight: 600;
            text-align: center;
            color: #ee1651;
            margin: 24px auto 0;
            position: relative;
            .tip {
                position: absolute;
                top: -25px;
                right: -38px;
                height: 42px;
                width: 194px;
                line-height: 42px;
                color: #fff;
                font-size: 24px;
                font-weight: normal;
                background: url(../../../assets/images/tip.png) no-repeat;
                background-size: cover;
            }
        }
        .pay_list {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            .fontSizeWithElder(28px);
            color: #fff;
            padding: 20px 0 10px;
            > div {
                margin: 0 30px;
                padding: 0 10px 0 72px;
                background: url(../../../assets/images/<EMAIL>) no-repeat 20px center;
                background-size: 36px auto;

                &.checked {
                    background-image: url(../../../assets/images/<EMAIL>);
                }
            }
        }
        /deep/ .agreement {
            position: relative;
            margin-top: -15px;
            text-align: center;
            // padding: 15px 30px 10px;
            color: rgba(255, 255, 255, 0.8);
            span {
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }
    .android-dialog {
        width: 610px;
        height: 888px;
        padding: 114px 43px 0;
        background: url(../../../assets/images/new-people-bg-android.png) no-repeat;
        background-size: cover;
        padding-bottom: calc(constant(safe-area-inset-bottom) -40px);
        padding-bottom: calc(env(safe-area-inset-bottom) -40px);
        &.leave {
            background-image: url(../../../assets/images/new-people-leave-bg-android.png);
        }
        /deep/ .agreement {
            margin-top: -5px;
        }
    }
}
</style>
