<template>
    <div class="pass-rate-wraper" @click="beforeOpen">
        <i></i>
        <div class="info">
            <div class="text">
                预测通过率
                <span class="rate">
                    {{ passRate + '%' }}
                </span>
            </div>
            <div class="status">
                {{ examDayText }}
            </div>
        </div>
        <PassRatePop @closed="closed" :show.sync="passRateIsShow" />
    </div>
</template>

<script>
import {throttle} from 'lodash-es'
import {MCProtocol} from '@simplex/simple-base'
import {URLParams, trackEvent, formatDate} from '../../../utils/tools'
import PassRatePop from './passRatePop.vue'
const oneTime = 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000

export default {
    data() {
        return {
            passRate: '0',
            downDay: '',
            passRateIsShow: false,
        }
    },
    components: {
        PassRatePop,
    },

    created() {
        this.getPassRate()
        this.getExamDate()
    },
    mounted() {
        let fragmentName1 = '预测通过率'
        let actionType = '出现'

        // v8.17.0-埋点文档
        // 精品课直播间页_预测通过率_出现
        trackEvent({fragmentName1, actionType})
    },

    computed: {
        examDayText() {
            if (this.downDay > 0) {
                return `距离考试${this.downDay}天>`
            } else if (this.downDay === 0) {
                return `祝您考试顺利`
            } else {
                return `选择考试时间>`
            }
        },
    },

    methods: {
        closed() {
            this.getExamDate()
        },
        handleShowData(passRate) {
            if (passRate <= 0) {
                return 0
            }
            let newpassRate = String(passRate)
            if (newpassRate.indexOf('.') !== -1) {
                newpassRate = newpassRate.substring(0, newpassRate.indexOf('.') + 2)
            }
            return Number(newpassRate) >= 100 ? 100 : Number(newpassRate)
        },
        getPassRate() {
            MCProtocol.Vip.getPassRate({
                car: URLParams.carStyle,
                kemu: URLParams.kemuNum,
                callback: ret => {
                    const passRate = this.handleShowData((ret.data * 1000) / 10 || 0)
                    this.passRate = passRate
                },
            })
        },

        getExamDate() {
            MCProtocol['jiakao-global'].web.getExamDate({
                kemu: URLParams.kemuNum,
                car: URLParams.carStyle,
                callback: res => {
                    let examTime = 0
                    if (res.data.date) {
                        examTime = res.data.date
                    }
                    // let isExpirTime = false
                    if (examTime) {
                        const examTimeString = formatDate(examTime, 'yyyy/MM/dd  00:00:00')
                        examTime = new Date(examTimeString).getTime() + oneTime

                        // isExpirTime = examTime - new Date(new Date().toDateString()).getTime() <= 0
                    }
                    const day = this.getDistanceDays(new Date(), examTime)
                    this.downDay = day
                },
            })
        },

        getDistanceDays(currentDate, targetDate) {
            const currentDateTimeStamp = new Date(currentDate)
            const targetTimeStamp = new Date(targetDate)
            return Math.floor((targetTimeStamp - currentDateTimeStamp) / (24 * 60 * 60 * 1000))
        },
        beforeOpen: throttle(
            function() {
                if (this.downDay >= 0) {
                    this.onPassRate()
                } else {
                    this.onExamDay()
                }
            },
            1000,
            {trailing: false}
        ),
        onPassRate() {
            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                this.passRateIsShow = true
            })

            let fragmentName1 = '预测通过率'
            let actionType = '点击'
            let actionName = '去支付'

            // v8.17.0-埋点文档
            // 精品课直播间页_预测通过率_点击去支付
            trackEvent({fragmentName1, actionType, actionName, payPathType: 0})
        },

        onExamDay() {
            MCProtocol['jiakao-global'].web.showExamDateAlert({
                kemu: URLParams.kemuNum,
                car: URLParams.carStyle,
                callback: () => {
                    this.getExamDate()
                    this.onPassRate()
                },
            })
        },
    },
}
</script>

<style lang="less" scoped>
.pass-rate-wraper {
    width: 328px;
    height: 88px;
    background: url(../images/rate-bg.png) no-repeat;
    background-size: cover;
    padding: 8px 0 8px 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
        display: inline-block;
        width: 72px;
        height: 72px;
        background: url(../images/xxcp.png) no-repeat;
        background-size: cover;
    }
    .info {
        flex: 1;
        font-size: 24px;
        color: #fff;
        text-align: center;
        .text {
            white-space: nowrap;
        }
        .rate {
            color: #ffe29c;
        }
        .status {
            font-size: 22px;
            height: 33px;
            padding: 2px 16px;
            line-height: 31px;
            white-space: nowrap;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            display: inline-block;
        }
    }
}
.vertical {
    .pass-rate-wraper {
        width: 288px;
        height: 94px;
        background-image: url(../images/rate-bg2.png);
    }
}
</style>
