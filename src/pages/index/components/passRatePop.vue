<template>
    <popup class="pass-rate-popup" @closed="closed" :position="'bottom'" :show.sync="visible">
        <div
            class="wrap"
            @touchmove="touchmove"
            @touchstart="touchstart"
            @touchend="touchend"
            :style="{height: height + 'px'}"
            :class="{full: isTouchLock}"
        >
            <div class="header">预测通过率分析</div>
            <div class="content">
                <div class="ifram-container">
                    <iframe width="100%" height="100%" :src="url"></iframe>
                </div>
                <div
                    v-if="!isTouchLock"
                    class="touch"
                    @touchmove="touchmove"
                    @touchstart="touchstart"
                    @touchend="touchend"
                ></div>
            </div>
            <div
                class="footer"
                v-if="vipGoodsDetail.price && (!vipGoodsDetail.bought || vipGoodsDetail.upgrade)"
            >
                <div class="pay_list" v-if="isAndroid">
                    <div
                        :class="item.checked ? 'checked' : ''"
                        @click="checkPayType(index)"
                        v-for="(item, index) in payList"
                        :key="item.name"
                    >
                        {{ item.name }}
                    </div>
                </div>
                <div class="majs">
                    <div class="buy-vip" @click="buyVipGoods">
                        <div class="tip">考不过补偿140元</div>
                        <div class="price">
                            ¥<span>{{ showPrice }}</span>
                            {{ vipGoodsDetail.upgrade ? '升级' : '开通' }}{{ vipGoodsDetail.name }}
                        </div>
                        <div class="day">有效期{{ vipGoodsDetail.validDays }}天</div>
                    </div>
                </div>
                <div class="line between">
                    <vipAgreement theme="blue"></vipAgreement>
                    <div class="ofc" v-if="isIOS" @click="goPayGuide">
                        支付教程
                    </div>
                </div>
            </div>
        </div>
    </popup>
</template>

<script>
import {mapState, mapGetters} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {find} from 'lodash-es'
import {
    isAndroid,
    getUrl,
    getURLParams,
    checkPayChannels,
    webOpen,
    toast,
    URLParams,
    isIOS,
    trackEvent,
} from '../../../utils/tools'
import {createMobileOrder, getPayAfterStrategy} from '../../../utils/payHelper'
import popup from '../../../components/dialog'
import vipAgreement from '../../../components/vipAgreement'
import touchMixin from '../../../utils/touchMixin'
export default {
    mixins: [touchMixin],
    components: {popup, vipAgreement},
    data() {
        return {
            visible: false,
            isAndroid,
            isIOS,
            payList: [
                {
                    channelName: 'alipay_mobile',
                    type: 1,
                    name: '支付宝支付',
                    checked: 0,
                },
            ],
            vipGoodsDetail: {},
            src: `https://laofuzi.kakamobi.com/pass-analysis/home.html?inletSource=zhibojian`,
        }
    },
    props: {
        show: Boolean,
        fragmentName1: String,
    },
    computed: {
        ...mapState(['pendantResource', 'isLandscape', 'bizConfig']),
        ...mapGetters(['checkAgreement', 'readed']),
        url() {
            var params = getURLParams(null, window.location.href)
            let url = this.src
            let urlParams = getURLParams(null, url)
            // url上如果没有kemu或者kemuStyle,手动带上直播间的kemu
            if (!(urlParams.kemu || urlParams.kemuStyle) && URLParams.kemuStyle) {
                url = getUrl(url, {kemuStyle: URLParams.kemuNum})
            }
            return getUrl(
                url,
                Object.assign(params, {
                    pageName: stat.getPageName(),
                    fragmentName1: this.fragmentName1,
                })
            )
        },
        showPrice() {
            let payPrice = this.vipGoodsDetail.price
            return payPrice
        },
    },
    watch: {
        show(val) {
            this.visible = val
            if (val) {
                // TODO 通过率分析 购买
            }
        },
        isTouchLock() {
            this.$EventBus.$emit('minimizePlayer', this.isTouchLock, {
                groupKey: this.pendantResource.goodsKey,
                goodsUniqueKey: this.pendantResource.goodsKey,
            })
        },
    },
    created() {
        this.$EventBus.$on('quitMinimize', () => {
            this.close()
        })
    },
    mounted() {
        if (isAndroid) {
            checkPayChannels().then(data => {
                if (data && data.wx) {
                    this.payList.unshift({
                        channelName: 'weixin_mobile',
                        type: 2,
                        name: '微信支付',
                        checked: 0,
                    })
                }
                this.payList[0].checked = 1
            })
        }
    },
    methods: {
        closed() {
            this.close()
        },
        goPayGuide() {
            webOpen({
                url: 'https://laofuzi.kakamobi.com/jkbd-vip/index/payguide.html?fromPageType=1',
                titleBar: false,
                title: '支付教程',
            })
        },
        close() {
            this.$emit('update:show', false)
            this.$emit('closed')
        },
        checkPayType(index) {
            if (this.payList[index].checked) return false
            this.payList.forEach(item => {
                item.checked = 0
            })
            this.payList[index].checked = 1
        },
        async buyVipGoods() {
            if (this.sellRateEnable && this.remainSotckInfo.sellRateNum === 0) {
                toast('优惠名额已售罄')
                return
            } else if (this.checkAgreement && !this.readed) {
                await this.$confirmProtocol()
            }
            let fragmentName1 = '预测通过率'
            this.payForVip(fragmentName1)

            let fragmentName2 = '支付弹窗'
            let actionType = '点击'
            let actionName = '确认支付'

            // v8.17.0-埋点文档
            // 精品课直播间页_预测通过率_支付弹窗_点击确认支付
            trackEvent({fragmentName1, fragmentName2, actionType, actionName, groupKey: this.pendantResource.goodsKey})
        },
        payForVip(fragmentName1) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {checked: 1})
                payType = pay.type
                payChannel = pay.channelName
            }

            // TODO 购买成功跳转状态页
            createMobileOrder(
                {
                    sessionIds: this.vipGoodsDetail.sessionIdList.join(','),
                    appleId: this.vipGoodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    activityType: this.vipGoodsDetail.activityType,
                    groupKey: this.pendantResource.goodsKey,
                    squirrelGoodsInfo: this.vipGoodsDetail.squirrelGoodsInfo,
                    promotionType: this.vipGoodsDetail.promotionActivityData.activityExt,
                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        groupKey: this.pendantResource.goodsKey,
                        lessonId: URLParams.id,
                        liveActivityId: this.vipGoodsDetail.promotionActivityData.activityId,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this.pendantResource.goodsKey,
                    },
                },
                getPayAfterStrategy(false, 'vip')
            )
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.pass-rate-popup {
    .wrap {
        background-color: #fff;
        display: flex;
        border-radius: 20px 20px 0 0;
        overflow: hidden;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
        &.full {
            border-radius: 0;
        }

        .header {
            background: #c2e4ff;
            height: 98px;
            padding-left: 30px;
            font-size: 34px;
            line-height: 98px;
            font-weight: bold;
        }
        .content {
            flex: 1;
            position: relative;
            .ifram-container {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                width: 100%;
            }
            iframe {
                background: #c2e4ff;
            }
        }
        .touch {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }
        iframe {
            border: 0;
        }
    }
    .footer {
        .pay_list {
            display: flex;
            .fontSizeWithElder(28px);
            color: #333;
            padding: 6px 30px;
            border-bottom: 1px solid #e8e8e8;
            > div {
                margin-right: 60px;
                padding: 10px 10px 10px 72px;
                background: url(../../../assets/images/<EMAIL>) no-repeat 20px center;
                background-size: 36px auto;

                &.checked {
                    background-image: url(../../../assets/images/<EMAIL>);
                }
            }
        }
        .ofc {
            .fontSizeWithElder(22px);
        }
        .line {
            padding: 10px 30px;
            display: flex;
            &.between {
                justify-content: space-between;
            }
            &.around {
                justify-content: space-around;
            }
        }
        .majs {
            padding: 8px 0;
        }
        .buy-vip {
            margin: 0 auto;
            width: 716px;
            height: 98px;
            background: url(../../../assets/images/buy-btn.png) no-repeat;
            background-size: 716px 98px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;

            .price {
                font-size: 32px;
                font-weight: bold;
                color: #fff;
                line-height: 1.4;
            }
            .tip {
                position: absolute;
                right: 8px;
                top: -30px;
                width: 204px;
                height: 40px;
                background: url(../../../assets/images/buy-tip-bg.png) no-repeat;
                background-size: 204px 40px;
                color: #6f2117;
                font-size: 22px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .day {
                font-size: 20px;
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }
}
</style>
