<template>
    <div>
        <div class="question-guide" :class="{ani: showAni}" v-if="showGuide">
            <div class="mask"></div>
            <div class="content">
                <div class="text"></div>
                <div class="opt"><img :src="webpImg" /></div>
                <div class="btn-wrap">
                    <div class="btn" @click="showGuide = false">知道了</div>
                </div>
            </div>
        </div>
        <div class="question-home" v-show="!showGuide && currentQuestion.options.length">
            <transition appear name="slide-fade" mode="out-in">
                <div class="question-list" v-if="currentQuestion.options.length">
                    <div class="item num">{{currentQuestion.questionNum}}</div>
                    <div class="options">
                        <div
                            v-for="item in currentQuestion.options"
                            :key="item.label"
                            class="item"
                            :class="{active: selectOptions.indexOf(item.answer) > -1}"
                            @click="check(item.answer)">{{item.label}}</div>
                    </div>
                    <div class="item btn" :class="{active: selectOptions.length > 0}" @click="submit">
                        提交<br>
                        <span>{{ remainTime }}s</span>
                    </div>
                </div>
            </transition>
        </div>
        <popup
            class="result-popup"
            :position="'center'"
            :show.sync="resultVisible"
            :showClose="false"
            :closeOnBlank="false"
        >
            <div class="wrap" :class="answerResult.right ? 'right' : 'wrong'">
                <div class="sign"></div>
                <div class="name">{{ answerResult.right ? '答对了' : '答错了'}}</div>
                <div class="info">
                    <div class="item">
                        正确答案：<span class="answer">{{ answerResult.answer }}</span>
                    </div>
                    <div class="item">
                        您的答案：<span class="my-answer">{{ answerResult.userAnswer }}</span>
                    </div>
                </div>
                <div class="btn" @click="resultVisible = false">我知道了</div>
            </div>
        </popup>
        <popup
            class="count-popup"
            :position="'center'"
            :show.sync="countVisible"
            :showClose="false"
        >
            <div class="wrap">
                <div class="header">
                    <div class="t">答题数据统计</div>
                    <!-- <div class="n">{{ answerCount.rightCount + answerCount.wrongCount }}人参与</div> -->
                </div>
                <div class="line right">
                    <div class="bg" :style="{width: answerCount.rightRate+'%'}"></div>
                    <div class="txt">{{answerCount.rightRate}}%</div>
                </div>
                <div class="line wrong">
                    <div class="bg" :style="{width: answerCount.wrongRate+'%'}"></div>
                    <div class="txt">{{answerCount.wrongRate}}%</div>
                </div>
                <div class="btn" @click="countVisible = false">我知道了</div>
            </div>
        </popup>
        <popup
            class="overview-popup"
            :position="'center'"
            :show.sync="overviewVisible"
            :closeOnBlank="false"
        >
            <div class="wrap">
                <div class="title">考试结束</div>
                <div class="score"><span>{{ overviewData.score }}</span>分</div>
                <div class="tit">本场模考得分</div>
                <div class="info">
                    <div class="item">
                        <div class="num">{{ overviewData.rank }}</div>
                        <div class="name">我的排名</div>
                    </div>
                    <div class="item">
                        <div class="num">{{ overviewData.highestScore }}</div>
                        <div class="name">本场最高分</div>
                    </div>
                    <div class="item">
                        <div class="num">{{ overviewData.greaterNum }}%</div>
                        <div class="name">已击败考生</div>
                    </div>
                </div>
                <div class="btn" @click="openReport">查看完整答题报告</div>
            </div>
        </popup>
        <examReportPopup :show.sync="examReportVisible" :sessionId="sessionId" :activityId="currentQuestion.activityId" />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {URLParams, getAuthToken, goLogin, trackEvent, getCityInfo, isIOS} from '../../../utils/tools'
import popup from '../../../components/dialog'
import examReportPopup from '../../../components/examReportPopup'
import {submitAnswer, getQuestionResultCount, getExamOverview} from '../../../server/active'
import guideImg1 from '../images/guide-opt.webp'
import guideImg2 from '../images/guide-opt.png'

// let localDataKey = 'jiaokaobaodian-zhibojian-question-guide'

const optionArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
const judgeOptionMap = {
    A: '对',
    B: '错',
}
const driftNumber = function(answer) {
    let options = []
    optionArr.forEach((label, i) => {
        const optionAnswer = 16 << i
        Boolean(answer & optionAnswer) && options.push(label)
    })
    return options.join(',')
}
let resultTimer
let answerTimer

// let hasExposeGuide = false

export default {
    components: {popup, examReportPopup},
    data() {
        return {
            sessionId: URLParams.id,
            resultVisible: false,
            countVisible: false,
            overviewVisible: false,
            examReportVisible: false,
            currentQuestion: {
                optionType: 0,
                options: [],
                questionId: 0,
                activityIdId: 0,
            },
            selectOptions: [],
            answerResult: {
                right: false,
                answer: '',
                userAnswer: '',
            },
            answerCount: {
                rightRate: 0,
                wrongRate: 0,
                rightCount: 0,
                wrongCount: 0,
            },
            overviewData: {
                score: 0,
                unAnsweredCount: 0,
                rightCount: 0,
                wrongCount: 0,
                rank: 0,
                highestScore: 0,
                greater: 0,
            },
            showGuide: false,
            showAni: false,
            webpImg: isIOS ? guideImg2 : guideImg1,
            remainTime: 0,
        }
    },
    watch: {
        remainTime(val) {
            if (val === 10) {
                this.$EventBus.$emit('showMsgInChat', {
                    type: 'systemMessage',
                    msg: `第${this.currentQuestion.questionNum}题答题时间剩余10秒钟，请大家抓紧提交答案哦！`,
                })
            }
        },
    },
    computed: {
        ...mapState(['bizConfig']),
    },
    created() {
        this.$EventBus.$on('liveExam', (content) => {
            content.startTime = +new Date()
            content.optionType = content.style
            content.options = content.options.split(',')
            content.options = content.options
                .map((label, i) => {
                    const optionAnswer = 16 << i
                    if (content.style === 0) {
                        label = judgeOptionMap[label]
                    }
                    return {
                        answer: optionAnswer,
                        label,
                    }
                })
            this.selectOptions = []
            this.currentQuestion = content
            this.resultVisible = false
            this.countVisible = false
            this.overviewVisible = false

            // if (!hasExposeGuide) {
            //     let actObj = localStorage.getItem(localDataKey)
            //     actObj = JSON.parse(actObj)
            //     actObj = actObj || {}
            //     actObj.autoPopupItemIds = actObj.autoPopupItemIds || []
            //     let isShow = actObj.autoPopupItemIds.indexOf(URLParams.id)
            //     if (isShow < 0) {
            //         this.showGuide = true
            //         setTimeout(() => {
            //             hasExposeGuide = true
            //             this.showAni = true
            //             actObj.autoPopupItemIds.push(URLParams.id)
            //             localStorage.setItem(localDataKey, JSON.stringify(actObj))
            //         }, 5000)
            //         setTimeout(() => {
            //             this.showGuide = false
            //         }, 15000)
            //     }
            // }

            let t = +new Date() + content.timeLimit * 1000
            clearTimeout(answerTimer)
            answerTimer = setInterval(() => {
                this.remainTime = Math.ceil((t - +new Date()) / 1000)
                if (this.remainTime <= 0) {
                    this.currentQuestion.options = []
                    this.selectOptions = []
                    clearInterval(answerTimer)
                }
            }, 16)
        })
        this.$EventBus.$on('stopCurrentQuestion', (content) => {
            this.currentQuestion.options = []
            this.selectOptions = []
            let {activityId} = content
            if (this.currentQuestion.activityId === activityId) {
                this.getAnswerCount()
            }
            clearTimeout(answerTimer)
        })
        this.$EventBus.$on('stopLiveExam', (content) => {
            this.currentQuestion.options = []
            this.selectOptions = []
            this.getOverviewData()
        })
    },
    methods: {
        check(answer) {
            let index = this.selectOptions.indexOf(answer)
            if (index > -1) {
                if (this.currentQuestion.optionType > 1) {
                    this.selectOptions.splice(index, 1)
                }
            } else {
                if (!(this.currentQuestion.optionType > 1)) {
                    this.selectOptions.pop()
                }
                this.selectOptions.push(answer)
            }
        },
        async submit() {
            let submitTime = +new Date()
            const authToken = await getAuthToken()
            if (!authToken) {
                await goLogin()
            }
            if (this.selectOptions.length === 0) return
            let {activityId, questionId, knowledgeIds, style, score, answer, startTime} = this.currentQuestion
            let takeUpTime = submitTime - startTime
            let userAnswer = this.selectOptions.reduce((prev, curr) => prev + curr)

            let result = answer === userAnswer
            let _answer = driftNumber(answer)
            let _userAnswer = driftNumber(userAnswer)
            if (style === 0) {
                _answer = judgeOptionMap[_answer]
                _userAnswer = judgeOptionMap[_userAnswer]
            }
            this.answerResult = {
                right: result,
                answer: _answer,
                userAnswer: _userAnswer,
            }
            this.currentQuestion.options = []
            this.selectOptions = []
            this.resultVisible = true
            clearTimeout(resultTimer)
            resultTimer = setTimeout(() => {
                this.resultVisible = false
            }, 5000)

            clearTimeout(answerTimer)
            await submitAnswer({
                sessionId: URLParams.id,
                sceneCode: URLParams.sceneCode,
                cityCode: (await getCityInfo()).cityCode,
                activityId,
                questionId,
                answer: userAnswer,
                score: result ? score : 0,
                result: result,
                knowledgeIds,
                takeUpTime,
                style,
            })

            this.$EventBus.$emit('submitQuestion')

            // 精品课直播间页_答题菜单_点击提交
            trackEvent({
                fragmentName1: '答题菜单',
                actionType: '点击',
                actionName: '提交',
            })
        },
        async getAnswerCount() {
            let {activityId, questionId} = this.currentQuestion
            let res = await getQuestionResultCount({sessionId: URLParams.id, activityId, questionId})
            let rightRate, wrongRate
            if (res.rightCounts === 0 && res.errorCounts === 0) {
                rightRate = wrongRate = 0
            } else {
                rightRate = (res.rightCounts / (res.rightCounts + res.errorCounts) * 100).toFixed()
                wrongRate = 100 - rightRate
            }
            this.answerCount = {
                rightRate,
                wrongRate,
                rightCount: res.rightCounts,
                wrongCount: res.errorCounts,
            }
            if (!this.resultVisible) {
                this.countVisible = true
            }
        },
        async getOverviewData() {
            let {activityId} = this.currentQuestion
            let res = await getExamOverview({sessionId: URLParams.id, activityId})
            let greaterNum
            if (res.rawRank === 1) {
                greaterNum = 100
            } else {
                greaterNum = Math.floor((res.userCount - res.rawRank) / res.userCount * 100)
            }
            let overviewData = {
                score: res.score,
                unAnsweredCount: res.noExamQuestionIds.length,
                rightCount: res.rightQuestionIds.length,
                wrongCount: res.wrongQuestionIds.length,
                rank: res.rank,
                highestScore: res.highestScore,
                greaterNum,
            }
            if (overviewData.wrongCount || overviewData.rightCount) {
                this.overviewData = overviewData
                this.resultVisible = false
                this.overviewVisible = true

                this.$EventBus.$emit('setExamReporIcon', {
                    visible: true,
                    activityId: this.currentQuestion.activityId,
                })

                // 精品课直播间页_答题得分弹窗_出现
                trackEvent({
                    fragmentName1: '答题得分弹窗',
                    actionType: '出现',
                })
            }
        },
        openReport() {
            this.overviewVisible = false
            this.examReportVisible = true
        },
    },
}
</script>

<style lang="less" scoped>
.slide-fade-enter-active {
    transition: all 0.3s ease;
}
.slide-fade-leave-active {
    transition: all 0.3s ease;
}
.slide-fade-enter {
    transform: translateX(100%);
}
.slide-fade-leave-to {
    transform: translateX(100%);
}
.question-guide {
    position: fixed;
    z-index: 6;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
    &.ani {
        opacity: 1;
        pointer-events: auto;
    }
    .mask {
        background-color: rgba(0, 0, 0, 0.6);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
    .content {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        z-index: 2;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 50px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 50px);
    }
    .text {
        background: url(../images/guide-text.png) no-repeat center center / 710px auto;
        height: 158px;
    }
    .opt {
        text-align: center;
        img {
            width: 690px;
            height: 90px;
        }
        margin-top: 30px;
    }
    .btn-wrap {
        padding-bottom: 34px;
    }
    .btn {
        margin: 30px auto 0;
        width: 160px;
        height: 60px;
        background: rgba(255,255,255,0.2);
        border: 1px solid #ffffff;
        line-height: 60px;
        border-radius: 40px;
        font-size: 28px;
        text-align: center;
        color: #ffffff;
    }
}
.question-home {
    z-index: 4;
    position: absolute;
    top: 2px;
    right: 20px;
}
.vertical /deep/ {
    .question-home {
        top: 482px;
    }
}
.landscape /deep/ {
    .question-home {
        top: 122px;
    }
}
.question-list {
    padding-top: 12px;
    height: 416px;
    width: 92px;
    background: rgba(0,0,0,0.15);
    border-radius: 8px;
    .options {
        height: 304px;
    }
    .item {
        margin: 0 auto 16px;
        width: 60px;
        height: 60px;
        color: #fff;
        font-size: 28px;
        line-height: 60px;
        background: rgba(0,0,0,0.25);
        border-radius: 100%;
        text-align: center;
        &.active {
            background: #fff;
            color: #04a5ff;
        }
        &.num {
            height: 24px;
            line-height: 24px;
            font-size: 16px;
            border-radius: 12px;
            margin-bottom: 12px;
        }
        &.btn {
            padding-top: 4px;
            line-height: 1.2;
            font-size: 22px;
            border-radius: 8px;
            margin-bottom: 0;
            span {
                font-size: 16px;
            }
            &.active {
                background: linear-gradient(139deg,#15baff 18%, #0499ff 83%);
                color: #fff;
            }
        }
    }
}
.result-popup {
    /deep/ .dialog {
        width: 640px;
    }

    .wrap {
        height: 420px;
        background-color: #fff;
        border-radius: 20px;
        padding-top: 10px;
    }
    .right {
        .sign {
            background: url(../images/right-bg.png) no-repeat center center / 316px 176px;
        }
        .my-answer {
            color: #04a5ff;
        }
    }
    .wrong {
        .sign {
            background: url(../images/wrong-bg.png) no-repeat center center / 316px 176px;
        }
        .my-answer {
            color: #fa0431;
        }
    }
    .sign {
        height: 168px;
        margin-top: -94px;
    }
    .name {
        text-align: center;
        font-size: 36px;
        font-weight: 500;
        margin-top: 38px;
    }
    .info {
        display: flex;
        justify-content: space-around;
        text-align: center;
        margin: 38px 0 0;
        .item {
            font-size: 28px;
            color: #6e6e6e;
            flex: 1;
            text-align: center;
        }
        .answer {
            color: #04a5ff;
        }
        .answer,.my-answer {
            font-size: 32px;
        }
    }
    .btn {
        width: 460px;
        margin: 30px auto 0;
        height: 80px;
        background: linear-gradient(135deg, #67cef8, #1e74fa);
        line-height: 80px;
        border-radius: 46px;
        font-size: 32px;
        text-align: center;
        color: #ffffff;
    }
}
.count-popup {
    /deep/ .dialog {
        width: 640px;
    }

    .wrap {
        background-color: #fff;
        border-radius: 20px;
        padding: 30px 50px 50px;
    }
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        .t {
            font-size: 36px;
            font-weight: bold;
        }
        .n {
            font-size: 28px;
            color: #6e6e6e;
        }
    }
    .line {
        height: 64px;
        border-radius: 32px;
        overflow: hidden;
        margin-top: 20px;
        position: relative;
        &::after {
            content: '';
            position: absolute;
            right: 14px;
            top: 8px;
            width: 48px;
            height: 48px;
        }
        .bg {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
        }
        &.right {
            background: #edf8ff;
            &::after {
                background: url(../images/right-icon.png) no-repeat center center / 100% 100%;
            }
            .bg {
                background: linear-gradient(270deg,#3f8eff, #26c3fd);
            }
        }
        &.wrong {
            background: #ebf0f8;
            &::after {
                background: url(../images/wrong-icon.png) no-repeat center center / 100% 100%;
            }
            .bg {
                background: #c5cedc;
            }
        }
        .txt {
            position: relative;
            padding-left: 26px;
            line-height: 64px;
            font-size: 28px;
            color: #ffffff;
            text-shadow: 0 2px 2px rgba(0,0,0,0.24);
        }
    }
    .btn {
        width: 460px;
        margin: 50px auto 0;
        height: 80px;
        background: linear-gradient(135deg, #67cef8, #1e74fa);
        line-height: 80px;
        border-radius: 46px;
        font-size: 32px;
        text-align: center;
        color: #ffffff;
    }
}
.overview-popup {
    /deep/ .dialog {
        width: 640px;
    }

    .wrap {
        height: 616px;
        background-color: #fff;
        border-radius: 20px;
        padding-top: 10px;
    }
    .title {
        text-align: center;
        padding-top: 24px;
        font-size: 36px;
        font-weight: 500;
    }
    .score {
        font-size: 36px;
        color: #04a5ff;
        line-height: 1;
        text-align: center;
        padding-left: 30px;
        padding-top: 24px;
        span {
            font-size: 100px;
            font-weight: bold;
        }
    }
    .tit {
        text-align: center;
        padding-top: 14px;
        font-size: 28px;
        font-weight: 500;
    }
    .info {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: #f4f9fd;
        border-radius: 16px;
        height: 166px;
        margin: 24px 38px 0;
        justify-content: space-evenly;
        .num {
            font-size: 40px;
            font-weight: 500;
            &.right {
                color: #04a5ff;
            }
            &.wrong {
                color: #fa0431;
            }
        }
        .name {
            color: #6e6e6e;
            font-size: 28px;
            margin-top: 10px;
        }
    }
    .btn {
        width: 460px;
        margin: 28px auto 0;
        height: 80px;
        background: linear-gradient(135deg, #67cef8, #1e74fa);
        line-height: 80px;
        border-radius: 46px;
        font-size: 32px;
        text-align: center;
        color: #ffffff;
    }
}
</style>