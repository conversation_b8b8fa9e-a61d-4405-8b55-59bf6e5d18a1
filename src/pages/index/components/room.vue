<template>
    <div class="room">
        <div class="shadow" :style="{width: width + 'px'}"></div>
        <div class="scroller" ref="scroller">
            <div class="comment-line" v-for="item in items" :key="item.id">
                <div v-if="item.type === 'systemMessage'" class="comment-item sys-msg">
                    <div>
                        <span>
                            {{ item.msg }}
                        </span>
                    </div>
                </div>
                <div v-else class="comment-item" :class="{vip: isVip(item.tags)}">
                    <div class="quote" v-if="item.quote">
                        <iconList :small="true" :tags="item.quote.tags" />
                        <span>
                            <span class="name" v-if="item.quote.name">{{
                                item.quote.name + ':'
                            }}</span>
                            {{ item.quote.msg }}
                        </span>
                    </div>
                    <div>
                        <iconList :tags="item.tags" />
                        <span>
                            <span class="name" v-if="item.name">{{ item.name + ':' }}</span>
                            {{ item.msg }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="enter-wrap">
                <div :key="currentUserEnterMessage.id">
                    <div v-if="currentUserEnterMessage.id" class="user-enter">
                        欢迎
                        <iconList :tags="currentUserEnterMessage.tags" />
                        <span>{{ currentUserEnterMessage.name }}</span>
                        加入课堂
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {throttle, find} from 'lodash-es'
import iconList from './iconList.vue'

export default {
    components: {iconList},
    data() {
        let width = document.documentElement.clientWidth
        return {
            scrollLock: false,
            $_scrollingToBottom: false,
            currentUserEnterMessage: {},
            isExpired: true,
            expiredTime: 2003,
            width,
        }
    },
    props: {
        items: Array,
        userEnterMessageList: Array,
    },
    watch: {
        items() {
            this.backBottom()
        },
        userEnterMessageList() {
            if (!this.currentUserEnterMessage.id && this.userEnterMessageList.length) {
                this.changeUserEnterMessage()
            }
        },
    },
    mounted() {
        let $el = this.$refs.scroller
        let fontSize = parseFloat(document.documentElement.style.fontSize)
        let oneLine = 0.6 * fontSize
        $el.addEventListener(
            'scroll',
            throttle(() => {
                let scrollTop = $el.scrollTop
                let scrollHeight = $el.scrollHeight
                let clientHeight = $el.clientHeight
                const k = scrollHeight - scrollTop - clientHeight
                let isBottom = k <= oneLine * 1.5
                this.scrollLock = !isBottom
            }, 50)
        )
    },
    methods: {
        changeUserEnterMessage() {
            if (this.isExpired) {
                let userEnterMessage = this.userEnterMessageList.shift()
                if (userEnterMessage) {
                    this.currentUserEnterMessage = userEnterMessage
                    this.isExpired = false
                    setTimeout(() => {
                        this.isExpired = true
                        this.changeUserEnterMessage()
                    }, this.expiredTime)
                } else {
                    this.currentUserEnterMessage = {}
                }
                this.backBottom()
            }
        },
        scrollToBottom() {
            if (this.$_scrollingToBottom) return
            this.$_scrollingToBottom = true
            const el = this.$refs.scroller
            const scrollHeight = el.scrollHeight
            this.$nextTick(() => {
                el.scrollTop = scrollHeight + 10000
                const cb = () => {
                    el.scrollTop = scrollHeight + 10000
                    requestAnimationFrame(() => {
                        el.scrollTop = scrollHeight + 10000
                        this.$_scrollingToBottom = false
                    })
                }
                // 双管齐下
                setTimeout(cb, 99)
                requestAnimationFrame(cb)
            })
        },
        backBottom() {
            if (!this.scrollLock) {
                this.scrollToBottom()
            }
        },
        isVip(tags) {
            let vip = find(tags, {kemu: 0})
            return !!vip && !vip.expire
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.room {
    height: 100%;
    color: #fff;
}
.scroller {
    height: 100%;
    padding-top: 10px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
        display: none;
    }
}
.portrait .shadow,
.landscape .shadow {
    position: absolute;
    top: -1px;
    left: 0;
    overflow: hidden;
    background: url(../../../assets/images/room-shadow.png) no-repeat;
    background-size: 100% 100%;
    height: 48px;
    z-index: 1;
}
.comment-line {
    margin: 8px 20px;
}

.user-enter {
    margin: 0 20px 8px;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 5px 30px 5px 20px;
    border-radius: 24px;
    .fontSizeWithElder(26px);
    line-height: 1.4;
    display: inline-block;
    vertical-align: middle;
    .fontSizeWithElder(26px);
    color: #fff;
    span {
        color: #f791e3;
    }
}
.comment-item {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 5px 30px 5px 20px;
    border-radius: 24px;
    .fontSizeWithElder(26px);
    line-height: 1.4;
    vertical-align: middle;
    word-break: break-all;
    &.sys-msg {
        color: #ff9399;
    }
    .quote {
        .fontSizeWithElder(22px);
        padding-left: 14px;
        color: rgba(255, 255, 255, 0.65);
        position: relative;
        &::after {
            content: '';
            position: absolute;
            width: 2px;
            left: 0;
            top: 4px;
            bottom: 8px;
            background: rgba(255, 255, 255, 0.45);
        }
        .name {
            color: rgba(255, 255, 255, 0.65) !important;
        }
    }
    img,
    .text {
        vertical-align: middle;
        box-sizing: border-box;
    }
    .name {
        color: #ec85d8;
    }

    &.vip {
        background: linear-gradient(
            315deg,
            rgba(250, 164, 95, 0.35) 0%,
            rgba(252, 220, 145, 0.35) 100%
        );
        .name {
            color: #ffe29c;
        }
    }
}

.vertical {
    .comment-item {
        background-color: rgba(0, 0, 0, 0.4);
        .name {
            color: #ffd3aa;
        }
        .vip {
            background: rgba(103, 69, 0, 0.7);
            color: #ffecbf;
            .name {
                color: #ffecbf;
            }
        }
    }
}
.landscape /deep/ {
    // landscape
    .comment-line {
        margin: 8px 15px;
    }
    .comment-item {
        background-color: transparent;
        .fontSizeWithElder(24px);
        padding: 0;
        &.vip {
            padding: 5px 10px;
        }
    }
    .shadow {
        width: 100%;
    }

    .user-enter {
        margin: 0 15px 8px;
    }
}
</style>
