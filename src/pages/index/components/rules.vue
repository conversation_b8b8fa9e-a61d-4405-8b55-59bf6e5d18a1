<template>
    <div class="rule-btn" @click="openRules">
        活动规则
        <popup class="rules-popup" :position="'bottom'" :show.sync="visible">
            <div
                class="wrap"
                @touchmove="touchmove"
                @touchstart="touchstart"
                @touchend="touchend"
                :style="{height: height + 'px'}"
                :class="{full: isTouchLock}"
            >
                <div class="header">活动规则</div>
                <div class="content scroll_view">
                    <div class="legal-image" v-html="roomDetail.activityRule"></div>
                </div>
            </div>
        </popup>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import popup from '../../../components/dialog'
import touchMixin from '../../../utils/touchMixin'

export default {
    mixins: [touchMixin],
    components: {popup},
    data() {
        return {
            visible: false,
        }
    },
    computed: {
        ...mapState(['roomDetail']),
        show() {
            return this.visible
        },
    },
    watch: {
        isTouchLock() {
            this.$EventBus.$emit('minimizePlayer', this.isTouchLock, {
                groupKey: '',
                goodsUniqueKey: '',
            })
        },
    },
    created() {
        this.$EventBus.$on('quitMinimize', () => {
            this.visible = false
        })
    },
    methods: {
        openRules() {
            this.$EventBus.$emit('setOrientation', 'portrait', () => {
                this.visible = true
            })
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.rules-popup {
    .wrap {
        background-color: #fff;
        display: flex;
        border-radius: 20px 20px 0 0;
        overflow: hidden;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
        padding-bottom: calc(env(safe-area-inset-bottom) - 40px);
        &.full {
            border-radius: 0;
            .scroll_view {
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }
        }
    }
    .header {
        font-weight: bold;
        .fontSizeWithElder(40px);
        text-align: center;
        padding: 40px 0 20px;
    }
    .content {
        margin-top: 18px;
        overflow: hidden;
        flex: 1;
        padding: 0 15px 20px 15px;
    }
}
.portrait {
    .rule-btn {
        position: absolute;
        right: 0;
        top: 5px;
        background: linear-gradient(114deg, rgba(205, 74, 115, 0.3) 8%, rgba(181, 45, 97, 0.3) 88%);
        padding: 10px 20px;
        border-radius: 200px 0px 0px 200px;
        font-size: 22px;
        color: rgba(255, 255, 255, 0.6);
        white-space: nowrap;
        z-index: 3;
    }
}
.vertical {
    .rule-btn {
        padding: 0 20px;
        line-height: 48px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 24px;
        font-size: 20px;
        color: rgba(255, 255, 255, 0.85);
        margin-left: 20px;
    }
}
</style>
