<template>
    <div class="vip-enter-wrap">
        <transition appear name="slide-fade" mode="out-in">
            <div :key="currentShowGradeMessage.id">
                <div v-if="currentShowGradeMessage.id" class="vip-user-enter">
                    恭喜
                    <iconList :tags="currentShowGradeMessage.tags" />
                    <span>{{ currentShowGradeMessage.name }}</span>
                    <span class="score"> {{ currentShowGradeMessage.score }}分</span>通过{{
                        getKemuText(currentShowGradeMessage.kemu)
                    }}考试
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import iconList from './iconList.vue'
export default {
    components: {iconList},
    data() {
        return {
            currentShowGradeMessage: {},
            isExpired: true,
            expiredTime: 5005,
        }
    },
    props: {
        showGradeMessageList: Array,
    },
    watch: {
        showGradeMessageList() {
            if (!this.currentShowGradeMessage.id && this.showGradeMessageList.length) {
                this.changeVipUserEnterMessage()
            }
        },
    },
    methods: {
        getKemuText(kemu) {
            switch (kemu) {
                case 1:
                    return '科目一'
                case 2:
                    return '科目二'
                case 3:
                    return '科目三'
                case 4:
                    return '科目四'
                default:
                    return ''
            }
        },
        changeVipUserEnterMessage() {
            if (this.isExpired) {
                let vipUserEnterMessage = this.showGradeMessageList.shift()
                if (vipUserEnterMessage) {
                    this.currentShowGradeMessage = vipUserEnterMessage
                    this.isExpired = false
                    setTimeout(() => {
                        this.isExpired = true
                        this.changeVipUserEnterMessage()
                    }, this.expiredTime)
                } else {
                    this.currentShowGradeMessage = {}
                }
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.slide-fade-enter-active {
    transition: all 0.3s ease;
}
.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter {
    transform: translateX(40px);
    opacity: 0;
}
.slide-fade-leave-to {
    transform: translateX(-40px);
    opacity: 0;
}
.vip-enter-wrap {
    pointer-events: none;
}
.vip-user-enter {
    margin: 16px 24px 0;
    padding: 10px 20px;
    background: linear-gradient(103deg, #ff3f17 0%, #f7751e 100%);
    border: 1px solid rgba(249, 171, 44, 1);
    border-radius: 28px;
    color: #fff;
    line-height: 1.2;
    display: inline-block;
    vertical-align: middle;
    .fontSizeWithElder(26px);
    color: #fff;
    .score {
        color: rgba(253, 249, 0, 1);
    }
}
</style>
