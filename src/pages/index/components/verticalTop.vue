<template>
    <div class="vertical-top" v-show="!isLandscape" :style="{'padding-top': statusBarHeight}">
        <div class="top-header" v-if="liveFrom === 'practice'">
            <div class="back" @click="back"></div>
            <div class="main">
                <div class="item" @click="goPractice('answer')">答题</div>
                <div class="item" @click="goPractice('remember')">背题</div>
                <div class="item active">
                    直播讲解
                    <i class="tag">NEW</i>
                </div>
            </div>
            <div class="seat"></div>
        </div>
        <div class="line" v-if="bizConfig.playStatus !== 3 && showTitle">
            <div class="title">
                <scrollText ref="scrollText" :text="roomDetail.title" />
            </div>
            <div class="viewer">
                {{ roomStatus.pv ? roomStatus.pv : '--' }}人观看中<i
                    @click="back"
                    v-if="liveFrom !== 'practice' && (isMucang || isWeixin)"
                    class="icon close"
                />
            </div>
        </div>
        <div class="line">
            <div class="and">
                <div class="image">
                    <img :src="roomDetail.teacherHeadImg" />
                </div>
                <div class="text">{{ roomDetail.teacherName }}</div>
            </div>
            <div class="mox" v-if="bizConfig.playStatus === 3 && isMucang">
                <i
                    @click="back"
                    class="icon close" />
            </div>
            <template v-else-if="isMucang">
                <div class="more" v-if="publicLive" @click="share">分享</div>
                <div class="more lg" v-else @click="more">
                    <i class="icon l" />更多课程<i class="icon a" />
                </div>
            </template>
            <div class="more" v-else-if="tidyLive && wxShareReady" @click="wxShare">分享</div>
        </div>
        <div
            class="line line-at mt10"
            v-if="!(liveFrom === 'practice' && roomDetail.orientation !== 1) && bizConfig.playStatus !== 3"
        >
            <div v-if="isMucang">
                <activityPack v-if="bizConfig.saleMode && false" />
                <div v-else>
                    <!-- 占位 -->
                </div>
                <div
                    class="line mt10"
                    v-if="roomDetail.orientation === 1 && roomDetail.activityRule && bizConfig.playStatus === 1"
                >
                    <rules />
                </div>
            </div>
            <div v-else>
                <!-- 占位 -->
            </div>
            <videoBanner
                fragmentName1="直播间横幅"
                v-if="(bizConfig.saleMode || bizConfig.stMode) && roomResource.bannerDetail.advertType"
                :advertDetail="roomResource.bannerDetail"
            />
        </div>
        <popup
            class="wx-mask"
            :position="'top'"
            :show.sync="wxMaskVisible"
            :closeOnBlank="true"
            :showClose="false"
        >
            <div class="content" @click="wxMaskVisible = false">点击右上角... 将本页分享给好友</div>
        </popup>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {MCProtocol} from '@simplex/simple-base'
import activityPack from './activityPack'
import popup from '../../../components/dialog'
import videoBanner from '../../../components/videoBanner.vue'
import scrollText from '../../../components/scrollText.vue'
import rules from './rules.vue'
import {
    isMucang,
    isAndroid,
    trackEvent,
    URLParams,
    webOpen,
    getUrl,
    isMajorApp,
    isWeixin,
} from '../../../utils/tools'
import {ROOM_TYPE} from '../../../utils/constant'
import {webClose} from '../../../utils/jump'

MCProtocol.register('jiakao-global.backToPractice', function(config) {
    return config
})

export default {
    components: {activityPack, videoBanner, scrollText, rules, popup},
    data() {
        return {
            isMucang,
            isWeixin,
            liveFrom: URLParams.liveFrom,
            statusBarHeight: '',
            showHeader: false,
            // 公共直播
            publicLive: false,
            paddingTop: '',
            // 带货直播
            tidyLive: false,
            wxShareReady: false,
            wxMaskVisible: false,
        }
    },
    props: {
        showTitle: Boolean,
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'roomStatus', 'isLandscape', 'roomResource']),
    },
    watch: {
        'roomDetail.roomType': {
            handler(val) {
                if (val === ROOM_TYPE.GGZB && !this.publicLive) {
                    setTimeout(() => {
                        this.initShare()
                        this.publicLive = true
                    }, 1000)
                } else if (val === ROOM_TYPE.ZBDH && !this.tidyLive) {
                    this.initWxShare()
                    this.tidyLive = true
                }
            },
            immediate: true,
        },
    },
    created() {
        MCProtocol.Core.Web.setStatusBarTheme({theme: 'light'})
        MCProtocol.Core.System.env(data => {
            let {statusBarHeight} = data.data
            if (statusBarHeight) {
                if (isAndroid) {
                    statusBarHeight += 6
                }
                statusBarHeight = statusBarHeight + 'px'
            } else {
                statusBarHeight = 'env(safe-area-inset-top)'
            }
            this.statusBarHeight = statusBarHeight
        })
    },
    mounted() {
        if (this.$refs.scrollText) {
            this.$refs.scrollText.startScroll()
        }
    },
    methods: {
        goPractice(tab) {
            MCProtocol['jiakao-global'].backToPractice({
                tab: tab,
            })
            webClose()
        },
        initShare() {
            MCProtocol.Core.Share.setting({
                channel: 'weixin_moment,weixin_friend,sina,qq',
                type: '',
                shareData: {
                    title: this.roomDetail.title,
                    description: '主播：' + this.roomDetail.teacherName,
                    // todo getUrl kemu
                    url: getUrl(
                        'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-zhibojian/pure-live.html',
                        {
                            id: URLParams.id,
                            carStyle: URLParams.carStyle,
                            kemuStyle: URLParams.kemuNum,
                        }
                    ),
                    iconUrl: this.roomDetail.teacherHeadImg,
                },
            })
        },
        initWxShare() {
            if (isWeixin) {
                window.wx.ready(() => {
                    const data = {
                        title: this.roomDetail.title,
                        desc: this.roomDetail.subtitle,
                        link: window.location.href,
                        imgUrl: this.roomDetail.teacherHeadImg,
                        success: () => {
                            this.wxShareReady = true
                        },
                    }
                    window.wx.updateAppMessageShareData(data)
                    window.wx.updateTimelineShareData(data)
                })
            }
        },
        share() {
            MCProtocol.Core.Web.menu()
        },
        back() {
            if (isWeixin) {
                window.WeixinJSBridge.call('closeWindow')
            } else {
                this.$EventBus.$emit('beforeClose')
            }
        },
        more() {
            if (URLParams.kemu === 'kemu3') {
                if (isMajorApp) {
                    webOpen({
                        url: `http://jiakao.nav.mucang.cn/famousTeacher?from=${URLParams.from}`,
                    })
                } else {
                    webOpen({
                        url: `https://laofuzi.kakamobi.com/jiakaobaodian-zhiboke/index.html?kemu=${URLParams.kemuNum}`,
                        // orientation不能没值，也不能传正确的值
                        orientation: '1',
                        titleBar: false,
                    })
                }
            } else {
                webOpen({
                    url: getUrl(
                        'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-must-learning/index.html',
                        {
                            from: URLParams.from,
                            fromItemCode: URLParams.id,
                            carStyle: URLParams.carStyle,
                            kemuStyle: URLParams.kemuNum,
                            sceneCode: URLParams.sceneCode,
                            patternCode: URLParams.patternCode,
                            orientation: 'portrait',
                        }
                    ),
                    // orientation不能没值，也不能传正确的值
                    // orientation: '1',
                    titleBar: true,
                    toolbar: true,
                    menu: false,
                    button: false,
                })
            }

            let actionName = '更多课程'
            let actionType = '点击'
            // 埋点梳理-驾考宝典-1112
            // 精品课直播间页_点击更多课程
            trackEvent({actionName, actionType})
        },
        wxShare() {
            this.wxMaskVisible = true
        },
    },
}
</script>
<style lang="less" scoped>
.vertical-top {
    padding-top: 10px;
    /deep/ .so-line {
        width: 260px;
        height: 76px;
        margin-right: 20px;
    }
}
.vertical {
    .vertical-top {
        position: absolute;
        width: 100%;
        z-index: 3;
    }
}
.top-header {
    box-sizing: content-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 16px 30px 16px;

    .back {
        width: 50px;
        height: 50px;
        background-image: url(../images/<EMAIL>);
        background-size: 50px 50px;
        background-repeat: no-repeat;
        background-position: center;
        box-sizing: content-box;
    }

    .main {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 12px;
        height: 60px;
        font-size: 28px;
        color: #ffffff;
        padding: 6px 5px;
        .item {
            width: 130px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            &.active {
                color: #333333;
                font-weight: 500;
                font-size: 26px;
                background: #ffffff;
                border-radius: 8px;
            }
        }
        .tag {
            position: absolute;
            right: -12px;
            top: -12px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 42px;
            height: 24px;
            background: #ff4c4c;
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
            white-space: nowrap;
        }
    }

    .seat {
        width: 50px;
        height: 50px;
    }
}
.line {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.line-at {
    align-items: flex-start;
}
.mt10 {
    margin-top: 10px;
}
.mt14 {
    margin-top: 14px;
}
.title {
    margin-left: 20px;
    font-size: 32px;
    color: #ffffff;
    text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    flex: 1;
    line-height: 70px;
    overflow: hidden;
}
.viewer {
    min-width: 220px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 70px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 13%, rgba(0, 0, 0, 0.4) 66%);
    border-radius: 36px;
    font-size: 22px;
    color: #ffffff;
    padding: 0 10px 0 50px;
    margin-right: 20px;
}
.and {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 46px;
    padding: 0 24px 0 8px;
    margin-left: 20px;
    .image {
        margin: 0 auto;
        width: 48px;
        height: 48px;
        border: 1px solid #fff;
        border-radius: 50%;
        overflow: hidden;
        img {
            width: 100%;
            display: block;
        }
    }
    .text {
        margin-left: 12px;
        color: #fff;
        font-size: 26px;
    }
}
.mox {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 36px;
    margin-right: 20px;
    height: 64px;
    width: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.icon.close {
    width: 50px;
    height: 50px;
    background-image: url(../images/close2.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 22px 20px;
}
.more {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 52px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 200px 0px 0px 200px;
    font-size: 26px;
    color: #ffffff;
    padding: 0 30px 0 14px;
    &.lg {
        width: 230px;
    }

    .icon.l {
        width: 36px;
        height: 36px;
        background-image: url(../images/live.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 20px 20px;
        background-color: #ff0745;
        border-radius: 50%;
    }
    .icon.a {
        width: 12px;
        height: 20px;
        background-image: url(../images/arrow.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 12px 20px;
    }
}
.wx-mask {
    /deep/ .dialog {
        background-image: url(../../../assets/images/wx-share-bg.png);
        background-position: right 46px top 12px;
        background-repeat: no-repeat;
        background-size: 180px auto;
    }
    .content {
        padding-top: 230px;
        padding-right: 40px;
        color: #fff;
        text-align: right;
        font-size: 30px;
    }
}
</style>
