<template>
    <div class="vip-enter-wrap">
        <transition appear name="slide-fade" mode="out-in">
            <div :key="currentVipUserEnterMessage.id">
                <div v-if="currentVipUserEnterMessage.id" class="vip-user-enter">
                    <iconList :tags="currentVipUserEnterMessage.tags" />
                    <span class="name">{{ currentVipUserEnterMessage.name }}</span>
                    进入直播间
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import iconList from './iconList.vue'
export default {
    components: {iconList},
    data() {
        return {
            currentVipUserEnterMessage: {},
            isExpired: true,
            expiredTime: 5005,
        }
    },
    props: {
        vipUserEnterMessageList: Array,
    },
    watch: {
        vipUserEnterMessageList() {
            if (!this.currentVipUserEnterMessage.id && this.vipUserEnterMessageList.length) {
                this.changeVipUserEnterMessage()
            }
        },
    },
    methods: {
        changeVipUserEnterMessage() {
            if (this.isExpired) {
                let vipUserEnterMessage = this.vipUserEnterMessageList.shift()
                if (vipUserEnterMessage) {
                    this.currentVipUserEnterMessage = vipUserEnterMessage
                    this.isExpired = false
                    setTimeout(() => {
                        this.isExpired = true
                        this.changeVipUserEnterMessage()
                    }, this.expiredTime)
                } else {
                    this.currentVipUserEnterMessage = {}
                }
            }
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../../assets/styles/variables';
.slide-fade-enter-active {
    transition: all 0.3s ease;
}
.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter {
    transform: translateX(40px);
    opacity: 0;
}
.slide-fade-leave-to {
    transform: translateX(-40px);
    opacity: 0;
}
.vip-enter-wrap {
    pointer-events: none;
}
.vip-user-enter {
    margin: 16px 24px 0;
    padding: 10px 20px;
    background: #c9376f;
    border-radius: 24px;
    color: #fff;
    line-height: 1.2;
    display: inline-block;
    vertical-align: middle;
    .fontSizeWithElder(26px);
    color: #fff;
    .name {
        color: #ec85d8;
    }
}
</style>
