<template>
    <div class="xrzx-icon" v-show="isShowXrzxIcon" @click="openXrzx">
        <newPeoplePopup
            v-if="kemu === 'kemu1' && carStyle === 'car'"
            :show.sync="xrzxPopupVisible"
        />
    </div>
</template>

<script>
import {trackEvent, URLParams} from '../../../utils/tools'
import newPeoplePopup from './newPeoplePopup.vue'

export default {
    components: {newPeoplePopup},
    data() {
        return {
            kemu: URLParams.kemu,
            carStyle: URLParams.carStyle,
            xrzxPopupVisible: false,
            isShowXrzxIcon: false,
        }
    },
    created() {
        this.$EventBus.$on('setXrzxIcon', val => {
            this.isShowXrzxIcon = val
        })
    },
    mounted() {},
    methods: {
        openXrzx() {
            this.xrzxPopupVisible = true

            let actionName = '新人专享图标'
            let actionType = '点击'

            // v8.17.0-埋点文档
            // 精品课直播间页_点击新人专享图标
            trackEvent({actionName, actionType})
        },
    },
}
</script>

<style lang="less" scoped>
.xrzx-icon {
    width: 76px;
    height: 76px;
    margin-left: 20px;
    background: url(../../../assets/images/<EMAIL>) no-repeat center;
    background-size: cover;
}
</style>
