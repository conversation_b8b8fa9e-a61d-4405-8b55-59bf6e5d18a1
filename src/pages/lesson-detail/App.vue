<template>
    <div class="detail-wrap">
        <div class="close-page" @click="webClose" v-if="!navLineVisible"></div>
        <div class="lesson-nav" ref="nav" v-if="navLineVisible">
            <div class="back" @click="webClose"></div>
            <div
                class="item"
                :class="{active: index === currentNavIndex}"
                :key="item.target"
                @click="getAnchor(item.target)"
                v-for="(item, index) in navList"
            >
                {{ item.name }}
            </div>
        </div>
        <div class="lesson-detail" v-if="detail.id" @scroll="pageScroll" ref="pageContent">
            <div class="header-img">
                <img :src="detail.goodsMasterImgUrl" />
            </div>
            <div class="price-info">
                <div class="top">
                    <div class="t1">直播间专属福利</div>
                    <!-- <div class="t2">{{ currentTeacher.name }}老师粉丝专享</div> -->
                </div>
                <div class="cont">
                    <div class="p1" v-if="detail.price">
                        ¥<span>{{ detail.price }}</span>
                    </div>
                    <div class="p3" v-else-if="detail.hasPermission">
                        免费
                    </div>
                    <div class="p2" v-if="detail.diffPrice">¥{{ detail.suggestedPrice }}</div>
                </div>
            </div>
            <div class="content push">
                <div class="title">{{ detail.title }}</div>
                <div class="tag-info">
                    <div class="item">
                        <div class="t c">180天</div>
                        <div class="g">有效期</div>
                    </div>
                    <div class="item">
                        <div class="t">已完结</div>
                        <div class="g">状态</div>
                    </div>
                    <div class="item">
                        <div class="t">随到随学</div>
                        <div class="g">学习方式</div>
                    </div>
                </div>
            </div>
            <div class="gap"></div>
            <div class="content full" id="lesson-list">
                <div class="title">课程目录</div>
                <div class="tab-list">
                    <div class="g">共{{ detail.topLessonCatelogDTOS.length }}节课</div>
                    <div class="list">
                        <div class="scroll_view">
                            <div
                                class="item"
                                @click="getLessonAnchor('sub-lesson-' + index)"
                                :key="lesson.id"
                                v-for="(lesson, index) in detail.topLessonCatelogDTOS"
                            >
                                第{{ toChineseNum(index + 1) }}课
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lesson-list">
                    <div
                        class="lesson"
                        :key="lesson.id"
                        v-for="(lesson, index) in detail.topLessonCatelogDTOS"
                    >
                        <div
                            class="th"
                            :id="'sub-lesson-' + index"
                            @click="toLessonForList(detail.id, lesson.id)"
                        >
                            <div class="sub-title">
                                <p class="txt">
                                    第{{ toChineseNum(index + 1) }}课 {{ lesson.title }}
                                </p>
                                <p class="tag">{{ detail.hasPermission ? '已' : '未' }}解锁</p>
                            </div>
                            <span class="time">{{ secToTime(lesson.videoDuration) }}</span>
                        </div>
                        <div class="key-list">
                            <div
                                class="td"
                                @click="toLessonForList(detail.id, lesson.id, keyTime.beginTime)"
                                :key="keyTime.knowlegdeIds"
                                v-for="(keyTime, index) in lesson.keyTimeLineDTOList"
                            >
                                <div class="txt">{{ '0' + (index + 1) }} {{ keyTime.title }}</div>
                                <span class="time">{{ secToTime(keyTime.beginTime) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gap"></div>
            <div class="content" id="teacher-info">
                <div class="title">讲师介绍</div>
                <div class="teacher-wrap">
                    <div class="teacher" :key="teacher.id" v-for="teacher in detail.teacherList">
                        <div class="info">
                            <div class="image"><img :src="teacher.avatar" /></div>
                            <div class="name">{{ teacher.name }}</div>
                        </div>
                        <div class="desc">
                            {{ teacher.desc }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="gap"></div>
            <div class="content" id="lesson-intro">
                <div class="title">课程介绍</div>
                <div class="rich-text" v-html="getHtml(detail.detail)"></div>
            </div>
        </div>
        <div class="loading" v-else>
            <loading bgColor="#666" />
        </div>
        <div v-if="detail.hasPermission" class="go-lesson">
            <div class="main">
                <button @click="toLessonForBottom(detail.id)">
                    去听课
                </button>
            </div>
        </div>
        <buyBtn
            v-else
            :detail="detail"
            :remainderText="remainderText"
            :showAgreement="false"
            @buy="
                () => {
                    // payForLesson('底部吸底按钮', true, true)
                    goPay()
                }
            "
        />
        <popup
            class="opening-model"
            :show.sync="openingVisible"
            :showClose="false"
            :closeOnBlank="false"
        >
            <!-- <canvas ref="poster"></canvas>
            <div class="content" :class="{bg: showOpeningBg}" v-if="showOpeningEndAni"> -->
            <div class="content bg">
                <div class="close" @click="goClose"></div>
                <div class="price">
                    ¥<span>{{ detail.diffPrice }}</span>
                </div>
                <div class="image">
                    <img :src="currentTeacher.avatar" />
                </div>
                <div class="name">{{ currentTeacher.name }}老师</div>
                <div class="sol">{{ detail.redPacketText }}</div>
                <div class="btn" @click="goUse">立即使用</div>
                <!-- <div class="time">{{ remainderText }} 过期</div> -->
            </div>
        </popup>
        <popup class="buy-model" position="bottom" :show.sync="buyVisible" :showClose="false">
            <buyBtn
                :detail="detail"
                :remainderText="remainderText"
                @buy="
                    () => {
                        if (isDetainmentPop) {
                            payForLesson('支付挽留弹窗', false, false)
                        } else {
                            payForLesson('支付弹窗', false, false)
                        }
                    }
                "
            >
                <div class="price-wrap">
                    <div class="line">
                        <div class="name">课程价格</div>
                        <div class="price">
                            ¥{{ detail.diffPrice ? detail.suggestedPrice : detail.price }}
                        </div>
                    </div>
                    <div class="line" v-if="detail.diffPrice">
                        <div class="name">红包</div>
                        <div class="fow">
                            <div class="z">红包</div>
                            <div>优惠{{ detail.diffPrice }}元</div>
                        </div>
                    </div>
                    <div class="divide" v-if="detail.diffPrice || isAndroid"></div>
                    <div class="line" v-if="detail.diffPrice">
                        <div class="name g">优惠说明</div>
                        <div class="total">
                            <div>
                                <span class="p1">共优惠</span>
                                <span class="p2">¥{{ detail.diffPrice }}</span>
                            </div>
                            <div>
                                <span class="p3">小计¥</span>
                                <span class="p4">{{ detail.price }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="line" v-if="isAndroid">
                        <div class="name">支付方式</div>
                        <payList direction="hor" theme="hor2" />
                    </div>
                </div>
            </buyBtn>
        </popup>
        <popup
            class="confirm-model"
            :show.sync="confirmVisible"
            :showClose="false"
            :closeOnBlank="false"
        >
            <div class="content">
                <div class="time-down" v-if="detail.diffPrice && remainderText !== '00:00:00:00'">
                    <span class="text">优惠倒计时</span>
                    <p class="time" v-html="remainderTextHtml"></p>
                </div>
                <div class="desc">购买后可解锁完整课程</div>
                <div class="btns">
                    <div class="cancel" @click="closeConfirm">我再想想</div>
                    <div class="confirm" @click="confirm">
                        立即抢购
                    </div>
                </div>
            </div>
        </popup>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {find, throttle} from 'lodash-es'
import {getDetailAndCatelog} from '../../server/topLesson'
import popup from '../../components/dialog'
import payList from '../../components/payList'
import loading from '../../components/loading'
import buyBtn from './components/buyBtn'
import {webClose} from '../../utils/jump'
import {payLessonGroup, getPayAfterStrategy} from '../../utils/payHelper'
import {
    // getAuthToken,
    trackEvent,
    webOpen,
    isIOS,
    isAndroid,
    isTablet,
    URLParams,
    secToTime,
    toChineseNum,
    // loadScript,
    convertPixelFromUI,
    setEmbeddedHeight,
    trackPageShow,
    formatRemainTime,
} from '../../utils/tools'
// import redFile from './images/red.pag'
// loadImage
// https://github.com/libpag/pag-web/blob/main/pages/replace-image.html

function getRandom(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min)
}

let localDataKey = 'jiaokaobaodian-lesson-popup-red-popup'

// loadScript('./static/libpag.min.js').then(async () => {
//     console.time('PAG done')
//     try {
//         window.pagInit = await window.libpag.PAGInit({
//             locateFile: function(file) {
//                 return 'https://core.luban.assets.interceptor.mucang.cn?file=pag/libpag.wasm'
//             },
//         })
//         console.timeEnd('PAG done')
//         console.log('PAG locateFile')
//     } catch (error) {
//         window.pagInit = await window.libpag.PAGInit()
//         console.timeEnd('PAG done')
//         console.log('PAG remoteFile')
//     }
//     // window.pagInit = await window.libpag.PAGInit()
//     // console.timeEnd('PAG done')
//     // console.log('PAG remoteFile')
// })

let pageName = '精品课课程售卖页'
export default {
    components: {popup, payList, loading, buyBtn},
    data() {
        return {
            openingVisible: false,
            buyVisible: false,
            confirmVisible: false,
            showOpeningEndAni: false,
            showOpeningBg: false,
            remainderText: '00:00:00:00',
            detail: {},
            isAndroid,
            currentNavIndex: 0,
            navLineVisible: false,
            scrollDom: null,
            navList: [
                {
                    name: '课程目录',
                    target: 'lesson-list',
                },
                {
                    name: '讲师介绍',
                    target: 'teacher-info',
                },
                {
                    name: '课程介绍',
                    target: 'lesson-intro',
                },
            ],
            isDetainmentPop: false,
        }
    },
    computed: {
        ...mapGetters(['payList', 'checkdPayType']),
        currentTeacher() {
            if (this.detail.teacherList && this.detail.teacherList.length) {
                let len = this.detail.teacherList.length
                return this.detail.teacherList[getRandom(0, len - 1)]
            } else {
                return {}
            }
        },
        remainderTextHtml() {
            let t = this.remainderText.replace(/\d+/g, '<span>$&</span>').replace(/:/g, '<i>$&</i>')
            return t
        },
    },
    watch: {
        openingVisible(val) {
            if (val) {
                this.confirmVisible = false
                this.buyVisible = false
            }
        },
        buyVisible(val) {
            if (val) {
                let fragmentName1 = '支付弹窗'
                if (this.isDetainmentPop) {
                    fragmentName1 = '支付挽留弹窗'
                }
                let actionType = '出现'

                // 精品课课程售卖页_支付弹窗_出现
                // 精品课课程售卖页_支付挽留弹窗_出现
                trackEvent({fragmentName1, actionType, lessonGroupId: URLParams.lessonGroupId})
            }
        },
    },
    created() {
        document.title = '直播间'
        stat.setPageName(pageName)
        setEmbeddedHeight(isTablet ? 768 : 568)
    },
    async mounted() {
        // 精品课课程售卖页_展示
        trackPageShow({
            actionType: '展示',
            lessonGroupId: URLParams.lessonGroupId,
        })
        let resData = await getDetailAndCatelog({
            topLessonId: URLParams.lessonGroupId,
        })
        if (resData.suggestedPrice && resData.suggestedPrice > resData.price) {
            resData.diffPrice = resData.suggestedPrice - resData.price
        }
        this.detail = resData

        if (resData.price === 0 && !resData.hasPermission) {
            resData.hasPermission = true
        }

        if (resData.diffPrice && !resData.hasPermission) {
            this.initRed()
        }
        this.$nextTick(() => {
            let i = 0
            let timer = setInterval(() => {
                if (i >= 2) {
                    clearInterval(timer)
                    return
                }
                this.update()
                i++
            }, 1000)
            this.update()
            this.scrollDom = this.$refs['pageContent']
        })
    },
    methods: {
        webClose,
        secToTime,
        toChineseNum,
        getHtml(str) {
            return str.match(/<body>[\s\S]*?<\/body>/)[0]
        },
        initRed() {
            this.openingVisible = false
            // let i = 0
            // let timer = setInterval(async () => {
            //     if (window.pagInit || i === 3) {
            //         this.startTimer()
            //         clearInterval(timer)
            //     }
            //     i++
            // }, 666)
            let {end, expired} = this.calcTime()
            if (expired) {
                this.showRedPopup()
            } else {
                this.startTimer(end)
            }
        },
        async showRedPopup() {
            this.openingVisible = true
            // this.showOpeningEndAni = false
            // this.showOpeningBg = false
            // this.$nextTick(async () => {
            //     try {
            //         const buffer = await fetch(redFile).then(response => response.arrayBuffer())
            //         const pagFile = await window.pagInit.PAGFile.load(buffer)
            //         var $poster = this.$refs.poster
            //         const pagView = await window.pagInit.PAGView.init(pagFile, $poster)
            //         pagView.addListener('onAnimationUpdate', e => {
            //             if (e.currentFrame > 20 && !this.showOpeningEndAni) {
            //                 this.showOpeningEndAni = true
            //             }
            //         })
            //         pagView.play()
            //     } catch (error) {
            //         this.showOpeningEndAni = true
            //         this.showOpeningBg = true
            //     }
            // })

            let fragmentName1 = '红包'
            let actionType = '出现'

            // 精品课课程售卖页_红包_出现
            trackEvent({fragmentName1, actionType, lessonGroupId: URLParams.lessonGroupId})
        },
        goClose() {
            this.goPay()

            let {end} = this.calcTime()
            this.startTimer(end)
            let fragmentName1 = '红包'
            let actionType = '点击'
            let actionName = '关闭'

            // 精品课课程售卖页_红包_点击关闭
            trackEvent({fragmentName1, actionType, actionName, lessonGroupId: URLParams.lessonGroupId})
        },
        goUse() {
            this.goPay()

            let {end} = this.calcTime()
            this.startTimer(end)
            let fragmentName1 = '红包'
            let actionType = '点击'
            let actionName = '立即使用'

            // 精品课课程售卖页_红包_点击立即使用
            trackEvent({fragmentName1, actionType, actionName, lessonGroupId: URLParams.lessonGroupId})
        },
        goPay() {
            this.openingVisible = false
            this.isDetainmentPop = false
            this.buyVisible = true
        },
        calcTime() {
            let date = +new Date()
            let expired = true
            let seconds = 5 * 60
            let end = date + seconds * 1000
            let actObj = localStorage.getItem(localDataKey)
            actObj = JSON.parse(actObj || '{}')
            actObj[URLParams.lessonGroupId] = actObj[URLParams.lessonGroupId] || {}
            let item = actObj[URLParams.lessonGroupId]
            if (item.endTime > date) {
                end = item.endTime
                expired = false
            }
            return {end, expired}
        },
        startTimer(end) {
            let actObj = localStorage.getItem(localDataKey)
            actObj = JSON.parse(actObj || '{}')
            actObj[URLParams.lessonGroupId] = {
                endTime: end,
            }
            localStorage.setItem(localDataKey, JSON.stringify(actObj))
            let date = +new Date()
            let timer = setInterval(() => {
                date = +new Date()
                let remainder = end - date
                this.remainderText = formatRemainTime(remainder, 'hh:mm:ss:SS')
                if (remainder <= 0) {
                    clearInterval(timer)
                    this.openingVisible = false
                    this.$nextTick(() => {
                        this.showRedPopup()
                    })
                }
            }, 45)
        },
        toLessonForBottom(lessonGroupId, subLessonId, seekTo) {
            this.toLesson(lessonGroupId, subLessonId, seekTo)
            let actionType = '点击'
            let actionName = '去听课'

            // 精品课课程售卖页_点击去听课
            trackEvent({actionType, actionName, lessonGroupId: URLParams.lessonGroupId})
        },
        toLessonForList(lessonGroupId, subLessonId, seekTo) {
            this.toLesson(lessonGroupId, subLessonId, seekTo)
            let actionType = '点击'
            let actionName = '课程目录'

            // 精品课课程售卖页_点击课程目录
            trackEvent({actionType, actionName, lessonGroupId: URLParams.lessonGroupId})
        },
        toLesson(lessonGroupId, subLessonId, seekTo) {
            if (!this.detail.hasPermission) {
                this.confirmVisible = true

                let fragmentName1 = '解锁引导弹窗'
                let actionType = '出现'

                // 精品课课程售卖页_解锁引导弹窗_出现
                trackEvent({fragmentName1, actionType, lessonGroupId: URLParams.lessonGroupId})
                return
            }

            let url
            url = `http://jiakao.nav.mucang.cn/topLesson/detail?from=200&id=${lessonGroupId}`
            if (subLessonId) {
                url += `&subLessonId=${subLessonId}`

                if (seekTo) {
                    url += `&seekTo=${seekTo}`
                }
            }
            // if (subLessonId) {
            //     url = `http://jiakao.nav.mucang.cn/topLesson/play?from=200&id=${subLessonId}`
            //     if (seekTo) {
            //         url += `&seekTo=${seekTo}`
            //     }
            // } else {
            //     url = `http://jiakao.nav.mucang.cn/topLesson/detail?from=200&id=${lessonGroupId}`
            // }
            webOpen({
                url,
            })
        },
        pageScroll: throttle(function(e) {
            const scrollTop = e.target.scrollTop

            if (this.navList[0].top && scrollTop > this.navList[0].top) {
                this.navLineVisible = true

                let len = this.navList.length
                let index = 0
                for (let i = len - 1; i > 0; i--) {
                    if (this.navList[i].top <= scrollTop) {
                        index = i
                        break
                    }
                }
                this.currentNavIndex = index
            } else {
                this.navLineVisible = false
            }
        }, 16),
        scrollTo(dom, start, end, speed, dir) {
            let top = start + speed

            if (dir) {
                top = Math.max(top, end)
            } else {
                top = Math.min(top, end)
            }
            dom.scrollTop = top

            if (top !== end) {
                requestAnimationFrame(this.scrollTo.bind(this, dom, top, end, speed, dir))
            }
        },
        getTop(target) {
            let dom = document.getElementById(target)
            let top = dom.offsetTop - convertPixelFromUI(88)
            top = parseInt(top)
            return top
        },
        update() {
            this.navList.forEach((item, index) => {
                let top = this.getTop(item.target)
                this.navList[index].top = top
            })
            console.log(this.navList.map(item => item.top))
        },
        getAnchor(target, ani) {
            let nav = find(this.navList, {target})
            let top = nav.top + 1
            if (ani) {
                let scrollTop = this.scrollDom.scrollTop
                let speed = (top - scrollTop) / 30
                requestAnimationFrame(
                    this.scrollTo.bind(this, this.scrollDom, scrollTop, top, speed, scrollTop > top)
                )
            } else {
                this.scrollDom.scrollTop = top
            }
        },
        getLessonAnchor(target) {
            let top = this.getTop(target)
            this.scrollDom.scrollTop = top
        },
        payForLesson(fragmentName1, directly, detainment) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else if (directly) {
                let pay = find(this.payList, {type: 2})
                if (pay) {
                    payType = 2
                    payChannel = 'weixin_mobile'
                } else {
                    payType = 1
                    payChannel = 'alipay_mobile'
                }
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }
            payLessonGroup(
                {
                    payType,
                    payChannel,
                    lessonGroupId: URLParams.lessonGroupId,
                    appleId: this.detail.applePriceId,
                    squirrelGoodsInfo: this.detail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    extraInfo: JSON.stringify({
                        lessonId: URLParams.lessonGroupId,
                    }),
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    // pageData: {
                    //     lessonId: URLParams.lessonId,
                    // },
                },
                getPayAfterStrategy(true, 'lesson'),
                null,
                () => {
                    if (detainment && !this.openingVisible) {
                        this.isDetainmentPop = true
                        this.buyVisible = true
                    }
                }
            )

            let actionType = '点击'
            let actionName = '确认支付'
            if (directly) {
                actionName = '去支付'
            }

            // 精品课课程售卖页_底部吸底按钮_点击去支付
            // 精品课课程售卖页_支付挽留弹窗_点击确认支付
            // 精品课课程售卖页_支付弹窗_点击确认支付
            // 精品课课程售卖页_解锁引导弹窗_点击去支付
            trackEvent({fragmentName1, actionType, actionName, lessonGroupId: URLParams.lessonGroupId})
        },
        closeConfirm() {
            this.confirmVisible = false

            let fragmentName1 = '解锁引导弹窗'
            let actionType = '点击'
            let actionName = '我再想想'

            // 精品课课程售卖页_解锁引导弹窗_点击我再想想
            trackEvent({fragmentName1, actionType, actionName, lessonGroupId: URLParams.lessonGroupId})
        },
        confirm() {
            // this.payForLesson('解锁引导弹窗', true, true)
            this.goPay()
            this.confirmVisible = false
        },
    },
}
</script>

<style lang="less">
@keyframes dodge {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}
@keyframes redPackage {
    from {
        transform: translateZ(1000px) rotateY(180deg) scale(0);
    }

    to {
        transform: translateZ(1000px) rotateY(360deg) scale(1);
    }
}

@keyframes btnScale {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}
</style>
<style lang="less" scoped>
.detail-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    .close-page {
        position: absolute;
        right: 28px;
        top: 32px;
        width: 50px;
        height: 50px;
        background: url(../../assets/images/<EMAIL>) no-repeat center center
            rgba(0, 0, 0, 0.45);
        border-radius: 100%;
        background-size: 40px 40px;
        z-index: 2;
    }
    .loading {
        flex: 1;
        position: relative;
    }
}
.lesson-detail {
    overflow-y: auto;
    .content {
        padding: 20px 30px 0;
        background: #fff;
        &.push {
            border-radius: 24px 24px 0px 0px;
            margin-top: -20px;
            padding-top: 40px;
            position: relative;
        }
        &.full {
            padding: 20px 0 0;
            .title {
                padding-left: 30px;
            }
        }
    }
    .title {
        color: #333333;
        font-size: 36px;
        font-weight: bold;

        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .gap {
        height: 16px;
        background: #f5f7fa;
    }
}
.header-img {
    min-height: 58px;
    img {
        width: 100%;
    }
}
.lesson-nav {
    display: flex;
    z-index: 1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 88px;
    align-items: center;
    background: #fff;
    color: #a0a0a0;
    .back {
        margin-left: 10px;
        width: 70px;
        height: 88px;
        background: url(../../assets/images/<EMAIL>) no-repeat 20px center / 50px auto;
    }
    .item {
        text-align: center;
        font-size: 30px;
        line-height: 88px;
        flex: 1;
        color: #464646;
    }
    .active {
        color: #333;
        position: relative;
        font-weight: bold;
        font-size: 34px;
        &::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            width: 40px;
            margin-left: -20px;
            height: 6px;
            border-radius: 4px;
            font-size: 0;
            background: #333;
        }
    }
}
.price-info {
    background: url(./images/price-bg.png) no-repeat center center / 100% auto;
    height: 200px;
    padding: 50px 0 0 30px;
    margin-top: -58px;
    position: relative;
    .top {
        display: flex;
    }
    .cont {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    .t1 {
        width: 212px;
        height: 32px;
        text-indent: -99999px;
        background: url(./images/text1.png) no-repeat left top / 100% auto;
    }
    .t2 {
        height: 32px;
        font-size: 22px;
        color: rgba(255, 235, 191, 0.9);
        background: rgba(227, 55, 45, 0.6);
        border-radius: 18px;
        padding: 0 16px;
        margin-left: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .p1 {
        color: #fff;
        font-size: 26px;
        font-weight: bold;
        font-family: PingFangSC, PingFangSC-Medium;
        span {
            font-size: 50px;
        }
    }
    .p2 {
        font-family: PingFangSC, PingFangSC-Medium;
        color: rgba(255, 255, 255, 0.9);
        font-size: 20px;
        padding-top: 10px;
        margin-left: 15px;
        text-decoration: line-through;
    }
    .p3 {
        color: #fff;
        font-size: 36px;
        font-weight: bold;
        padding-top: 10px;
    }
}
.tag-info {
    display: flex;
    background: #fff;
    padding: 20px 0;
    .item {
        flex: 1;
        text-align: center;
        position: relative;
        &:not(:first-child):after {
            content: '';
            position: absolute;
            left: 0;
            top: 16px;
            height: 64px;
            border-right: 1px solid #f5f7fa;
        }
        .t {
            font-size: 28px;
            font-weight: bold;
            color: #333333;
            &.c {
                color: #04a5ff;
            }
        }
        .g {
            font-size: 26px;
            color: #a0a0a0;
        }
    }
}
.tab-list {
    display: flex;
    align-items: center;
    padding: 0 30px;
    .g {
        font-size: 24px;
        color: #6e6e6e;
    }
    .list {
        flex: 1;
        overflow-x: auto;
        .scroll_view {
            display: flex;
            margin: 10px 0 0 18px;
            .item {
                padding: 0 30px;
                flex-shrink: 0;
                background: #f9f9f9;
                border-radius: 30px;
                height: 58px;
                line-height: 58px;
                margin-right: 14px;
                &.c {
                    color: #04a5ff;
                    background: rgba(4, 165, 255, 0.08);
                }
            }
        }
    }
}
.lesson-list {
    margin-top: 20px;
}
.lesson {
    .th {
        color: #333333;
        background: #f9f9f9;
        height: 128px;
        padding: 20px 30px 0;
        .sub-title {
            display: flex;
            align-items: center;
            .txt {
                font-size: 32px;
                font-weight: bold;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .tag {
                color: #785136;
                font-size: 22px;
                background: #f9dfc0;
                border-radius: 6px;
                height: 38px;
                padding: 0 10px;
                display: flex;
                align-items: center;
                margin-left: 10px;
                white-space: nowrap;
            }
        }
        .time {
            font-size: 24px;
            color: #605d60;
        }
    }
    .key-list {
        padding: 0 30px;
        .td:not(:first-child) {
            border-top: 1px solid #f2f2f2;
        }
    }
    .td {
        color: #333333;
        height: 144px;
        padding: 30px 0 0;
        .txt {
            font-size: 30px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .time {
            font-size: 24px;
            color: #605d60;
        }
    }
}
.teacher-wrap {
    padding-bottom: 40px;
    .teacher {
        background: #f9f9f9;
        border-radius: 16px;
        margin-top: 20px;
    }
    .info {
        display: flex;
        align-items: center;
        padding-left: 30px;
        height: 144px;
        .image {
            width: 96px;
            height: 96px;
            border-radius: 100%;
            overflow: hidden;
        }
        .name {
            margin-left: 20px;
            font-size: 34px;
            color: #333330;
        }
    }
    .desc {
        border-top: 1px solid #dfe8ee;
        font-size: 28px;
        color: #666666;
        margin: 0 30px;
        padding: 18px 0 30px;
    }
}
.rich-text {
    margin: 20px 0;
}

.go-lesson {
    position: relative;
    background: #ffffff;
    border-top: 1px solid #e8e8e8;
    padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
    padding-bottom: calc(env(safe-area-inset-bottom) - 20px);

    .main {
        margin-top: 24px;
        padding: 0 30px 24px;
        position: relative;
        button {
            font-size: 28px;
            width: 100%;
            height: 88px;
            background: linear-gradient(135deg, #ff751d 6%, #ed3211 88%, #ed3211 100%);
            border-radius: 444px;
            color: #fff;
            border: none;
            text-align: center;
            font-weight: 500;
            &.disabled {
                background: #999;
            }
            .price {
                display: block;
                line-height: 1.2;
            }
        }
    }
}

.opening-model {
    width: 100%;
    height: 100%;

    /deep/ .dialog {
        height: 750px;
        width: 100%;
    }
    .close {
        position: absolute;
        bottom: -50px;
        left: 50%;
        transform: translateX(-50%);
        width: 66px;
        height: 66px;
        background: url(../../assets/images/close4.png) no-repeat center center;
        background-size: 66px 66px;
    }
    canvas {
        width: 100%;
        height: 750px;
    }
    .content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 750px;
        text-align: center;
        // animation: dodge 0.2s ease-out;
        animation: redPackage 0.5s linear;
        &.bg {
            background: url(./images/red-bg.png) no-repeat center center / 100% auto;
        }
    }
    .image {
        border: 6px solid #ffe7b9;
        box-shadow: 0px 4px 8px 0px #f21b1e;
        width: 104px;
        height: 104px;
        border-radius: 100%;
        overflow: hidden;
        background: #fff;
        margin: 0 auto;
    }

    .price {
        color: #ef3131;
        font-size: 72px;
        font-weight: bold;
        margin: 58px 0 0;
        font-family: PingFangSC, PingFangSC-Medium;
        line-height: 1.4;
        span {
            font-size: 180px;
        }
    }
    .name {
        color: #ffcb9e;
        font-size: 28px;
        margin-top: 6px;
    }
    .sol {
        color: #ffcb9e;
        font-size: 40px;
    }
    .btn {
        background: linear-gradient(270deg, #ffdab7, #ffead6);
        border-radius: 48px;
        box-shadow: 0px 4px 12px 0px #c10b0d, 2px 2px 6px 0px #ffdfc1 inset;
        width: 480px;
        height: 96px;
        line-height: 96px;
        text-align: center;
        font-size: 40px;
        font-weight: bold;
        color: #88110b;
        margin: 20px auto 0;
        animation: btnScale 0.5s infinite;
    }
    .time {
        color: #ffcb9e;
        font-size: 22px;
        margin-top: 10px;
    }
}
.buy-model {
    .buy-bottom {
        border-radius: 20px 20px 0px 0px;
        border-top: none;
    }
    .price-wrap {
        border-bottom: 1px solid #e8e8e8;
        padding-top: 10px;
    }
    .divide {
        margin: 0 40px;
        border-top: 1px solid #e8e8e8;
    }
    .line {
        display: flex;
        align-items: center;
        padding: 0 40px;
        height: 90px;
        justify-content: space-between;
        .name {
            width: 142px;
            font-size: 30px;
            color: #333;
            &.g {
                color: #a0a0a0;
            }
        }
        .fow {
            height: 38px;
            display: flex;
            align-items: center;
            background: #ffffff;
            border: 1px solid #ffc8d0;
            border-radius: 4px;
            overflow: hidden;
            font-size: 24px;
            color: #fc0639;
            > div {
                padding: 0 10px;
            }
            .z {
                background: #feeff1;
            }
        }
        .total {
            color: #333;
            display: flex;
            align-items: center;
            > div {
                display: flex;
                align-items: center;
                margin-left: 20px;
            }
            .p1 {
                font-size: 28px;
            }
            .p2 {
                margin-left: 8px;
                font-size: 32px;
                color: #ff822d;
            }
            .p3 {
                font-size: 28px;
            }
            .p4 {
                font-size: 44px;
            }
        }
    }
    .price {
        font-weight: bold;
        font-size: 30px;
        color: #333;
    }
}
.confirm-model {
    /deep/ .dialog {
        width: 580px;
    }
    .content {
        background-color: #fff;
        border-radius: 20px;
        padding: 0 0 60px;
        text-align: center;
        overflow: hidden;
    }
    .time-down {
        height: 70px;
        background: #ffe6d9;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px 20px 0 0;
        position: relative;
        font-size: 28px;
        color: #f45217;
        .time {
            margin-left: 10px;
            width: 180px;
            display: flex;
            align-items: center;
        }
    }
    .desc {
        font-size: 30px;
        margin-top: 30px;
    }
    .btns {
        display: flex;
        padding: 0 10px;
        justify-content: space-around;
        margin-top: 32px;
        .cancel {
            flex: 1;
            margin: 0 20px;
            height: 80px;
            border: 1px solid #a0a0a0;
            border-radius: 46px;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
            color: #333;
            line-height: 80px;
        }
        .confirm {
            flex: 1;
            margin: 0 20px;
            height: 80px;
            background: linear-gradient(135deg, #ff751d 6%, #ed3211 88%, #ed3211 100%);
            line-height: 80px;
            border-radius: 46px;
            font-size: 32px;
            text-align: center;
            color: #ffffff;
        }
    }
}
</style>
