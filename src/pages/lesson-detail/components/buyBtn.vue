<template>
    <div class="buy-bottom">
        <div class="time-down" v-if="detail.diffPrice && remainderText !== '00:00:00:00'">
            <span class="icon"></span>
            <span class="text">优惠倒计时</span>
            <p class="time" v-html="remainderTextHtml"></p>
        </div>
        <slot></slot>
        <div class="main">
            <button @click="buy" :class="detail.price ? '' : 'disabled'">
                ¥{{ detail.price }} 确认协议并支付
            </button>
        </div>
        <vipAgreement theme="brown" :showAgreement="showAgreement"></vipAgreement>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import vipAgreement from '../../../components/vipAgreement'
export default {
    components: {vipAgreement},
    data() {
        return {}
    },
    props: {
        detail: Object,
        remainderText: String,
        showAgreement: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        ...mapGetters(['checkAgreement', 'readed']),
        remainderTextHtml() {
            let t = this.remainderText.replace(/\d+/g, '<span>$&</span>').replace(/:/g, '<i>$&</i>')
            return t
        },
    },
    methods: {
        async buy() {
            if (this.checkAgreement && !this.readed && this.showAgreement) {
                await this.$confirmProtocol()
            }
            this.$emit('buy')
        },
    },
}
</script>
<style lang="less" scoped>
.buy-bottom {
    position: relative;
    background: #ffffff;
    border-top: 1px solid #e8e8e8;
    padding-bottom: calc(constant(safe-area-inset-bottom) - 40px);
    padding-bottom: calc(env(safe-area-inset-bottom) - 40px);

    .time-down {
        position: absolute;
        left: 116px;
        top: -106px;
        width: 518px;
        height: 72px;
        background: #fef0e9;
        display: flex;
        align-items: center;
        padding-left: 98px;
        border-radius: 15px;
        &:after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            width: 20px;
            height: 6px;
            background: url(../images/angle.png) no-repeat left top / 100% auto;
            // width: 0;
            // height: 0;
            // transform: translateX(-50%);
            // border-top: 10px solid #fef0e9;
            // border-right: 10px solid transparent;
            // border-left: 10px solid transparent;
        }
        .icon {
            position: absolute;
            left: 16px;
            top: -16px;
            width: 70px;
            height: 82px;
            background: url(../images/red-icon.png) no-repeat left top / 100% auto;
        }
        .text {
            text-indent: -99999px;
            width: 130px;
            height: 28px;
            background: url(../images/text2.png) no-repeat left top / 100% auto;
        }
        .time {
            font-size: 30px;
            color: #f45217;
            display: flex;
            align-items: center;
            margin-left: 12px;
            /deep/ span {
                width: 50px;
                height: 42px;
                background: linear-gradient(180deg, #ff5024 25%, #ff8b56);
                color: #fff;
                border-radius: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            /deep/ i {
                padding: 0 4px;
            }
        }
    }

    .main {
        margin-top: 24px;
        padding: 0 30px;
        position: relative;
        button {
            font-size: 28px;
            width: 100%;
            height: 88px;
            background: linear-gradient(135deg, #ff751d 6%, #ed3211 88%, #ed3211 100%);
            border-radius: 444px;
            color: #fff;
            border: none;
            text-align: center;
            &.disabled {
                background: #999;
            }
            .price {
                display: block;
                line-height: 1.2;
            }
        }
    }
    /deep/ .agreement {
        padding: 10px 30px 20px;
        font-size: 24px;
    }
}
</style>
