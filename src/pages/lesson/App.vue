<template>
    <div class="lesson-page">
        <div class="lesson-home">
            <div @click="close" class="close"></div>
            <div class="scroll_view" v-if="lessonDetail.id">
                <div class="header">
                    <div class="title">购买</div>
                </div>
                <div class="content">
                    <div
                        class="goods-item"
                        @click="changeGoods('lesson')"
                        :class="{
                            active: selectGoods === 'lesson',
                        }"
                    >
                        <div class="cvh">
                            <div class="name">{{ lessonDetail.title }}</div>
                            <div class="price">
                                ¥ <span>{{ lessonDetail.price }}</span>
                            </div>
                        </div>
                    </div>
                    <div
                        class="goods-item"
                        @click="changeGoods('vip')"
                        :class="{
                            active: selectGoods === 'vip',
                        }"
                        v-if="showVipButton"
                    >
                        <div class="cvh">
                            <div class="name">{{ popupDetail.popupTitle }}</div>
                            <div class="price">
                                ¥ <span>{{ vipGoodsDetail.price }}</span>
                            </div>
                        </div>
                        <div class="mark" v-if="lessonDetail.cornerMark">
                            {{ lessonDetail.cornerMark }}
                        </div>
                        <div class="image" v-if="lessonDetail.detailImg">
                            <img :src="lessonDetail.detailImg" />
                        </div>
                    </div>
                    <payList direction="vrt" theme="vrt2" :showPayForOther="true" v-if="isAndroid" />
                </div>
            </div>
            <div class="loading" v-else>
                <loading bgColor="#666" />
            </div>
            <div class="footer">
                <div class="main">
                    <button @click="buy" :class="loaded ? '' : 'disabled'">
                        ¥{{ showPrice }} 确认支付
                    </button>
                </div>
                <div class="line">
                    <vipAgreement theme="blue"></vipAgreement>
                    <couponItem v-if="this.selectGoods === 'vip'" :couponUsable="couponUsable" :goodsDetail="currentGoodsDetail" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {mapState, mapGetters, mapMutations} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {find} from 'lodash-es'
import {webClose} from '../../utils/jump'
import {getBestCoupon} from '../../utils/coupon'
import {replaceInviteUrl} from '../../utils/constant'
import {getLessonDetail} from '../../server/topLesson'
import {getGoodsDetail} from '../../server/goods'
import {
    queryOrderStatus,
    createMobileOrder,
    payLessonGroup,
    getPayAfterStrategy,
} from '../../utils/payHelper'
import {
    isAndroid,
    isIOS,
    URLParams,
    trackEvent,
    setEmbeddedHeight,
    openNewVip,
    getUrl,
} from '../../utils/tools'
import loading from '../../components/loading'
import payList from '../../components/payList'
import vipAgreement from '../../components/vipAgreement'
import couponItem from '../../components/couponItem'

window.webviewFeature = 'vipWebview'

let pageName = '精品课课程支付弹窗'
export default {
    components: {loading, payList, vipAgreement, couponItem},
    props: {
        lessonId: {
            type: [String, Number],
            default: URLParams.id,
        },
        fragmentName1: {
            type: String,
            default: URLParams.fragmentName1 || 'unknown',
        },
        isRoot: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            selectGoods: 'lesson',
            isAndroid,
            lessonDetail: {},
            popupDetail: {
                popupTitle: '',
            },
            vipGoodsDetail: {
                price: null,
            },
            patternCode: URLParams.patternCode,
            loaded: false,
            viewHeight: 0,
        }
    },
    watch: {
        viewHeight(val) {
            if (this.isRoot) {
                setEmbeddedHeight(val)
            } else {
                this.$emit('payData', {
                    viewHeight: val,
                    lessonDetail: this.lessonDetail,
                    popupDetail: this.popupDetail,
                    vipGoodsDetail: this.vipGoodsDetail,
                })
            }
        },
    },
    computed: {
        ...mapState(['selectCoupons']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed']),
        showVipButton() {
            return this.popupDetail.groupKey && !this.popupDetail.bought
        },
        currentGoodsDetail() {
            if (this.selectGoods === 'lesson') {
                return this.lessonDetail
            } else {
                return this.vipGoodsDetail
            }
        },
        couponUsable() {
            return this.selectCoupons[this.currentGoodsDetail.groupKey + '_selectCoupon'] || {}
        },
        showPrice() {
            if (this.selectGoods === 'vip') {
                let payPrice = this.currentGoodsDetail.price
                let couponPrice = this.couponUsable.priceCent
                if (couponPrice) {
                    return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
                } else {
                    return payPrice
                }
            } else {
                return this.currentGoodsDetail.price
            }
        },
    },
    async created() {
        if (this.isRoot) {
            document.title = '精品课'
            stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        }
        let readed =
            URLParams.agreementAccept === 'true' || +URLParams.agreementAccept === 1 || this.readed
        this.$store.commit('updatePayConfig', {readed})

        await queryOrderStatus('vip', {
            toStatus: true,
            closeSelf: true,
            reFresh: false,
            toLogin: false,
            sendMessage: false,
            useNewApi: true,
        })
        queryOrderStatus('lesson', {
            toStatus: true,
            closeSelf: true,
            reFresh: false,
            toLogin: false,
            sendMessage: true,
            useNewApi: true,
        })
    },
    async mounted() {
        const resData = await getLessonDetail({
            carType: URLParams.carStyle,
            id: this.lessonId,
            patternCode: URLParams.patternCode,
            sceneCode: URLParams.sceneCode,
        })

        this.loaded = true
        this.lessonDetail = resData
        this.popupDetail = resData.popupDetail || {}

        if (this.popupDetail.groupKey) {
            const goodsDetail = await getGoodsDetail({
                tiku: URLParams.carStyle,
                groupKey: this.popupDetail.groupKey,
            })
            this.getCoupon(goodsDetail)
            this.vipGoodsDetail = goodsDetail
        }
        this.setPageHeight()

        // payPath = 1 不上报埋点
        if (this.isRoot && !(URLParams.payPath && +URLParams.payPath)) {
            let fragmentName1 = URLParams.fragmentName1 || 'unknown'
            let actionType = '点击'
            let actionName = '去支付'

            // 埋点梳理-驾考宝典-V8.9.0
            // xx_xx_点击去支付
            trackEvent({
                fragmentName1,
                actionType,
                actionName,
                lessonGroupId: URLParams.id,
                lessonId: URLParams.lessonId,
                payPathType: 0,
                payStatus: 2,
                fromItemCode: URLParams.fromItemCode,
            })
        }
    },
    methods: {
        ...mapMutations([
            'updateSelectCoupons',
        ]),
        close() {
            if (this.isRoot) {
                webClose()
            } else {
                this.$emit('close')
            }
        },
        async getCoupon(goodsDetail) {
            let coupon = await getBestCoupon(goodsDetail)

            if (coupon.couponCode) {
                this.updateSelectCoupons({[goodsDetail.groupKey + '_selectCoupon']: coupon})
            }
        },
        setPageHeight() {
            let viewHeight = 290
            if (isAndroid) {
                viewHeight += 90
            }
            if (this.showVipButton) {
                viewHeight += 140
            }
            this.viewHeight = viewHeight
        },
        changeGoods(type) {
            this.selectGoods = type
        },
        async buy() {
            let fragmentName1 = this.fragmentName1
            if (this.checkAgreement && !this.readed) {
                await this.$confirmProtocol()
            }
            this.payForVip(fragmentName1)

            let fragmentName2 = '支付弹窗'
            let actionType = '点击'
            let actionName = '确认支付'

            let strs = {}
            if (this.selectGoods === 'lesson') {
                strs = {
                    lessonGroupId: URLParams.id,
                    fromItemCode: URLParams.fromItemCode,
                    payPathType: 0,
                    payStatus: 2,
                }
            } else {
                strs = {
                    groupKey: this.vipGoodsDetail.groupKey,
                    lessonId: URLParams.lessonId,
                    fromItemCode: URLParams.fromItemCode,
                    payPathType: 0,
                    payStatus: 2,
                }
            }
            // 埋点梳理-驾考宝典-V8.9.0
            // xx_xx_支付弹窗_点击确认支付
            trackEvent({fragmentName1, fragmentName2, actionType, actionName, ...strs})
        },
        payForVip(fragmentName1) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            if (this.selectGoods === 'lesson') {
                if (payType === 100) {
                    openNewVip({
                        url: getUrl(replaceInviteUrl, {
                            channelCode: this.lessonDetail.channelCode,
                        }),
                    })
                    return
                }
                payLessonGroup(
                    {
                        payType,
                        payChannel,
                        lessonGroupId: URLParams.id,
                        appleId: this.lessonDetail.applePriceId,
                        squirrelGoodsInfo: this.lessonDetail.squirrelGoodsInfo,

                        ref: 'JIAKAOBAODIAN',
                        page: stat.getPageName(),
                        statExtra: '支付',
                        extraInfo: JSON.stringify({
                            lessonId: URLParams.id,
                        }),
                        fragmentName1: fragmentName1,
                        payPathType: 0,
                        pageData: {
                            lessonId: URLParams.lessonId,
                        },
                    },
                    getPayAfterStrategy(true, 'lesson')
                )
            } else {
                if (payType === 100) {
                    openNewVip({
                        url: getUrl(replaceInviteUrl, {
                            channelCode: this.vipGoodsDetail.groupKey,
                        }),
                    })
                    return
                }
                createMobileOrder(
                    {
                        sessionIds: this.vipGoodsDetail.sessionIdList.join(','),
                        appleId: this.vipGoodsDetail.appleId,
                        tiku: URLParams.carStyle,
                        payType,
                        payChannel,
                        couponCode: this.couponUsable.couponCode,
                        activityType: this.vipGoodsDetail.activityType,
                        groupKey: this.vipGoodsDetail.groupKey,
                        squirrelGoodsInfo: this.vipGoodsDetail.squirrelGoodsInfo,

                        ref: 'JIAKAOBAODIAN',
                        page: stat.getPageName(),
                        statExtra: '支付',
                        fragmentName1: fragmentName1,
                        payPathType: 0,
                        pageData: {
                            groupKey: this.vipGoodsDetail.groupKey,
                            lessonId: URLParams.lessonId,
                        },
                    },
                    getPayAfterStrategy(true, 'vip')
                )
            }
        },
    },
}
</script>

<style lang="less">
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
}

.lesson-page {
    background: #ffffff;
    height: 100%;
    .lesson-home {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        .close {
            position: absolute;
            right: 24px;
            top: 18px;
            width: 40px;
            height: 40px;
            background: url(./images/<EMAIL>) no-repeat;
            background-size: 40px 40px;
            z-index: 1;

            &:after {
                content: '';
                position: absolute;
                left: -20%;
                right: -20%;
                top: -20%;
                bottom: -20%;
            }
        }
        .loading {
            flex: 1;
            position: relative;
        }
        .scroll_view {
            overflow-y: auto;
        }
        .header {
            padding: 20px 0 20px 40px;
            border-bottom: 1px solid #e8e8e8;
            .title {
                .fontSizeWithElder(32px);
                color: #333;
            }
        }
        .content {
            margin-top: 38px;
            .goods-item {
                margin: 40px 40px 0;
                padding: 20px 0;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 8px;
                position: relative;
                &.elder-item {
                    border: 0;
                    background: linear-gradient(116deg, #ffe283 0%, #feebb0 35%, #fed162 100%);
                    &.active {
                        background: linear-gradient(116deg, #ffe283 0%, #feebb0 35%, #fed162 100%);
                        &::after {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 38px;
                            height: 34px;
                            background: url(./images/<EMAIL>) no-repeat center;
                            background-size: cover;
                        }
                        .price {
                            color: #333;
                        }
                    }
                }
                &.active {
                    background: #fff4f4;
                    border-color: #f25247;
                    .price {
                        color: #f25247;
                    }
                }
                .cvh {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 40px 0 30px;
                }
                .name {
                    .fontSizeWithElder(36px);
                    font-weight: bold;
                }
                .price {
                    .fontSizeWithElder(36px);
                    white-space: nowrap;
                    span {
                        .fontSizeWithElder(52px);
                        font-weight: bold;
                    }
                }
                .image {
                    margin: 20px auto 0;
                    width: 626px;
                }
                .mark {
                    position: absolute;
                    padding: 0 10px;
                    top: -20px;
                    right: 0;
                    height: 40px;
                    line-height: 40px;
                    .fontSizeWithElder(24px);
                    color: #fff;
                    background: linear-gradient(90deg, #ff7810 0%, #fe3c29 55%, #fe6164 100%);
                    border-radius: 60px 60px 60px 8px;
                    max-width: 600px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
            .pay-list {
                margin-top: 20px;
            }
        }
        .footer {
            padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
            padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
            .line {
                padding: 10px 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .agreement {
                .fontSizeWithElder(24px);
            }
            .main {
                margin-top: 10px;
                padding: 0 30px;
                position: relative;
                button {
                    .fontSizeWithElder(32px);
                    width: 100%;
                    height: 96px;
                    background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
                    border-radius: 48px;
                    color: #fff;
                    border: none;
                    text-align: center;
                    &.disabled {
                        background: #999;
                    }
                    .price {
                        display: block;
                        .fontSizeWithElder(32px);
                        line-height: 1.2;
                    }
                    .delPrice {
                        display: block;
                        .fontSizeWithElder(20px);
                        line-height: 1.2;
                    }
                }
                .mark {
                    position: absolute;
                    top: -30px;
                    right: 8px;
                    padding: 0 20px;
                    text-align: center;
                    background: linear-gradient(111deg, #ffd74f 0%, #ffc634 100%);
                    border-radius: 0px 20px 0px 16px;
                    border-radius: 0 20px 0 16px;
                    color: #8c2801;
                    .fontSizeWithElder(22px);
                    line-height: 40px;
                }
            }
        }
    }
}
</style>
