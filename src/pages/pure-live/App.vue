<template>
    <div @click.prevent class="page-container" :key="roomDetail.timestamp">
        <verticalTop v-if="roomDetail.timestamp && bizConfig.playStatus" />
        <videoPlayer :isShowFullScreen="isShowFullScreen"></videoPlayer>
        <div class="interact-wrap">
            <chat v-if="roomDetail.timestamp || showErrorTips" :showErrorTips="showErrorTips" :recordPlay="!startPlay" />
        </div>
    </div>
</template>

<script>
import {mapMutations, mapState} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {URLParams, addClass, removeClass, trackPageShow, updateSessionId, toast, isHarmony, isTablet} from '../../utils/tools'
import roomStatusMixin from '../../utils/roomStatusMixin'
import {ROOM_TYPE} from '../../utils/constant'
import {reload} from '../../utils/jump'
import {getLiveDetail, inform} from '../../server/topLesson'
import verticalTop from '../index/components/verticalTop.vue'
import chat from '../index/components/chat.vue'
import videoPlayer from '../../components/videoPlayer.vue'

// TODO 加载优化，进入直播页时，客户端只请求播放地址，而不是全部直播间信息

// http://jiakao.nav.mucang.cn/topLesson/live-channel?type=free
// http://jiakao.nav.mucang.cn/topLesson/live?id=3110
// 安卓8.3.2&ios8.3.6 新增参数透传

let pageName = '精品课直播间页-外部直播'
export default {
    mixins: [roomStatusMixin],
    components: {
        verticalTop,
        chat,
        videoPlayer,
    },
    data() {
        return {
            showErrorTips: false,
            isShowFullScreen: false,
            anchorIdentity: 'official',
            liveRoomMode: !!URLParams.anchorId,
            startPlay: false,
        }
    },
    provide() {
        return {
            anchorIdentity: this.anchorIdentity,
            liveRoomMode: this.liveRoomMode,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'bizConfig', 'roomStatus', 'pendantResource', 'isLandscape']),
    },
    watch: {
        'bizConfig.playStatus': {
            handler(val, oldVal) {
                if (oldVal === 0) {
                    if (val === 3 && this.roomDetail.orientation === 2) {
                        addClass(document.body, 'vertical')
                        removeClass(document.body, 'portrait')
                    }
                } else {
                    if (val === 3 || oldVal === 3) {
                        reload()
                    }
                }
            },
        },
        'roomStatus.sessionId': {
            handler(val) {
                if (this.liveRoomMode) {
                    updateSessionId(val)
                    this.init()
                }
            },
        },
    },
    created() {
        let isLandscape = Math.abs(window.orientation) === 90
        if (isHarmony && isTablet) {
            isLandscape = false
        }
        this.setLandscape(isLandscape)
        stat.setPageName(pageName)

        this.$EventBus.$on('startPlay', val => {
            this.startPlay = val
        })

        this.$EventBus.$on('setOrientation', (orientation, callback) => {
            callback && callback()
        })
        if (!this.liveRoomMode) {
            this.init()
        }
    },
    methods: {
        ...mapMutations(['setLandscape']),
        pingErrorHandler(error) {
            if (!this.roomStatus.sessionId) {
                if (!this.showErrorTips && error && error.message === '直播场次不存在') {
                    toast('直播尚未开始，请稍后重试')
                }
                this.showErrorTips = true
            }
        },
        async init() {
            inform({
                id: URLParams.id,
            })
            this.getRoomDetail(URLParams.id)
        },
        async getRoomDetail(id) {
            try {
                let resData = await getLiveDetail({
                    id: id,
                    carType: URLParams.carStyle,
                })
                document.title = resData.title
                resData.sessionId = id
                resData.roomType = resData.roomType || 1
                if (resData.orientation === 1) {
                    addClass(document.body, 'vertical')
                    removeClass(document.body, 'portrait')
                }
                let {roomType, free} = resData
                let saleMode =
                    roomType === ROOM_TYPE.CGZB ||
                    roomType === ROOM_TYPE.HYZX ||
                    roomType === ROOM_TYPE.ZBZB ||
                    roomType === ROOM_TYPE.ZBDH ||
                    roomType === ROOM_TYPE.ZDSTB_FREE ||
                    roomType === ROOM_TYPE.ZBMK
                let stMode =
                    roomType === ROOM_TYPE.KQFD ||
                    roomType === ROOM_TYPE.ZXGK ||
                    roomType === ROOM_TYPE.ZDSTB_OLD ||
                    roomType === ROOM_TYPE.ZDSTB
                this.$store.commit('updateBizConfig', {
                    saleMode: saleMode || (free && stMode),
                    stMode: stMode,
                })
                this.$store.commit('setRoomDetail', resData)
                this.showErrorTips = false
            } catch (error) {
                this.showErrorTips = true
            }

            // 埋点梳理-驾考宝典-0610
            // 精品课直播间页_展示
            trackPageShow({
                actionType: '展示',
                payStatus: 0,
                groupKey: this.pendantResource.goodsKey,
                goodsUniqueKey: this.pendantResource.goodsKey,
            })
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
    background: #000;
}
body:not(.tablet) {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
}

.page-container {
    height: 100%;
    display: flex;
    padding-top: env(safe-area-inset-top);
    // background-image: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2023/02/15/11/389ab047ac1a402582e7d7e2c69a4420.png);
    background-image: linear-gradient(90deg, #251d5a 0%, #5f1d22 100%);
    background-repeat: repeat-y;
    background-size: 100% auto;
    background-color: #1b1d22;
}

@media (min-width: 689px) {
    .landscape .interact-wrap {
        flex: 1;
        overflow: hidden;
    }
}

/* 540+150=690 */
@media (max-width: 690px) {
    .landscape .interact-wrap {
        width: 4.5rem;
    }
}

.interact-wrap {
    display: flex;
    flex-direction: column;
    position: relative;
}
.vertical {
    .interact-wrap {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 520px !important;
    }
}
.portrait {
    // portrait
    body {
        background: url(../../assets/images/page-bg.png) repeat-y;
        background-size: 100% auto;
    }
    .page-container {
        flex-direction: column;
    }
    .interact-wrap {
        height: 0;
        flex: 1;
    }
}
.landscape {
    // landscape
    .interact-wrap {
        background: url(../../assets/images/page-bg.png) repeat-y;
        background-size: 100% auto;
    }
}
</style>
