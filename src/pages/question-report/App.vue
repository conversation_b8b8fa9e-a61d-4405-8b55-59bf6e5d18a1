<template>
    <examReportPopup :show.sync="visible" :isInDialog="true" :sessionId="sessionId" :activityId="activityId" :openShareMenu="openShareMenu" />
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import examReportPopup from '../../components/examReportPopup'
import {URLParams, setEmbeddedHeight} from '../../utils/tools'

MCProtocol.register('Vip.enableOutsideCancel', function(config) {
    return config
})

let pageName = '答题报告'
export default {
    components: {examReportPopup},
    data() {
        return {
            visible: false,
            sessionId: URLParams.id,
            activityId: URLParams.activityId,
            openShareMenu: URLParams.openShareMenu === '1',
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '答题报告'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(558)
    },
    mounted() {
        MCProtocol.Vip.enableOutsideCancel({
            enable: true,
        })
        this.visible = true
    },
}
</script>

<style lang="less">
</style>
