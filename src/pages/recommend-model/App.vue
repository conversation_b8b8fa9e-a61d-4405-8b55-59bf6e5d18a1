<template>
    <div>
        <recommendBuyPupop
            v-if="recommendContent.goodsUniqueKey"
            :remainderText="remainderText"
            :show.sync="recommendBuyVisible"
            :content="recommendContent"
            :recommendRemainSotckInfo="remainSotckInfo"
            :isInDialog="true"
        ></recommendBuyPupop>
        <template v-else>
            <div class="close" @click="close"></div>
            <loading bgColor="#666" />
        </template>
    </div>
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {getBatchSellRate, getSellApplyScene} from '../../server/topLesson'
import recommendBuyPupop from '../../components/recommendBuyPupop'
import loading from '../../components/loading'
import {URLParams, formatRemainTime, trackEvent, setEmbeddedHeight} from '../../utils/tools'
import {webClose} from '../../utils/jump'

let pageName = '直播间页'
export default {
    components: {recommendBuyPupop, loading},
    data() {
        return {
            recommendBuyVisible: false,
            remainderText: '00:00:00',
            timer: null,
            sellRateTimer: null,
            remainSotckInfo: {
                sellRateEnable: false,
                sellRateNum: 100,
                applyScene: '',
            },
            recommendContent: {
                detail: {},
            },
            index: isNaN(URLParams.index) ? 0 : +URLParams.index,
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    async created() {
        document.title = '直播间'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(346, 30)
        this.$EventBus.$on('setOrientation', (orientation, callback) => {
            callback && callback()
        })
        if (URLParams.carStyle === 'car') {
            this.initStockInfo()
        }

        MCProtocol.Vip.getGoodsContent({
            dataId: URLParams.dataId,
            callback: async ret => {
                console.log('getGoodsContent', ret.data)
                if (ret.data) {
                    this.recommendContent = ret.data
                    this.$nextTick(() => {
                        this.recommendBuyVisible = true
                    })
                }
                this.log()
                this.startRemainder()
            },
        })
    },
    methods: {
        close() {
            webClose()
        },
        async initStockInfo() {
            clearInterval(this.sellRateTimer)
            this.sellRateTimer = null
            this.getSellRate()
            this.sellRateTimer = setInterval(() => {
                // 安卓客户端 通过协议轮询接口 会影响登录成功callback，原因待查
                if (document.visibilityState === 'visible') {
                    this.getSellRate()
                }
            }, 10000)

            const resData = await getSellApplyScene({
                lessonItemId: URLParams.id,
                carType: URLParams.carStyle,
            })
            this.remainSotckInfo.applyScene = resData.value || ''
        },
        async getSellRate() {
            const resData = await getBatchSellRate(
                {
                    liveSessionId: URLParams.id,
                },
                {noConsole: true}
            )
            let dataList = resData.itemList.map(item => {
                let sellRateNum
                if (item.value === 0) {
                    sellRateNum = 0
                } else {
                    sellRateNum = Math.floor(item.value * 100)
                    sellRateNum = Math.max(sellRateNum, 1)
                }
                return {
                    sellRateEnable: item.enable,
                    sellRateNum,
                }
            })
            let saleData = dataList[this.index]
            this.remainSotckInfo.sellRateEnable = saleData.sellRateEnable
            this.remainSotckInfo.sellRateNum = saleData.sellRateNum
        },
        startRemainder() {
            if (!this.recommendContent.popupTime) {
                this.remainderText = '00:00:00'
                return
            }
            let seconds = this.recommendContent.popupTime
            let date = +new Date()
            let gap = this.recommendContent.timestamp - date
            let end = date + gap + seconds * 1000
            clearInterval(this.timer)
            this.timer = setInterval(() => {
                date = +new Date()
                let remainder = end - date
                this.remainderText = formatRemainTime(remainder, 'mm:ss:SS')
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    webClose()
                }
            }, 16)
        },
        log() {
            let fragmentName1 = '推荐商品弹窗'
            let actionType = '出现'

            // 埋点梳理-驾考宝典-0713
            // 精品课直播间页_推荐商品弹窗_出现
            trackEvent({
                fragmentName1,
                actionType,
                groupKey: this.recommendContent.goodsUniqueKey,
                goodsUniqueKey: this.recommendContent.goodsUniqueKey,
            })
        },
    },
}
</script>

<style lang="less" scoped>
.close {
    position: absolute;
    z-index: 2;
    right: 14px;
    top: 14px;
    width: 50px;
    height: 50px;
    background: url(../../assets/images/<EMAIL>) no-repeat;
    background-size: 50px 50px;
}
</style>
