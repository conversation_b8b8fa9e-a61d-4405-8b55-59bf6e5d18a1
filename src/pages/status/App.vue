<template>
    <div id="app" @click.prevent>
        <div class="status_container">
            <div class="top_header" ref="header">
                <div class="header" style="background-color: rgba(255, 255, 255, 0);">
                    <div class="back" @click="back"></div>
                    <div class="title">{{ title }}</div>
                </div>
            </div>
            <div class="desc_wrap">
                <div>
                    <div class="succ_logo" :class="orderType"></div>
                    <div class="status_txt">{{ tips }}</div>
                    <p class="desc" v-html="desc"></p>
                </div>

                <div class="content">
                    <div v-if="showNext" class="btn" :class="orderType" @click="back">下一步</div>
                    <div v-else class="btn" :class="orderType" @click="login">{{ buttonText }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    trackEvent,
    isMucang,
    getAuthToken,
    isAndroid,
    URLParams,
    goLogin,
} from '../../utils/tools'
import {webClose} from '../../utils/jump'
import {queryOrderStatus} from '../../utils/payHelper'
window.webviewFeature = 'statusPage'
let pageName = '精品课VIP引导登录页'
export default {
    data() {
        let orderType = URLParams.orderType
        let title, tips, desc, buttonText
        if (orderType === 'lesson') {
            title = '支付成功'
            tips = '已解锁课程权限'
            desc = '为保障您的权益，确保成功领取课程权限<br>请进行登录'
            buttonText = '登录解锁课程'
        } else {
            title = '成为会员'
            tips = '恭喜成为驾考宝典VIP会员'
            desc = '为保障您的会员权益，同时享受会员服务<br>请进行会员登录'
            buttonText = '会员登录'
        }
        return {
            showNext: false,
            orderType,
            title,
            tips,
            desc,
            buttonText,
        }
    },
    created() {
        document.title = this.title
        stat.setPageName(pageName)
        MCProtocol.Core.Web.setting({
            fullScreen: true,
        })
        MCProtocol.Core.Web.setStatusBarTheme({
            theme: 'dark',
        })

        if (isMucang) {
            MCProtocol.Vip.show({
                h5whRate: 0,
                h5ContentMaxWidth: 480,
            })
        }
        getAuthToken(authToken => {
            if (authToken) {
                this.showNext = true
            }
        })
    },
    mounted() {
        MCProtocol.Core.System.env(data => {
            let {statusBarHeight} = data.data
            if (statusBarHeight) {
                statusBarHeight = statusBarHeight + 'px'
            } else {
                statusBarHeight = 'env(safe-area-inset-top)'
            }
            this.$refs['header'].style.paddingTop = statusBarHeight
        })
    },
    methods: {
        async login() {
            // POINT
            await goLogin()
            this.showNext = true
            await queryOrderStatus('vip', {
                toStatus: false,
                closeSelf: !!isAndroid,
                reFresh: false,
                toLogin: false,
                sendMessage: false,
                useNewApi: true,
            })
            queryOrderStatus('lesson', {
                toStatus: false,
                closeSelf: !!isAndroid,
                reFresh: false,
                toLogin: false,
                sendMessage: true,
                useNewApi: true,
            })

            let actionType = '点击'
            let actionName = '会员登录'

            // 驾考宝典--V8.8.0带上的埋点
            // 精品课VIP引导登录页_点击会员登录
            trackEvent({actionType, actionName})
        },
        back() {
            if (isMucang) {
                webClose()
            } else {
                history.back()
            }
        },
    },
}
</script>

<style lang="less">
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
}

#app {
    height: 100%;
    .status_container {
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
    }

    .top_header {
        padding-top: 60px;
        height: 90px;
        box-sizing: content-box;
        .header {
            display: flex;
            align-items: center;
            padding: 0 30px;
            height: 90px;
            box-sizing: content-box;
            background-color: #fff;
            position: relative;
            .back {
                position: absolute;
                width: 50px;
                height: 50px;
                top: 20px;
                left: 30px;
                background-size: 50px 50px;
                background-repeat: no-repeat;
                background-position: center;
                box-sizing: content-box;
                background-image: url(./images/back.png);
            }
            .title {
                -webkit-box-flex: 1;
                -webkit-flex: 1;
                flex: 1;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                .fontSizeWithElder(40px);
                color: #333;
            }
            .help {
                width: 44px;
                height: 44px;
                background-size: 44px 44px;
                background-repeat: no-repeat;
                background-position: center;
                box-sizing: content-box;
                background-image: url(./images/kf.png);
            }
        }
    }

    .desc_wrap {
        margin-top: 160px;
    }

    .succ_logo {
        text-align: center;
        margin: 0 auto;
        width: 148px;
        height: 148px;
        background: url(./images/s1.png) no-repeat center;
        background-size: 148px 148px;
        &.lesson {
            background-image: url(./images/s2.png);
        }
    }

    .status_txt {
        .fontSizeWithElder(40px);
        padding-top: 20px;
        font-weight: bold;
        color: #333333;
        text-align: center;
    }
    .desc {
        .fontSizeWithElder(30px);
        color: #999999;
        text-align: center;
    }

    .content {
        padding: 80px 100px 0;
        width: 100%;
        box-sizing: border-box;
    }

    .btn {
        background: linear-gradient(315deg, #f2ab79 0%, #ffc79f 100%);
        color: #542702;
        border-radius: 44px;
        .fontSizeWithElder(36px);
        height: 88px;
        line-height: 88px;
        text-align: center;
        margin-bottom: 20px;
        &.lesson {
            background: linear-gradient(135deg, #67cef8 0%, #1e74fa 100%);
            color: #fff;
        }
    }
}
</style>
