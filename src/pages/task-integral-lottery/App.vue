<template>
    <div id="app" @click.prevent>
        <div style="position: absolute; opacity: 0; pointer-events: none;">
            <img src="./images/draw-bg.png" />
        </div>
        <div class="article">
            <div class="close" @click="close"></div>
            <div class="menu" @click.stop="toggleMenu" v-clickoutside="closeMenu">
                <div class="menu-popup" v-if="menuVisible">
                    <div class="item" @click="openActivityRules">规则</div>
                    <div class="item" @click="openPointAccount">明细</div>
                    <div class="item" @click="openPrizeList">奖品</div>
                </div>
            </div>
            <div class="banner">
                <div class="btn" v-if="showBtn" @click="gotDraw"></div>
                <canvas class="btn-ani" v-if="showBtnAni" @click="gotDraw" ref="drawBtn"></canvas>
            </div>
            <div class="roll-message">
                <messageList />
            </div>
            <prizeList />
            <taskList />
        </div>
        <rules :show.sync="activityRulesVisible" />
        <pointAccount :show.sync="pointAccountVisible" />
        <popup
            class="draw-model"
            ref="wrap"
            :show.sync="drawVisible"
            :showMask="false"
            :showClose="false"
        >
            <canvas class="poster" v-if="showDrawAni" ref="poster"></canvas>
            <div class="content" v-else>
                <div class="close" @click="closeDialog"></div>
                <div class="img" :class="getPrizeImgClass(presentInfo.category)"></div>
                <div class="tag" :class="getPrizeTagClass(presentInfo.presentRelType)"></div>
                <div class="info">
                    <p class="name">{{ presentInfo.name }}</p>
                    <p class="expired">{{ presentInfo.quantity + presentInfo.unit }}体验卡</p>
                </div>
                <div
                    class="btn"
                    v-if="presentInfo.actionUrl"
                    @click="goUse(presentInfo.actionUrl)"
                ></div>
            </div>
        </popup>

        <confirm
            :show.sync="noCoinVisible"
            :message="'完成任务获得更多盲盒币'"
            :title="'盲盒币不足'"
            :confirmText="'我知道了'"
            :showConfirmButton="true"
            @confirm="closeNoCoin"
        >
        </confirm>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    trackEvent,
    URLParams,
    webOpen,
    loadScript,
    getAuthToken,
    goLogin,
    setEmbeddedHeight,
    trackPageShow,
} from '../../utils/tools'
import {awardListURL} from '../../utils/constant'
import {drawPresent} from '../../server/active'
import {webClose} from '../../utils/jump'
import messageList from './components/messageList'
import prizeList from './components/prizeList'
import taskList from './components/taskList'
import rules from './components/rules'
import pointAccount from './components/pointAccount'
import popup from '../../components/dialog'
import confirm from '../../components/confirm'
import lottFile from './images/lott.pag'
import btnFile from './images/btn.pag'

loadScript('./static/libpag.min.js').then(async () => {
    console.time('PAG done')
    try {
        window.pagInit = await window.libpag.PAGInit({
            locateFile: function(file) {
                return 'https://core.luban.assets.interceptor.mucang.cn?file=pag/libpag.wasm'
            },
        })
        console.timeEnd('PAG done')
        console.log('PAG locateFile')
    } catch (error) {
        window.pagInit = await window.libpag.PAGInit()
        console.timeEnd('PAG done')
        console.log('PAG remoteFile')
    }
    // window.pagInit = await window.libpag.PAGInit()
    // console.timeEnd('PAG done')
    // console.log('PAG remoteFile')
})

const clickoutside = {
    // 初始化指令
    bind(el, binding, vnode) {
        function documentHandler(e) {
            // 这里判断点击的元素是否是本身，是本身，则返回
            if (el.contains(e.target)) {
                return false
            }
            // 判断指令中是否绑定了函数
            if (binding.expression) {
                // 如果绑定了函数 则调用那个函数，此处binding.value就是handle方法
                binding.value(e)
            }
        }
        // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
        el.__vueClickOutside__ = documentHandler
        document.addEventListener('click', documentHandler)
    },
    unbind(el, binding) {
        // 解除事件监听
        document.removeEventListener('click', el.__vueClickOutside__)
        delete el.__vueClickOutside__
    },
}

let pageName = '直播间活动主页'
export default {
    components: {messageList, prizeList, taskList, rules, pointAccount, popup, confirm},
    directives: {clickoutside},
    data() {
        return {
            menuVisible: false,
            activityRulesVisible: false,
            pointAccountVisible: false,
            drawVisible: false,
            noCoinVisible: false,
            showDrawAni: false,
            presentInfo: {},
            showBtn: true,
            showBtnAni: false,
        }
    },
    computed: {
        ...mapState(['lotteryData']),
    },
    created() {
        document.title = pageName
        stat.setPageName(pageName)
        setEmbeddedHeight(620)
    },
    mounted() {
        // 直播间活动主页_展示
        trackPageShow({
            actionType: '展示',
            roomId: URLParams.anchorId,
        })

        let timer = setInterval(async () => {
            if (window.pagInit) {
                this.showBtnAni = true
                this.startBtnAni()
                clearInterval(timer)
            }
        }, 666)
    },
    methods: {
        async startBtnAni() {
            this.$nextTick(async () => {
                var $drawBtn = this.$refs.drawBtn
                const buffer = await fetch(btnFile).then(response => response.arrayBuffer())
                const pagFile = await window.pagInit.PAGFile.load(buffer)
                const pagView = await window.pagInit.PAGView.init(pagFile, $drawBtn)
                pagView.setRepeatCount(0)
                pagView.addListener('onAnimationStart', () => {
                    this.showBtn = false
                })
                pagView.play()
            })
        },
        close() {
            webClose()
        },
        toggleMenu() {
            this.menuVisible = !this.menuVisible
        },
        closeMenu() {
            this.menuVisible = false
        },
        openActivityRules() {
            this.activityRulesVisible = true
        },
        openPointAccount() {
            this.pointAccountVisible = true
        },
        async openPrizeList() {
            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                return
            }
            let activityId = URLParams.activityId
            webOpen({
                url: `${awardListURL}?activityId=${activityId}`,
                titleBar: true,
            })
        },
        async gotDraw() {
            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                return
            }
            try {
                let actionType = '点击'
                let actionName = '抽奖'

                // 精品课直播间页_点击抽奖
                trackEvent({actionType, actionName, roomId: URLParams.anchorId})
            } catch (error) {}
            if (this.lotteryData.point < 100) {
                this.noCoinVisible = true
                return
            }
            this.drawVisible = true
            this.showDrawAni = true
            this.$nextTick(async () => {
                var $wrap = this.$refs.wrap
                var $poster = this.$refs.poster
                let resData
                let PAG = window.pagInit
                if (!PAG) {
                    try {
                        resData = await drawPresent(
                            {
                                activityId: URLParams.activityId,
                            },
                            {showErrorInfo: true}
                        )
                        this.showDrawAni = false
                        this.presentInfo = resData
                        this.$EventBus.$emit('updatePoint')
                    } catch (error) {
                        this.drawVisible = false
                    }
                    // TEST
                    // setTimeout(() => {
                    //     resData = {
                    //         actionUrl: '',
                    //         category: 9,
                    //         name: '付费视频VIP',
                    //         quantity: '5',
                    //         unit: '天',
                    //     }
                    //     this.presentInfo = resData
                    // }, 2500)
                    return
                }
                const buffer = await fetch(lottFile).then(response => response.arrayBuffer())
                const pagFile = await window.pagInit.PAGFile.load(buffer)
                const pagView = await PAG.PAGView.init(pagFile, $poster)
                pagView.setRepeatCount(0)
                pagView.addListener('onAnimationRepeat', () => {
                    if (resData.category) {
                        pagView.destroy()
                        this.showDrawAni = false
                        $wrap.$el.style.backgroundColor = 'rgba(0, 0, 0, 0.8)'

                        try {
                            let fragmentName1 = '抽奖结果弹窗'
                            let actionType = '出现'

                            // 直播间活动主页_抽奖结果弹窗_出现
                            trackEvent({fragmentName1, actionType, roomId: URLParams.anchorId})
                        } catch (error) {}
                    }
                })
                pagView.play()
                $wrap.$el.style.backgroundColor = 'rgba(0, 0, 0, 1)'
                try {
                    resData = await drawPresent(
                        {
                            activityId: URLParams.activityId,
                        },
                        {showErrorInfo: true}
                    )
                    this.presentInfo = resData
                    this.$EventBus.$emit('updatePoint')
                } catch (error) {
                    pagView.destroy()
                    this.drawVisible = false
                }
                // TEST
                // setTimeout(() => {
                //     resData = {
                //         actionUrl: '',
                //         category: 9,
                //         name: '付费视频VIP',
                //         quantity: '5',
                //         unit: '天',
                //     }
                //     this.presentInfo = resData
                // }, 2500)
            })
        },
        getPrizeImgClass(category) {
            if (category === 7) {
                return 'g2'
            } else if (category === 8) {
                return 'g1'
            } else if (category === 9 || category === 11) {
                return 'g3'
            } else if (category === 10) {
                return 'g4'
            }
        },
        getPrizeTagClass(presentRelType) {
            let prizeTag
            switch (presentRelType) {
                case 22:
                case 23:
                case 25:
                case 26:
                case 28:
                case 29:
                case 34:
                case 35:
                    prizeTag = 'lv3'
                    break
                case 21:
                case 24:
                case 27:
                case 32:
                case 33:
                    prizeTag = 'lv2'
                    break
                case 30:
                case 31:
                    prizeTag = 'lv1'
                    break
            }
            return prizeTag
        },
        closeDialog() {
            this.drawVisible = false
        },
        goUse(url) {
            if (url) {
                webOpen({
                    url,
                })
                this.drawVisible = false
            }
        },
        closeNoCoin() {},
    },
}
</script>
<style lang="less">
@keyframes enlarge {
    0% {
        transform: scale(0.9);
        opacity: 0.85;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>
<style lang="less" scoped>
html,
body {
    height: 100%;
    overflow: hidden;
}
.draw-model {
    background-color: rgba(0, 0, 0, 0.85);
    width: 100%;
    height: 100%;
    transition: background-color 0.5s linear;

    /deep/ .dialog {
        height: 982px;
        width: 100%;
    }
    .poster {
        width: 100%;
        height: 100%;
    }
    .content {
        width: 100%;
        height: 100%;
        background: url(./images/draw-bg.png) no-repeat top center;
        background-size: 100% auto;
        animation: enlarge 0.2s ease-out;
        overflow: hidden;
        position: relative;
        .close {
            position: absolute;
            top: 4px;
            right: 26px;
            width: 60px;
            height: 60px;
            background: url(./images/close2.png) no-repeat center;
            background-size: 100% auto;
        }
        .img {
            position: absolute;
            top: 192px;
            left: 176px;
            width: 398px;
            height: 340px;
            background: no-repeat center / 100% auto;
            &.g1 {
                background-image: url(./images/g1.png);
            }
            &.g2 {
                background-image: url(./images/g2.png);
            }
            &.g3 {
                background-image: url(./images/g3.png);
            }
            &.g4 {
                background-image: url(./images/g4.png);
            }
        }
        .tag {
            position: absolute;
            top: 448px;
            right: 74px;
            width: 164px;
            height: 164px;
            background: no-repeat center / 100% auto;

            &.lv1 {
                background-image: url(./images/prize-lv1.png);
            }

            &.lv2 {
                background-image: url(./images/prize-lv2.png);
            }

            &.lv3 {
                background-image: url(./images/prize-lv3.png);
            }
        }
        .info {
            position: absolute;
            top: 604px;
            left: 142px;
            width: 460px;
            .name {
                font-size: 60px;
                color: #ffffff;
                font-weight: 600;
                text-align: center;
                white-space: nowrap;
            }
            .expired {
                margin: 6px auto 0;
                width: 276px;
                height: 50px;
                background: rgba(255, 255, 255, 0.13);
                border-radius: 26px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 36px;
                color: #ffffff;
            }
        }
        .btn {
            position: absolute;
            top: 840px;
            left: 186px;
            width: 390px;
            height: 122px;
            background: url(./images/draw2-btn.png) no-repeat center;
            background-size: 100% auto;
        }
    }
}
#app {
    height: 100%;
    background-color: #069ffd;
    overflow-y: scroll;
    .article {
        background-color: #a9f3fc;
        position: relative;
        .close {
            position: fixed;
            left: 30px;
            top: 36px;
            width: 64px;
            height: 64px;
            background: url(./images/close.png) no-repeat;
            background-size: 100% auto;
            z-index: 3;
        }
        .menu {
            position: fixed;
            right: 30px;
            top: 36px;
            width: 64px;
            height: 64px;
            background: url(./images/menu.png) no-repeat;
            background-size: 100% auto;
            z-index: 3;
        }
    }
    .menu-popup {
        position: absolute;
        right: -16px;
        top: 90px;
        width: 204px;
        height: 246px;
        background: #ffffff;
        border-radius: 16px;
        z-index: 2;
        &::after {
            content: '';
            position: absolute;
            top: -12px;
            right: 36px;
            width: 30px;
            height: 12px;
            font-size: 0;
            background: url(./images/up-icon.png) no-repeat;
            background-size: 100% auto;
        }
        .item {
            height: 82px;
            line-height: 82px;
            text-align: center;
            font-size: 30px;
            color: #333333;
            &:not(:first-child) {
                border-top: 1px solid #f1f1f1;
            }
        }
    }
    .banner {
        height: 798px;
        background: url(./images/banner-bg.png) no-repeat;
        background-size: 100% auto;
        position: relative;
        .btn {
            position: absolute;
            left: 110px;
            top: 580px;
            width: 530px;
            height: 146px;
            background: url(./images/draw-btn.png) no-repeat;
            background-size: 100% auto;
        }
        .btn-ani {
            position: absolute;
            left: 110px;
            top: 580px;
            width: 530px;
            height: 146px;
        }
    }
    .roll-message {
        position: absolute;
        left: 0;
        top: 120px;
        width: 100%;
        height: 600px;
        z-index: 1;
        pointer-events: none;
    }
}
</style>
