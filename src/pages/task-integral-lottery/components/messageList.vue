<template>
    <barrage ref="barrage" class="message" :isPause="false" :isOpen="true" :channelCount="3" />
</template>

<script>
import {URLParams} from '../../../utils/tools'
import {queryRecentPresentShip} from '../../../server/active'
import barrage from './barrage'

export default {
    components: {
        barrage,
    },
    data() {
        return {
            messageList: [],
        }
    },
    mounted() {
        this.getMessageList()
    },
    methods: {
        async getMessageList() {
            let resData = await queryRecentPresentShip({
                activityId: URLParams.activityId,
            })
            this.messageList = resData.itemList.map(item => {
                return {
                    content: `${item.userId}抽中了<span style="color: #ffff89">${item.presentName}</span>`,
                    type: 'dom',
                }
            })
            this.loopSendMsg()
        },
        loopSendMsg() {
            this.$refs['barrage'].receiveDanmu(this.messageList)
            setInterval(() => {
                if (document.visibilityState === 'visible') {
                    this.$refs['barrage'].receiveDanmu(this.messageList)
                }
            }, 20000)
        },
    },
}
</script>

<style lang="less" scoped>
.message /deep/ .barrage-item {
    padding: 0 16px;
    background: rgba(130, 195, 230, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 34px;
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0px 1.6px 0px 0px rgba(0, 0, 0, 0.1);
}
</style>
