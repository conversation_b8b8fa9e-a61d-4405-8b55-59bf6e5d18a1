<template>
    <popup
        class="page-to-left"
        :transitionName="'slide-left'"
        :position="'bottom'"
        :show.sync="visible"
        :showMask="false"
        :showClose="false"
    >
        <div class="header">
            <div class="back" @click="close"></div>
            <div class="title">{{ title }}</div>
            <div class="seat"></div>
        </div>
        <div class="content">
            <slot></slot>
        </div>
    </popup>
</template>

<script>
import popup from '../../../components/dialog'

export default {
    components: {popup},
    data() {
        return {
            visible: false,
        }
    },
    props: {
        title: String,
        show: Boolean,
    },
    watch: {
        show(val) {
            this.visible = val
        },
    },
    methods: {
        close() {
            this.$emit('update:show', false)
        },
    },
}
</script>

<style lang="less" scoped>
.page-to-left {
    /deep/ .dialog {
        height: 100%;
        background: #fff;
        display: flex;
        flex-direction: column;
    }
    .header {
        box-sizing: content-box;
        display: flex;
        align-items: center;
        position: relative;
        height: 64px;
        padding: 36px 30px 30px;

        .back {
            width: 64px;
            height: 64px;
            background-image: url(../images/back-icon.png);
            background-size: 64px 64px;
            background-repeat: no-repeat;
            background-position: center;
            box-sizing: content-box;
        }

        .title {
            flex: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 34px;
        }
        .seat {
            width: 64px;
            height: 64px;
        }
    }
    .content {
        flex: 1;
        overflow-y: auto;
    }
}
</style>
