<template>
    <pageToLeft title="盲盒币明细" v-bind="$props" v-on="$listeners">
        <div class="point-wrap">
            <div class="title">
                <div class="text">{{ lotteryData.point }}</div>
            </div>
            <div class="point-list" v-if="groupDTOList.length">
                <div
                    class="group-item"
                    v-for="groupItem in groupDTOList"
                    :key="groupItem.createDate"
                >
                    <div class="days">{{ groupItem.days }}</div>
                    <div class="item" v-for="item in groupItem.itemDTOList" :key="item.bizId">
                        <div class="time">{{ item.time }}</div>
                        <div class="reason">{{ item.reason }}</div>
                        <div class="point plus" v-if="item.calculateRule === 1">
                            +{{ item.point }}
                        </div>
                        <div class="point plus" v-else-if="item.calculateRule === 2">
                            -{{ item.point }}
                        </div>
                    </div>
                </div>
                <div class="no-more">没有更多了</div>
            </div>
            <div class="no-data" v-else>没有盲盒币，快去做任务吧～</div>
        </div>
    </pageToLeft>
</template>

<script>
import {mapMutations, mapState} from 'vuex'
import {formatDate} from '../../../utils/tools'
import {queryPointAccount} from '../../../server/active'
import pageToLeft from './pageToLeft'

export default {
    components: {
        pageToLeft,
    },
    data() {
        return {
            groupDTOList: [],
        }
    },
    computed: {
        ...mapState(['lotteryData']),
    },
    props: {
        title: String,
        show: Boolean,
    },
    watch: {
        show(val) {
            if (val) {
                this.getPointAccount()
            }
        },
    },
    methods: {
        ...mapMutations(['updateLotteryData']),
        async getPointAccount() {
            let resData = await queryPointAccount({})
            resData.groupDTOList.forEach(groupItem => {
                groupItem.days = formatDate(new Date(groupItem.createDate), 'M月d日')
                if (groupItem.days === formatDate(new Date(), 'M月d日')) {
                    groupItem.days = '今天'
                }

                groupItem.itemDTOList.forEach(item => {
                    item.time = formatDate(item.createTime, 'hh:mm')
                })
            })

            this.groupDTOList = resData.groupDTOList
            this.updateLotteryData({
                point: resData.totalPoint,
            })
        },
    },
}
</script>

<style lang="less" scoped>
.point-wrap {
    // margin-top: 28px;
    .title {
        height: 130px;
        background: url(../images/title-bg.png) no-repeat;
        background-size: 100% auto;
        overflow: hidden;
        .text {
            margin: 30px 0 0 36px;
            width: fit-content;
            height: 70px;
            background: url(../images/text2.png) no-repeat;
            background-size: 278px auto;
            padding-left: 286px;
            color: #333;
            font-size: 60px;
            font-weight: 600;
            line-height: 70px;
            text-shadow: 4px 4px 0 #fff, -4px 4px 0 #fff, 4px -4px 0 #fff, -4px -4px 0 #fff;
        }
    }
    .no-data {
        margin-top: 50px;
        background: url(../images/empty-bg.png) no-repeat center 40px;
        background-size: 360px auto;
        font-size: 28px;
        text-align: center;
        padding-top: 302px;
        padding-bottom: 80px;
    }
}
.point-list {
    padding: 0 40px;
    .group-item {
        padding: 16px 0;
        border-bottom: 1px solid #f6f6f6;
        .days {
            line-height: 64px;
            font-size: 32px;
            font-weight: 600;
            color: #333333;
        }
        .item {
            display: flex;
            height: 96px;
            justify-content: space-between;
            align-items: center;
            .time {
                color: #959595;
                font-size: 26px;
            }
            .reason {
                flex: 1;
                color: #333;
                font-size: 30px;
                padding-left: 16px;
            }
            .point {
                font-size: 32px;
                font-weight: 500;
                background: url(../images/coin-icon.png) center right / 36px auto no-repeat;
                padding-right: 50px;
                &.plus {
                    color: #ff6715;
                }
                &.minus {
                    color: #04a5ff;
                }
            }
        }
    }
    .no-more {
        margin-top: 16px;
        line-height: 64px;
        font-size: 26px;
        color: #959595;
        text-align: center;
    }
}
</style>
