<template>
    <div class="prize-wrap">
        <div class="tabs">
            <div
                class="item"
                @click="switchTo(index)"
                :class="[{active: index === currentIndex}, 'lv' + (index + 1)]"
                :key="index"
                v-for="(item, index) in prizeList"
            >
                {{ item.name }}
            </div>
        </div>
        <div class="text" :class="'g' + (currentIndex + 1)">
            <h5>{{ currentData.title }}</h5>
            <p v-html="currentData.desc"></p>
        </div>
    </div>
</template>

<script>
import {getCityInfo} from '../../../utils/tools'
function getRandom(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min)
}
let timer = null

export default {
    data() {
        return {
            currentIndex: getRandom(0, 3),
            prizeList: [
                {
                    name: '本地考场',
                    title: '科目二考场',
                    desc:
                        '可随机抽取<span>科目二考场免费体验1/3天或3/10次</span>。提前了解科二考场，考试更有信心！',
                },
                {
                    name: '3D项目教学',
                    title: '3D项目教学',
                    desc:
                        '可随机抽取<span>3D练车付费项目免费体验1/3天或3/10次</span>。随时随地练习点位，有效提升车感！',
                },
                {
                    name: '付费教学视频',
                    title: '付费教学视频',
                    desc:
                        '可随机抽取<span>付费教学视频免费观看1/2/3天</span>。本地考试车型，通用教学点位！',
                },
                {
                    name: 'VIP',
                    title: 'VIP',
                    desc:
                        '可随机抽取<span>科目二VIP免费体验1/3/5/7天</span>。VIP可体验所有科目二付费项目！',
                },
            ],
        }
    },
    computed: {
        currentData() {
            return this.prizeList[this.currentIndex]
        },
    },
    async mounted() {
        timer = setInterval(() => {
            if (this.currentIndex >= this.prizeList.length - 1) {
                this.currentIndex = 0
            } else {
                this.currentIndex++
            }
        }, 6666)
        let cityInfo = await getCityInfo()
        if (cityInfo.cityName) {
            this.prizeList[0].title = cityInfo.cityName + this.prizeList[0].title
        }
    },
    methods: {
        switchTo(index) {
            this.currentIndex = index
            if (timer) {
                clearInterval(timer)
            }
        },
    },
}
</script>

<style lang="less" scoped>
.prize-wrap {
    margin-top: -40px;
    .tabs {
        padding: 0 10px 38px;
        margin: 0 auto;
        width: 694px;
        height: 134px;
        background: url(../images/tabs-bg.png) no-repeat;
        background-size: 100% auto;
        display: flex;
        align-items: center;
        color: #ffffff;
        font-size: 26px;
        position: relative;
        z-index: 1;
        .item {
            height: 62px;
            line-height: 62px;
            flex: 1;
            text-align: center;
            border: 1px solid transparent;
            border-radius: 32px;
            white-space: nowrap;
            &.lv3 {
                flex-grow: 0;
                flex-basis: 200px;
            }
            &.lv4 {
                flex-grow: 0;
                flex-basis: 120px;
            }
            &.active {
                background: url(../images/tabs2-bg.png) center 70% / 128px auto,
                    linear-gradient(180deg, #ffffff 19%, #e4f7ff);
                background-repeat: no-repeat;
                border: 1px solid #81d9fc;
                // border-image: linear-gradient(135deg, #cefafa, #81d9fc);
                // border-image-width: 2;
                // border-image-slice: 1;
                color: #233a4e;
                font-weight: bold;
            }
        }
    }
    .text {
        margin: -30px auto 0;
        width: 690px;
        height: 254px;
        border-radius: 20px;
        padding: 24px 274px 0 36px;
        background-color: #ffffff;
        background-repeat: no-repeat;
        background-position: right 20px bottom 10px;
        background-size: 250px auto;
        &.g1 {
            background-image: url(../images/g1.png);
        }
        &.g2 {
            background-image: url(../images/g2.png);
        }
        &.g3 {
            background-image: url(../images/g3.png);
        }
        &.g4 {
            background-image: url(../images/g4.png);
        }

        h5 {
            font-size: 42px;
            font-weight: bold;
            line-height: 2;
            margin-top: 2px;
        }
        p {
            font-size: 26px;
            color: #405669;
            /deep/ span {
                color: #04a5ff;
            }
        }
    }
}
</style>
