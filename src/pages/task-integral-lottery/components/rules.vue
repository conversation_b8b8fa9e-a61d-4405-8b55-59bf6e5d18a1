<template>
    <pageToLeft title="规则说明" v-bind="$props" v-on="$listeners">
        <div class="rules">
            <div class="title" style="margin-top: 0">玩法简介</div>
            <div class="section">
                VIP盲盒是针对直播间用户的玩法。用户完成任务后有机会获得科目二体验VIP、3D练车全项目体验VIP、科目二考场体验VIP、付费视频体验VIP等奖励。
            </div>
            <div class="title">盲盒奖励</div>
            <div class="group">
                <div class="subtitle lv1">科目二考场</div>
                <div class="desc">
                    可随机抽取<span>科目二考场免费体验1/3天或3/10次</span>。提前了解科二考场，考试更有信心！
                </div>
                <div class="subtitle lv2">3D项目教学</div>
                <div class="desc">
                    可随机抽取<span>科目二3D练车付费项目免费体验1/3天或3/10次</span>。随时随地练习点位，有效提升车感
                </div>
                <div class="subtitle lv3">付费视频</div>
                <div class="desc">
                    可随机抽取<span>科目二付费教学视频免费观看1/2/3天</span>。本地考试车型，通用教学点位！
                </div>
                <div class="subtitle lv4">科目二VIP</div>
                <div class="desc">
                    可随机抽取<span>科目二VIP免费体验1/3/5/7天</span>。VIP可体验所有科目二付费项目！
                </div>
                <div class="spec">
                    特别说明：所有奖品获得当天立即生效，且VIP体验时间不叠加
                </div>
            </div>
            <div class="title">开盒机会</div>
            <div class="section">
                完成任务可获得盲盒币，每消耗100盲盒币可开一次盲盒，盲盒币最多可累积200个。
            </div>
            <div class="title">免责申明</div>
            <div class="section">
                <p>
                    1、在参与活动中，如果用户出现违规行为（如作弊领取、恶意批量注册、无效手机、虚假交易等不正当手段，通过此不正当手段获取利益、以所获得权益进行盈利、不当使用或其它违背诚实信用等行为），驾考宝典有权取消违规用户的获取资格，并有权撤销违规交易，收回全部活动权益（含已使用的及未使用的），必要时追究法律责任；
                </p>
                <p>
                    2、活动期间如出现不可抗力或情势变更的情况（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动遭受严重网络攻击或因系统故障不能举办的），驾考宝典可依相关法律法规的规定主张免责；
                </p>
                <p>
                    3、在法律法规允许的范围内，驾考宝典（武汉木仓科技股份有限公司）可以根据本活动的实际举办情况对活动规则进行变动或调整，相关变动或调整将公布在活动页面上，公布后依法生效；
                </p>
                <p>
                    4、同一用户是指根据用户木仓账号关联信息，关联信息包括但不限于同一UID/同一设备号/同一手机号/同一支付账号/同一收货地址等，并且以用户第一个参与本活动的账号为有效账号，其余账号参与结果均视为无效；
                </p>
                <p>
                    5、如有疑问，请至驾考宝典APP-点击右下角人像图标“我的”－帮助与反馈，进行问题反馈；
                </p>
                <p>6、此活动与苹果公司无关。</p>
            </div>
        </div>
    </pageToLeft>
</template>

<script>
import pageToLeft from './pageToLeft'

export default {
    components: {
        pageToLeft,
    },
    data() {
        return {}
    },
    props: {
        title: String,
        show: Boolean,
    },
    mounted() {},
    methods: {},
}
</script>

<style lang="less" scoped>
.rules {
    padding: 0 40px 40px;
    .title {
        margin-top: 36px;
        font-size: 34px;
        color: #333333;
        font-weight: 600;
        position: relative;
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 136px;
            height: 16px;
            background: rgba(103, 188, 255, 0.2);
        }
    }
    .section {
        font-size: 28px;
        line-height: 40px;
        color: #333333;
        margin-top: 10px;
        p {
            margin-bottom: 20px;
        }
    }
    .group {
        .subtitle {
            font-size: 28px;
            font-weight: 600;
            line-height: 40px;
            color: #333333;
            padding-left: 42px;
            margin-top: 20px;
            &.lv1 {
                background: url(../images/rules1-icon.png) left center / 32px auto no-repeat;
            }
            &.lv2 {
                background: url(../images/rules2-icon.png) left center / 32px auto no-repeat;
            }
            &.lv3 {
                background: url(../images/rules3-icon.png) left center / 32px auto no-repeat;
            }
            &.lv4 {
                background: url(../images/rules4-icon.png) left center / 32px auto no-repeat;
            }
        }
        .desc {
            font-size: 26px;
            line-height: 36px;
            color: #6e6e6e;
            padding-left: 42px;
            span {
                color: #04a5ff;
            }
        }
        .spec {
            font-size: 28px;
            padding: 14px 0 0;
            color: #333;
            line-height: 40px;
        }
    }
}
</style>
