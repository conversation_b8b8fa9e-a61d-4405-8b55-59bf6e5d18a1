<template>
    <div class="task-wrap">
        <div class="title">
            <div class="text2" v-if="lotteryData.point">{{ lotteryData.point }}</div>
            <div class="text" v-else></div>
        </div>
        <div class="task-list">
            <div
                class="item"
                :key="item.code"
                v-for="item in taskList"
                :class="getProgressClass(item.progress)"
            >
                <div class="intergral">+{{ item.point }}</div>
                <div class="name">{{ item.name }}</div>
                <div class="btn done" @click="receive(item)" v-if="item.progress === 1">领取</div>
                <template v-else-if="item.progress === 0">
                    <div class="btn unused" v-if="item.type === 2">
                        <template v-if="item.code === currentRemainder.code">
                            {{ currentRemainder.text }}
                        </template>
                        <template v-else>
                            {{ item.btnText }}
                        </template>
                    </div>
                    <div class="btn normal" @click="doit(item)" v-else>
                        {{ item.btnText || '去完成' }}
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
import {mapMutations, mapState} from 'vuex'
import {MCProtocol} from '@simplex/simple-base'
import {keyBy, pick, keys, values, merge, find} from 'lodash-es'
import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {
    webOpen,
    URLParams,
    getUrl,
    formatRemainTime,
    getAuthToken,
    goLogin,
} from '../../../utils/tools'
import {followAnchor} from '../../../server/chat'
import {getLiveRoomDetail} from '../../../server/topLesson'
import {
    queryTaskProgress,
    queryTotalPoint,
    receivePoint,
    reportTaskProgress,
} from '../../../server/active'

// 'livehouse.luban.mucang.cn/notify/followstatus'
MCProtocol.register('livehouse.notify.followstatus', function(config) {
    return config
})
// 'livehouse.luban.mucang.cn/notify/updatetask'
MCProtocol.register('livehouse.notify.updatetask', function(config) {
    return config
})
// 'livehouse.luban.mucang.cn/notify/sendmessages'
MCProtocol.register('livehouse.notify.sendmessages', function(config) {
    return config
})
// 'livehouse.luban.mucang.cn/fetch/viewtimes'
MCProtocol.register('livehouse.fetch.viewtimes', function(config) {
    return config
})

let taskMap = [
    {
        code: 'followAnchor',
        btnText: '关注',
    },
    {
        code: 'sendDanMu',
        desc: '“我正在参与VIP盲盒活动”',
        btnText: '发弹幕',
    },
    {
        code: 'watch3Min',
        btnText: '待解锁',
    },
    {
        code: 'watch5Min',
        btnText: '待解锁',
    },
    {
        code: 'watch10Min',
        btnText: '待解锁',
    },
    {
        code: 'watch20Min',
        btnText: '待解锁',
    },
    {
        code: 'watch30Min',
        btnText: '待解锁',
    },
]
let first = false
export default {
    data() {
        return {
            taskList: [],
            timer: null,
            currentRemainder: {
                code: '',
                text: '',
            },
        }
    },
    computed: {
        ...mapState(['lotteryData']),
    },
    created() {
        this.getRoomDetail()
    },
    mounted() {
        MCProtocol.Listener.shareChannel(shareChannel => {
            let item = find(this.taskList, {code: 'shareBroadCastRoom'})
            if (item.progress === 0) {
                this.reportProgress(item)
            }
        })
        this.getTaskProgress()
        this.getTotalPoint()
        this.$EventBus.$on('updatePoint', () => {
            this.getTotalPoint()
        })

        pageSwitch.onPageShow(() => {
            this.starViewTask()
        })
        pageSwitch.onPageHide(() => {
            clearInterval(this.timer)
        })
    },
    methods: {
        ...mapMutations(['updateLotteryData']),
        shareInit(data) {
            MCProtocol.Core.Share.setting({
                channel: 'weixin_moment,weixin_friend,sina,qq',
                type: '',
                shareData: {
                    title: data.title,
                    description: '主播：' + data.nickName,
                    url: getUrl(
                        'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-zhibojian/third-anchor-live.html',
                        {
                            anchorId: URLParams.anchorId,
                            carStyle: URLParams.carStyle,
                            kemuStyle: URLParams.kemuNum,
                        }
                    ),
                    iconUrl: data.avatar,
                },
            })
        },
        async getRoomDetail() {
            let resData = await getLiveRoomDetail({
                anchorId: URLParams.anchorId,
            })
            this.shareInit(resData)
        },
        async getTaskProgress() {
            let resData = await queryTaskProgress({
                activityId: URLParams.activityId,
                anchorId: URLParams.anchorId,
            })
            let taskList = resData.itemList.map(item => {
                return {
                    name: item.taskName,
                    code: item.taskCode,
                    point: item.point,
                    progress: item.progress,
                    time: item.taskTime,
                    type: item.taskType,
                    id: item.id,
                }
            })

            let k1 = keyBy(taskList, 'code')
            let k2 = pick(keyBy(taskMap, 'code'), keys(k1))
            let merged = merge(k1, k2)
            taskList = values(merged)

            if (!first) {
                first = true
                let item = find(taskList, {code: 'enterBroadCastRoom'})
                if (item.progress === 0) {
                    this.reportProgress(item)
                }
            }
            this.taskList = taskList
            this.starViewTask()
        },
        async getTotalPoint() {
            const token = await getAuthToken()
            if (!token) {
                return
            }
            let resData = await queryTotalPoint({
                activityId: URLParams.activityId,
                anchorId: URLParams.anchorId,
            })
            this.updateLotteryData({
                point: resData.value,
            })
        },
        getProgressClass(progress) {
            if (progress === 2) {
                return 'received'
            } else if (progress === 1) {
                return 'done'
            } else {
                return ''
            }
        },
        async starViewTask() {
            const token = await getAuthToken()
            if (!token) {
                return
            }
            // 依赖服务端 返回的观看时长任务的排序
            let item = find(this.taskList, {type: 2, progress: 0})
            if (item) {
                MCProtocol.livehouse.fetch.viewtimes({
                    callback: retData => {
                        console.log('viewtimes', retData)
                        if (retData.success) {
                            this.currentRemainder.code = item.code
                            this.startRemainder(
                                item.time,
                                retData.data.timestamp - retData.data.viewTimes * 1000
                            )
                        }
                    },
                })

                // TEST
                // this.currentRemainder.code = item.code
                // this.startRemainder(item.time, +new Date() - 96600 * 1000)
            }
        },
        startRemainder(duration, timestamp) {
            if (!duration) {
                this.currentRemainder.text = '00:00'
                return
            }
            let seconds = duration
            let date = +new Date()
            let gap = timestamp - date
            let end = date + gap + seconds * 1000
            clearInterval(this.timer)
            this.timer = setInterval(() => {
                date = +new Date()
                let remainder = end - date
                let remainderText = formatRemainTime(remainder, 'mm:ss')
                this.currentRemainder.text = remainderText
                if (remainder <= 0) {
                    clearInterval(this.timer)
                    let item = find(this.taskList, {code: this.currentRemainder.code})
                    this.reportProgress(item)
                }
            }, 16)
        },
        async receive(item) {
            await receivePoint(
                {
                    activityId: URLParams.activityId,
                    anchorId: URLParams.anchorId,
                    taskCode: item.code,
                    id: item.id,
                },
                {showErrorInfo: true}
            )
            this.getTaskProgress()
            this.getTotalPoint()
            MCProtocol.livehouse.notify.updatetask()
        },
        async reportProgress(item) {
            const token = await getAuthToken()
            if (!token) {
                return
            }
            let resData = await reportTaskProgress({
                activityId: URLParams.activityId,
                anchorId: URLParams.anchorId,
                bizId: URLParams.anchorId,
                taskCode: item.code,
            })
            if (resData.result) {
                item.progress = 1
                item.id = resData.id
                if (item.type === 2) {
                    this.starViewTask()
                }

                MCProtocol.livehouse.notify.updatetask()
            }
        },
        async doit(item) {
            const authToken = await getAuthToken()
            if (!authToken) {
                goLogin({refresh: true})
                return
            }
            let code = item.code
            if (code === 'followAnchor') {
                try {
                    await followAnchor({
                        anchorId: URLParams.anchorId,
                    })
                    MCProtocol.livehouse.notify.followstatus()
                } catch (error) {
                    return
                }
            } else if (code === 'sendDanMu') {
                try {
                    await new Promise(resolve => {
                        MCProtocol.livehouse.notify.sendmessages({
                            message: '我正在参与VIP盲盒活动',
                            callback: data => {
                                resolve(data)
                            },
                        })
                    })
                } catch (error) {
                    return
                }
            } else if (code === 'shareBroadCastRoom') {
                MCProtocol.Core.Web.menu()
                return
            } else if (code === 'open3dApp') {
                webOpen({
                    url: `http://jiakao3d.nav.mucang.cn/main?url=mucang-jiakao3d%3a%2f%2fk2home%3ffrom%3d0243`,
                })
                await new Promise(resolve => {
                    setTimeout(() => {
                        resolve()
                    }, 100)
                })
            } else if (code === 'browseExamRoom') {
                webOpen({
                    url: getUrl('https://laofuzi.kakamobi.com/ke2kaochang/selectExamRoom.html', {
                        selectMode: 'newk2kaochang',
                        selectCityCode: this.cityCode,
                    }),
                    orientation: '1',
                    titleBar: false,
                })
                await new Promise(resolve => {
                    setTimeout(() => {
                        resolve()
                    }, 100)
                })
            } else if (code === 'browseTechVideo') {
                webOpen({
                    url: `http://jiakao.nav.mucang.cn/exam-project-detail?kemu=kemu2&tiku=car&articleId=190&projectId=144&groupId=2`,
                })
                await new Promise(resolve => {
                    setTimeout(() => {
                        resolve()
                    }, 100)
                })
            }

            this.reportProgress(item)
        },
    },
}
</script>

<style lang="less" scoped>
.task-wrap {
    margin-top: 28px;
    .title {
        height: 130px;
        background: url(../images/title-bg.png) no-repeat;
        background-size: 100% auto;
        overflow: hidden;
        .text {
            margin: 26px auto 0;
            width: 346px;
            height: 76px;
            background: url(../images/text.png) no-repeat;
            background-size: 100% auto;
        }
        .text2 {
            margin: 26px auto 0;
            width: fit-content;
            height: 74px;
            background: url(../images/text3.png) no-repeat;
            background-size: 216px auto;
            padding-left: 220px;
            color: #ff7328;
            font-size: 60px;
            font-weight: 600;
            line-height: 74px;
            text-shadow: 4px 4px 0 #fff, -4px 4px 0 #fff, 4px -4px 0 #fff, -4px -4px 0 #fff;
        }
    }
}
.task-list {
    display: flex;
    padding: 0 20px 30px;
    flex-wrap: wrap;
    min-height: 572px;
    .item {
        width: 216px;
        height: 266px;
        margin: 20px 10px 0;
        padding: 0 18px 20px;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0px 4px 8px 0px #c4e5f0;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        &.done {
            background: linear-gradient(135deg, #ffc211, #ff9f3f);
            .name {
                color: #fff;
            }
        }
        &.received {
            background-image: url(../images/receive-icon.png);
            background-repeat: no-repeat;
            background-position: right bottom;
            background-size: 134px auto;
        }
        .intergral {
            position: absolute;
            top: 0;
            left: 0;
            height: 56px;
            line-height: 56px;
            padding: 0 14px 0 52px;
            background: url(../images/coin-icon.png) 12px 10px / 36px auto,
                linear-gradient(128deg, #fffae2 14%, #fff0bf 91%);
            background-repeat: no-repeat;
            border: 1px solid #ffffff;
            border-radius: 16px 0px 16px 0px;
            color: #ff7328;
            font-size: 30px;
            font-weight: 600;
            white-space: nowrap;
        }
        .name {
            font-size: 30px;
            color: #333333;
            font-weight: 500;
            text-align: center;
        }
        .desc {
            font-size: 20px;
            color: #6e6e6e;
            text-align: center;
        }
        .btn {
            position: absolute;
            left: 24px;
            bottom: 20px;
            width: 168px;
            height: 56px;
            line-height: 56px;
            border-radius: 44px;
            font-size: 28px;
            font-weight: 500;
            text-align: center;
            border: 1px solid #fff;
            &.normal {
                background: linear-gradient(135deg, #5ccfff, #239bff);
                box-shadow: 4px 4px 8px 0px rgba(255, 255, 255, 0.5) inset;
                color: #fff;
            }
            &.unused {
                border: 1px solid #04a5ff;
                color: #04a5ff;
            }
            &.done {
                background: #ffffff;
                color: #ff7328;
            }
        }
    }
}
</style>
