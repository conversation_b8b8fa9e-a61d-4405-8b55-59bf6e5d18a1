<template>
    <div @click.prevent class="page-container" :class="{'end-live': !canPlay}" :key="roomDetail.timestamp">
        <template v-if="canPlay">
            <anchorBar />
            <videoPlayer v-if="roomDetail.timestamp" :isShowFullScreen="isShowFullScreen"></videoPlayer>
            <div class="interact-wrap">
                <chat
                    v-if="roomDetail.timestamp || showErrorTips"
                    :recordPlay="roomStatus.status === 3 && roomStatus.playbackDone"
                    :showErrorTips="showErrorTips"
                />
            </div>
            <div class="app-in-look">
                驾考宝典内观看
                <wakeupBtn :targetUrl="targetUrl" />
            </div>
        </template>
        <template v-else>
            <anchorBox />
            <sessionList v-if="roomDetail.sessionId" />
        </template>
        <followPopup />
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {isMucang, URLParams, webOpen, isAndroid, addClass, removeClass, trackPageShow} from '../../utils/tools'
import roomStatusMixin from '../../utils/roomStatusMixin'
import {webClose} from '../../utils/jump'
import {liveRoomURL} from '../../utils/constant'
import {getLiveRoomDetail} from '../../server/topLesson'
import sessionList from './components/sessionList.vue'
import anchorBar from './components/anchorBar.vue'
import anchorBox from './components/anchorBox.vue'
import followPopup from './components/followPopup.vue'
import chat from '../index/components/chat.vue'
import videoPlayer from '../../components/videoPlayer.vue'
import wakeupBtn from '../../components/wakeupBtn'

// TODO 加载优化，进入直播页时，客户端只请求播放地址，而不是全部直播间信息

// http://jiakao.nav.mucang.cn/topLesson/live-channel?type=free
// http://jiakao.nav.mucang.cn/topLesson/live?id=3110
// 安卓8.3.2&ios8.3.6 新增参数透传

let pageName = '直播间页'
export default {
    mixins: [roomStatusMixin],
    components: {
        sessionList,
        anchorBar,
        anchorBox,
        followPopup,
        chat,
        videoPlayer,
        wakeupBtn,
    },
    data() {
        return {
            showErrorTips: false,
            isShowFullScreen: false,
            id: 0,
            anchorIdentity: 'thirdAnchor',
            liveRoomMode: true,
            pupopList: [],
            waitPupopList: [],
            targetUrl: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
        }
    },
    provide() {
        return {
            anchorIdentity: this.anchorIdentity,
            liveRoomMode: this.liveRoomMode,
            pupopList: this.pupopList,
            waitPupopList: this.waitPupopList,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'roomStatus']),
        canPlay() {
            return this.roomStatus.status === 1 || (this.roomStatus.status === 3 && this.roomStatus.playbackDone)
        },
    },
    watch: {
        'roomStatus.sessionId': {
            handler(val) {
                if (this.liveRoomMode) {
                    this.getRoomDetail()
                }
            },
        },
    },
    created() {
        if (isMucang) {
            webOpen({
                url: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
                closeCurrent: 1,
            })
            if (isAndroid) {
                webClose()
            }
        }
        stat.setPageName(pageName)
    },
    methods: {
        pingErrorHandler() {
            if (!this.roomStatus.sessionId) {
                this.showErrorTips = true
            }
        },
        async getRoomDetail() {
            try {
                let resData = await getLiveRoomDetail({
                    anchorId: URLParams.anchorId,
                    // 站外传入_version 可以获取快直播拉流地址
                    _version: isMucang ? '' : '8.29.0',
                })
                document.title = resData.title || resData.nickName
                if (resData.orientation === 1) {
                    addClass(document.body, 'vertical')
                    removeClass(document.body, 'portrait')
                }
                resData.streamOrigin = resData.source === 1 ? 3 : 1
                this.$store.commit('setRoomDetail', resData)
                this.showErrorTips = false
            } catch (error) {
                this.showErrorTips = true
            }
            trackPageShow({
                actionType: '展示',
            })
        },
    },
}
</script>

<style lang="less" scoped>
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
    background: #000;
}
body:not(.tablet) {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
}

.page-container {
    height: 100%;
    display: flex;
    padding-top: env(safe-area-inset-top);
    // background-image: url(https://jiakao-web.mc-cdn.cn/jiakao-web/2023/02/15/11/389ab047ac1a402582e7d7e2c69a4420.png);
    background-image: linear-gradient(90deg, #251d5a 0%, #5f1d22 100%);
    background-repeat: repeat-y;
    background-size: 100% auto;
    background-color: #1b1d22;

    &.end-live {
        flex-direction: column;
    }
}

@media (min-width: 689px) {
    .landscape .interact-wrap {
        flex: 1;
        overflow: hidden;
    }
}

/* 540+150=690 */
@media (max-width: 690px) {
    .landscape .interact-wrap {
        width: 4.5rem;
    }
}

.interact-wrap {
    display: flex;
    flex-direction: column;
    position: relative;
}
.app-in-look {
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    width: 224px;
    height: 58px;
    background: #04a5ff;
    border-radius: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    z-index: 1;
}
.vertical {
    .interact-wrap {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 520px !important;
    }
}
.portrait {
    // portrait
    body {
        background: url(../../assets/images/page-bg.png) repeat-y;
        background-size: 100% auto;
    }
    .page-container {
        flex-direction: column;
    }
    .interact-wrap {
        height: 0;
        flex: 1;
    }
}
.landscape {
    // landscape
    .interact-wrap {
        background: url(../../assets/images/page-bg.png) repeat-y;
        background-size: 100% auto;
    }
}
</style>
