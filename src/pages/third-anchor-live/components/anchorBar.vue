<template>
    <div class="anchor-bar">
        <div class="and">
            <div class="image">
                <img :src="roomDetail.avatar" />
            </div>
            <div class="text">{{ roomDetail.nickName }}</div>
            <button>
                + 关注
                <wakeupBtn :targetUrl="targetUrl" @trigger="follow" />
            </button>
        </div>
        <div class="app-in-look">
            <img src="../images/logo.png" /> 内观看
            <wakeupBtn :targetUrl="targetUrl" @trigger="appInLook" />
        </div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {trackEvent, URLParams} from '../../../utils/tools'
import {liveRoomURL} from '../../../utils/constant'
import wakeupBtn from '../../../components/wakeupBtn'

export default {
    components: {wakeupBtn},
    data() {
        return {
            targetUrl: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
        }
    },
    computed: {
        ...mapState(['roomDetail']),
    },
    methods: {
        follow() {
            let actionType = '点击'
            let actionName = '关注'

            // v8.24.0-埋点文档
            // 直播间页_点击关注
            trackEvent({actionType, actionName})
        },
        appInLook() {
            let actionType = '点击'
            let actionName = '驾考宝典内观看'

            // v8.24.0-埋点文档
            // 直播间页_点击驾考宝典内观看
            trackEvent({actionType, actionName})
        },
    },
}
</script>
<style lang="less" scoped>
.anchor-bar {
    background-color: #1c1c1c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 136px;
    background: rgba(0, 0, 0, 0.4);
    padding: 0 30px;
}
.and {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 46px;
    display: flex;
    padding: 8px;
    align-items: center;
    flex: 1;
    .image {
        width: 48px;
        height: 48px;
        border: 1px solid #fff;
        border-radius: 24px;
        overflow: hidden;
        img {
            width: 100%;
            display: block;
        }
    }
    .text {
        margin-left: 12px;
        color: #fff;
        font-size: 28px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        width: 20px;
    }
    button {
        position: relative;
        margin-left: 20px;
        width: 114px;
        height: 44px;
        background: linear-gradient(119deg, #ff5c64 14%, #ff283f 91%);
        border-radius: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 26px;
    }
}
.app-in-look {
    position: relative;
    text-align: center;
    line-height: 2;
    color: #fff;
    width: 264px;
    height: 64px;
    background: #04a5ff;
    border-radius: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-left: 20px;
    img {
        width: auto;
        height: 30px;
        margin-right: 4px;
    }
}

.vertical {
    .anchor-bar {
        position: absolute;
        width: 100%;
        z-index: 2;
    }
}
</style>
