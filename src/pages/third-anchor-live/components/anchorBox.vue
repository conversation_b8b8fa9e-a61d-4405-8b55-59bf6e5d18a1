<template>
    <div class="anchor-box">
        <div class="and">
            <div class="image">
                <img :src="roomDetail.avatar" />
            </div>
            <div class="text">{{ roomDetail.nickName }}</div>
        </div>
        <div class="btns">
            <div class="btn btn1">
                进入直播间
                <wakeupBtn :targetUrl="targetUrl" @trigger="toLive" />
            </div>
            <div class="btn btn2">
                + 关注
                <wakeupBtn :targetUrl="targetUrl" @trigger="follow" />
            </div>
        </div>
        <div class="t1">主播正在休息中～</div>
        <div class="t2" v-if="roomDetail.lastLiveTime">
            上次直播：{{ formatDate(roomDetail.lastLiveTime, 'yyyy-MM-dd hh:mm:ss') }}
        </div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {formatDate, trackEvent, URLParams} from '../../../utils/tools'
import {liveRoomURL} from '../../../utils/constant'
import wakeupBtn from '../../../components/wakeupBtn'

export default {
    components: {wakeupBtn},
    data() {
        return {
            targetUrl: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
        }
    },
    computed: {
        ...mapState(['roomDetail']),
    },
    methods: {
        formatDate(date, fmt) {
            return formatDate(date, fmt)
        },
        toLive() {
            let fragmentName1 = '直播结束'
            let actionType = '点击'
            let actionName = '进入直播间'

            // v8.24.0-埋点文档
            // 直播间页_直播结束_点击进入直播间
            trackEvent({fragmentName1, actionType, actionName})
        },
        follow() {
            let fragmentName1 = '直播结束'
            let actionType = '点击'
            let actionName = '关注'

            // v8.24.0-埋点文档
            // 直播间页_直播结束_点击关注
            trackEvent({fragmentName1, actionType, actionName})
        },
    },
}
</script>
<style lang="less" scoped>
.anchor-box {
    padding: 90px 30px 0;
    .t1 {
        margin-top: 40px;
        text-align: center;
        font-size: 30px;
        color: #fffbfb;
    }
    .t2 {
        margin-top: 10px;
        text-align: center;
        font-size: 26px;
        color: rgba(255, 251, 251, 0.6);
    }
}
.and {
    padding: 8px;
    .image {
        margin: 0 auto;
        width: 120px;
        border: 1px solid #fff;
        border-radius: 60px;
        overflow: hidden;
        img {
            width: 100%;
            display: block;
        }
    }
    .text {
        margin-left: 12px;
        color: #fff;
        font-size: 28px;
        text-align: center;
    }
}

.btns {
    display: flex;
    justify-content: space-evenly;
    padding: 30px 40px 0;
}
.btn {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    border-radius: 40px;
    width: 208px;
    height: 68px;
}
.btn1 {
    border: 1px solid rgba(255, 255, 255, 0.6);
    color: #fff;
}
.btn2 {
    background: linear-gradient(119deg, #ff5c64 14%, #ff283f 91%);
    color: #fff;
}
</style>
