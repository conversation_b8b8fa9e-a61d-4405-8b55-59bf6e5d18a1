<template>
    <popup class="follow-popup" :showClose="false" :show.sync="visible">
        <div class="wrap">
            <div class="btns">
                <div class="btn btn1" @click="close">取消</div>
                <div class="btn btn2">
                    关注Ta
                    <wakeupBtn :targetUrl="targetUrl" @trigger="follow" />
                </div>
            </div>
        </div>
    </popup>
</template>

<script>
import popup from '../../../components/dialog'
import {trackEvent, URLParams} from '../../../utils/tools'
import {liveRoomURL} from '../../../utils/constant'
import wakeupBtn from '../../../components/wakeupBtn'

export default {
    components: {
        popup,
        wakeupBtn,
    },
    data() {
        let seconds = 5 * 60
        return {
            visible: false,
            stayTime: seconds * 1000,
            targetUrl: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
        }
    },
    mounted() {
        clearInterval(this.monitor)
        let date = +new Date()
        this.monitor = setInterval(() => {
            let now = +new Date()
            let spend = now - date
            date = now
            this.stayTime -= spend
            if (this.stayTime <= 0) {
                clearInterval(this.monitor)
                this.visible = true
                let fragmentName1 = '关注弹窗'
                let actionType = '出现'

                // v8.24.0-埋点文档
                // 直播间页_关注弹窗_出现
                trackEvent({fragmentName1, actionType})
            }
        }, 16)
    },
    methods: {
        close() {
            this.visible = false
            let fragmentName1 = '关注弹窗'
            let actionType = '点击'
            let actionName = '取消'

            // v8.24.0-埋点文档
            // 直播间页_关注弹窗_点击取消
            trackEvent({fragmentName1, actionType, actionName})
        },
        follow() {
            let fragmentName1 = '关注弹窗'
            let actionType = '点击'
            let actionName = '关注'

            // v8.24.0-埋点文档
            // 直播间页_关注弹窗_点击关注
            trackEvent({fragmentName1, actionType, actionName})
        },
    },
}
</script>
<style lang="less" scoped>
.follow-popup {
    /deep/ .dialog {
        background: url(../images/follow-popup-bg.png) no-repeat;
        background-size: 562px 600px;
        width: 562px;
        height: 600px;
    }
    .wrap {
        padding-top: 440px;
    }

    .btns {
        display: flex;
        justify-content: space-around;
        padding: 40px 25px 0;
    }
    .btn {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 34px;
        flex: 1;
        border-radius: 46px;
        margin: 0 10px;
    }
    .btn1 {
        font-size: 32px;
        height: 88px;
        border: 1px solid #fc5977;
        color: #fa0431;
    }
    .btn2 {
        height: 88px;
        background: #fa0431;
        color: #ed0026;
        border: 1px solid transparent;
        color: #fff;
    }
}
</style>
