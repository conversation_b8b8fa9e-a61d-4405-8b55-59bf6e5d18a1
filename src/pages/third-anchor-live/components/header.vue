<template>
    <div class="header-container">
        <div class="m-header">
            <div class="title">{{ title }}</div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        title: String,
    },
}
</script>
<style>
.header-container {
    background-color: #302f2f;
}
.m-header {
    display: flex;
    align-items: center;
    color: #000;
    background-color: #fff;
    height: 80px;
}
.m-header .title {
    padding: 0 35px 0 55px;
    flex: 1;
    text-align: center;
    font-size: 30px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
