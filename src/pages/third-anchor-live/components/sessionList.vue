<template>
    <div class="session-list">
        <div class="more-title">更多直播</div>
        <div class="session-wrap">
            <div
                class="item"
                v-for="item in sessionList.slice(0, 3)"
                :key="item.anchorId"
            >
                <div class="dis">
                    <div class="live" v-if="item.status === 1"><i class="icon" />直播中</div>
                    <div
                        class="image"
                        :style="{'background-image': 'url(' + item.cover + ')'}"
                    ></div>
                    <div class="info">
                        <div class="avatar"><img :src="item.anchorData.avatar" /></div>
                        <div class="name">{{ item.anchorData.nickName }}</div>
                        <div class="view-num" v-if="item.pv">
                            <img src="../images/view.png" /> {{ item.pv }}
                        </div>
                    </div>
                </div>
                <div class="bot">
                    <div class="title">{{ item.title }}</div>
                    <div class="city" v-if="item.status === 1 && item.cityName">
                        <img src="../images/located.png" />{{ item.cityName }}
                    </div>
                    <div class="playback" v-else-if="item.sessionDesc">
                        <img src="../images/time.png" />{{ item.sessionDesc }}
                    </div>
                    <div class="tag" v-else-if="item.kemu === 2 || item.kemu === 3">
                        {{ item.kemu === 2 ? '科目二' : '科目三' }}
                    </div>
                </div>
                <wakeupBtn :targetUrl="targetUrl" @trigger="toLive" />
            </div>
            <div class="item more" v-if="sessionList.length > 3">
                <div class="avatar-list">
                    <div
                        class="avatar"
                        v-for="item in sessionList.slice(0, 4)"
                        :key="item.anchorId"
                    >
                        <div class="image">
                            <img :src="item.anchorData.avatar" />
                        </div>
                    </div>
                </div>
                <div class="desc">你可能感兴趣的直播</div>
                <div class="btn">更多直播 ></div>
                <wakeupBtn :targetUrl="targetUrl" @trigger="more" />
            </div>
        </div>
    </div>
</template>

<script>
import {mapState} from 'vuex'
import {URLParams, trackEvent, isMucang} from '../../../utils/tools'
import {liveRoomURL} from '../../../utils/constant'
import wakeupBtn from '../../../components/wakeupBtn'
import {getSessionList} from '../../../server/topLesson'

export default {
    components: {wakeupBtn},
    data() {
        return {
            sessionList: [],
            targetUrl: `${liveRoomURL}?anchorId=${URLParams.anchorId}`,
        }
    },
    computed: {
        ...mapState(['roomDetail']),
    },
    async mounted() {
        const resData = await getSessionList({
            kemu: URLParams.kemuNum,
            sessionId: this.roomDetail.sessionId,
            // 站外传8.26.0 有数据
            _version: isMucang || URLParams._version ? null : '8.26.0',
        })
        this.sessionList = resData.itemList
    },
    methods: {
        toLive() {
            let fragmentName1 = '直播结束'
            let actionType = '点击'
            let actionName = '推荐直播间'

            // v8.24.0-埋点文档
            // 直播间页_直播结束_点击推荐直播间
            trackEvent({fragmentName1, actionType, actionName})
        },
        more() {
            let fragmentName1 = '直播结束'
            let actionType = '点击'
            let actionName = '更多直播'

            // v8.24.0-埋点文档
            // 直播间页_直播结束_点击更多直播
            trackEvent({fragmentName1, actionType, actionName})
        },
    },
}
</script>
<style lang="less" scoped>
.session-list {
    margin: 30px 0 0;
    flex: 1;
    overflow-y: auto;
    .more-title {
        text-align: center;
        color: rgba(255, 251, 251, 0.8);
        font-size: 28px;
        position: relative;
        &::after {
            content: '';
            position: absolute;
            top: 20px;
            right: 70px;
            width: 210px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        &::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 70px;
            width: 210px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
    }
}
.session-wrap {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    padding: 0 10px;
    .item {
        position: relative;
        width: 345px;
        height: 312px;
        position: relative;
        overflow: hidden;
        margin: 20px 10px 0;
        border-radius: 12px;
        &.more {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            .avatar-list {
                margin-top: 48px;
                display: flex;
                flex-direction: row-reverse;
                justify-content: center;
            }
            .avatar {
                width: 70px;
                border-radius: 35px;
                overflow: hidden;
                margin-left: -14px;
            }
            .desc {
                margin-top: 10px;
                font-size: 26px;
                text-align: center;
                color: rgba(255, 255, 255, 0.8);
            }
            .btn {
                width: 200px;
                height: 60px;
                border: 1px solid rgba(255, 255, 255, 0.6);
                border-radius: 44px;
                margin: 30px auto 0;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fff;
            }
        }
        .image {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center center;
        }
    }
    .dis {
        height: 190px;
        position: relative;
    }
    .bot {
        height: 122px;
        background: #fff;
        padding: 10px 20px 0;
    }
    .info {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        .avatar {
            width: 70px;
            border-radius: 35px;
            overflow: hidden;
            position: absolute;
            left: 10px;
            bottom: -10px;
            border: 1px solid #ffffff;
        }
        .name {
            margin-left: 86px;
            font-size: 20px;
            color: #fff;
        }
        .view-num {
            margin-right: 10px;
            display: flex;
            align-items: center;
            font-size: 20px;
            color: #fff;
            img {
                width: 24px;
                margin-right: 8px;
            }
        }
    }
    .live {
        position: absolute;
        top: 14px;
        right: 10px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 16px;
        height: 32px;
        display: flex;
        align-items: center;
        color: #fff;
        font-size: 20px;
        padding: 0 12px 0 40px;
        .icon {
            width: 36px;
            height: 36px;
            background-image: url(../images/live.png);
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 20px 20px;
            background-color: #ff0745;
            border-radius: 50%;
            overflow: hidden;
            position: absolute;
            top: -2px;
            left: -2px;
        }
    }
    .city {
        display: flex;
        align-items: center;
        font-size: 24px;
        color: #a0a0a0;
        img {
            width: 20px;
            margin-right: 8px;
        }
    }
    .playback {
        display: flex;
        align-items: center;
        font-size: 24px;
        color: #a0a0a0;
        img {
            width: 42px;
            margin-right: 8px;
        }
    }
    .tag {
        background: #f2f6f8;
        border-radius: 4px;
        padding: 0 6px;
        line-height: 34px;
        font-size: 20px;
        color: #a0a0a0;
        width: max-content;
    }
    .title {
        font-size: 28px;
        color: #333;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 0;
    }
}
</style>
