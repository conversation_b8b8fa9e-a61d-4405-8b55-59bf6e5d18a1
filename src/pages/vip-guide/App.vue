<template>
    <vipGuidePopup :show.sync="visible" :isInDialog="true" />
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import vipGuidePopup from '../../components/vipGuidePopup'
import {URLParams, setEmbeddedHeight} from '../../utils/tools'

MCProtocol.register('Vip.enableOutsideCancel', function(config) {
    return config
})

let pageName = '会员引导'
export default {
    components: {vipGuidePopup},
    data() {
        return {
            visible: false,
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '会员引导'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(518)
    },
    mounted() {
        MCProtocol.Vip.enableOutsideCancel({
            enable: true,
        })
        this.visible = true
    },
}
</script>

<style lang="less">
</style>
