import Vue from 'vue'
import App from '../index/App.vue'
import store from '../../store/index'
import '../../utils/built-inComponent'
import {URLParams, statInit} from '../../utils/tools'
import {replace} from '../../utils/jump'
import {hasPermission, getLiveType} from '../../server/permission'

Vue.bizIdName = 'liveId'
Vue.pageMode = 'liveRoom'
Vue.prototype.taskList = []

statInit()

async function judgePermission() {
    let permissionKey = 'kqfdLive'
    let pageName = 'kqfd'
    let liveType = URLParams.liveType
    let free = false
    try {
        let resData = await getLiveType({
            liveSessionId: URLParams.id,
        })
        liveType = resData.value
        free = resData.free
    } catch (error) {}
    if (liveType === 7) {
        // 考前辅导
        permissionKey = 'kqfdLive'
        pageName = 'kqfd'
    } else if (liveType === 9) {
        // 专项攻克
        permissionKey = 'zxgkLive'
        pageName = 'zxgk'
    } else if (liveType === 10 || liveType === 11) {
        // 重点刷题班
        permissionKey = 'zdstbLive'
        pageName = 'zdst'
    }
    let isVip = await hasPermission(permissionKey)
    if (isVip || free) {
        new Vue({
            store,
            render: h => h(App),
        }).$mount('#app')
    } else {
        replace(`https://laofuzi.kakamobi.com/jkbd-vip/index/${pageName}.html` + location.search)
    }
}
judgePermission()
