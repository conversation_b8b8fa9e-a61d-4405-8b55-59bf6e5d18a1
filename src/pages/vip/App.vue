<template>
    <div class="vip-page">
        <div class="vip-home">
            <div @click="close" class="close"></div>
            <div class="scroll_view" v-if="popupDetail.groupKey">
                <div class="header">
                    <div class="line">
                        <span class="title">{{ popupDetail.popupTitle }}</span>
                        <span class="mark" v-if="popupDetail.popupTag">{{
                            popupDetail.popupTag
                        }}</span>
                    </div>
                    <div class="desc">{{ popupDetail.popupSubTitle }}</div>
                </div>
                <div class="content">
                    <div class="image">
                        <img :src="popupDetail.popupImg" />
                    </div>
                    <payList direction="hor" theme="hor1" :showPayForOther="true" v-if="isAndroid" />
                </div>
            </div>
            <div class="loading" v-else>
                <loading bgColor="#666" />
            </div>
            <div class="footer">
                <div class="line">
                    <span @click="openHelp" class="icon icon_kefu">客服</span>
                    <span @click="openDetail" v-if="popupDetail.detailUrl" class="icon icon_xiangqing">详情</span>
                    <div class="main">
                        <button @click="buyVip" :class="loaded ? '' : 'disabled'">
                            <span class="price">¥{{ showPrice }}确认协议并支付</span>
                            <span class="delPrice">限时特惠价</span>
                        </button>
                        <span class="mark" v-if="popupDetail.popupBuyTag">{{
                            popupDetail.popupBuyTag
                        }}</span>
                    </div>
                </div>
                <div class="line between">
                    <vipAgreement theme="red"></vipAgreement>
                    <couponItem :couponUsable="couponUsable" :goodsDetail="vipGoodsDetail" />
                </div>
            </div>
        </div>

        <helpPop :show.sync="helpPopupVisible" :fragmentName1="helpFragmentName1" @setHelpIcon="(val) => isShowHelpIcon = val" />
        <detainmentPupop
            :show.sync="detainmentPupopVisible"
            @buy="buyVip"
            :isInDialog="true"
            :title="popupDetail.popupTitle"
            :price="showPrice"
            :groupKey="vipGoodsDetail.groupKey"
        />
    </div>
</template>

<script>
import {mapState, mapGetters, mapMutations} from 'vuex'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {find} from 'lodash-es'
import {webClose} from '../../utils/jump'
import {getBestCoupon} from '../../utils/coupon'
import {getLessonDetail} from '../../server/topLesson'
import {getGoodsDetail} from '../../server/goods'
import {helpURL, replaceInviteUrl} from '../../utils/constant'
import {
    queryOrderStatus,
    createMobileOrder,
    getPayAfterStrategy,
} from '../../utils/payHelper'
import {
    webOpen,
    getAuthToken,
    isMucang,
    isAndroid,
    isIOS,
    URLParams,
    trackEvent,
    goLogin,
    setEmbeddedHeight,
    openNewVip,
    getUrl,
} from '../../utils/tools'
import loading from '../../components/loading'
import payList from '../../components/payList'
import vipAgreement from '../../components/vipAgreement'
import couponItem from '../../components/couponItem'

window.webviewFeature = 'vipWebview'

let pageName = '精品课VIP支付弹窗'
export default {
    components: {
        loading,
        helpPop: () => import('../../components/helpPop.vue'),
        detainmentPupop: () => import('../../components/detainmentPupop'),
        payList,
        vipAgreement,
        couponItem,
    },
    data() {
        return {
            isAndroid,
            popupDetail: {
                groupKey: '',
                popupBuyTag: '',
                popupTag: '',
                popupTitle: '',
                popupSubTitle: '',
                popupImg: '',
                detailUrl: '',
            },
            vipGoodsDetail: {
                price: null,
            },
            loaded: false,
            isShowHelpIcon: false,
            helpFragmentName1: '',
            helpPopupVisible: false,
            detainmentPupopVisible: false,
            viewHeight: 0,
            viewHeightCache: 0,
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    computed: {
        ...mapState(['selectCoupons']),
        ...mapGetters(['payList', 'checkdPayType', 'checkAgreement', 'readed']),
        couponUsable() {
            return this.selectCoupons[this.vipGoodsDetail.groupKey + '_selectCoupon'] || {}
        },
        showPrice() {
            let payPrice = this.vipGoodsDetail.price
            let couponPrice = this.couponUsable.priceCent
            if (couponPrice) {
                return Math.max((+(payPrice || 0) * 100) - (+(couponPrice || 0) * 100), 0) / 100
            } else {
                return payPrice
            }
        },
    },
    watch: {
        viewHeight(val) {
            setEmbeddedHeight(val)
        },
        helpPopupVisible(val) {
            if (!val) {
                this.viewHeight = this.viewHeightCache
            }
        },
    },
    async created() {
        document.title = '精品课'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        let readed =
            URLParams.agreementAccept === 'true' || +URLParams.agreementAccept === 1 || this.readed
        this.$store.commit('updatePayConfig', {readed})

        if (isMucang) {
            if (+URLParams.kemuNum === 4) {
                this.viewHeight = 340
            } else {
                this.viewHeight = 430
            }
        }

        await queryOrderStatus('vip', {
            toStatus: true,
            closeSelf: true,
            reFresh: false,
            toLogin: false,
            sendMessage: false,
            useNewApi: true,
        })
        queryOrderStatus('lesson', {
            toStatus: true,
            closeSelf: true,
            reFresh: false,
            toLogin: false,
            sendMessage: true,
            useNewApi: true,
        })
    },
    async mounted() {
        const resData = await getLessonDetail({
            carType: URLParams.carStyle,
            id: URLParams.id,
            patternCode: URLParams.patternCode,
            sceneCode: URLParams.sceneCode,
        })

        this.loaded = true
        this.popupDetail = resData.popupDetail || {}

        if (this.popupDetail.groupKey) {
            const goodsDetail = await getGoodsDetail({
                tiku: URLParams.carStyle,
                groupKey: this.popupDetail.groupKey,
            })
            this.getCoupon(goodsDetail)
            this.vipGoodsDetail = goodsDetail
        }

        let fragmentName1 = URLParams.fragmentName1 || 'unknown'
        let actionType = '点击'
        let actionName = '去支付'

        // 埋点梳理-驾考宝典-V8.9.0
        // xx_xx_点击去支付
        trackEvent({
            fragmentName1,
            actionType,
            actionName,
            lessonGroupId: URLParams.id,
            lessonId: URLParams.lessonId,
            payPathType: 0,
            fromItemCode: URLParams.fromItemCode,
        })
    },
    methods: {
        ...mapMutations([
            'updateSelectCoupons',
        ]),
        close(showDetainment = true) {
            if (isAndroid && showDetainment) {
                if (this.viewHeight < 430) {
                    this.viewHeight = 430
                }
                this.detainmentPupopVisible = !!showDetainment
            } else {
                webClose()
            }
        },
        async getCoupon(goodsDetail) {
            let coupon = await getBestCoupon(goodsDetail)

            if (coupon.couponCode) {
                this.updateSelectCoupons({[goodsDetail.groupKey + '_selectCoupon']: coupon})
            }
        },
        async openHelp() {
            let fragmentName1 = URLParams.fragmentName1 || 'unknown'

            if (this.isShowHelpIcon) {
                this.helpFragmentName1 = fragmentName1
                this.helpPopupVisible = true
                this.viewHeightCache = this.viewHeight
                this.viewHeight = 460
            } else {
                const authToken = await getAuthToken()
                if (!authToken) {
                    await goLogin()
                }
                webOpen({
                    url: helpURL,
                    titleBar: true,
                })
            }
        },
        openDetail() {
            if (this.popupDetail.detailUrl) {
                webOpen({
                    url: this.popupDetail.detailUrl,
                    titleBar: true,
                })
            }
        },
        async buyVip() {
            let fragmentName1 = URLParams.fragmentName1 || 'unknown'
            if (this.checkAgreement && !this.readed) {
                await this.$confirmProtocol()
            }
            this.payForVip(fragmentName1)
            let fragmentName2 = '支付弹窗'
            let actionType = '点击'
            let actionName = '确认支付'

            // 埋点梳理-驾考宝典-V8.9.0
            // xx_xx_支付弹窗_点击确认支付
            trackEvent({
                fragmentName1,
                fragmentName2,
                actionType,
                actionName,
                lessonGroupId: URLParams.id,
                lessonId: URLParams.lessonId,
                groupKey: this.vipGoodsDetail.groupKey,
                fromItemCode: URLParams.fromItemCode,
            })
        },
        payForVip(fragmentName1) {
            let payType, payChannel
            if (isIOS) {
                payType = 3
            } else {
                let pay = find(this.payList, {type: this.checkdPayType})
                payType = pay.type
                payChannel = pay.channelName
            }

            if (payType === 100) {
                openNewVip({
                    url: getUrl(replaceInviteUrl, {
                        channelCode: this.vipGoodsDetail.groupKey,
                    }),
                })
                return
            }
            createMobileOrder(
                {
                    sessionIds: this.vipGoodsDetail.sessionIdList.join(','),
                    appleId: this.vipGoodsDetail.appleId,
                    tiku: URLParams.carStyle,
                    payType,
                    payChannel,
                    couponCode: this.couponUsable.couponCode,
                    activityType: this.vipGoodsDetail.activityType,
                    groupKey: this.vipGoodsDetail.groupKey,
                    squirrelGoodsInfo: this.vipGoodsDetail.squirrelGoodsInfo,

                    ref: 'JIAKAOBAODIAN',
                    page: stat.getPageName(),
                    statExtra: '支付',
                    fragmentName1: fragmentName1,
                    payPathType: 0,
                    pageData: {
                        groupKey: this.vipGoodsDetail.groupKey,
                        lessonId: URLParams.lessonId,
                    },
                },
                getPayAfterStrategy(true, 'vip')
            )
        },
    },
}
</script>

<style lang="less">
@import '../../assets/styles/main';
@import '../../assets/styles/variables';
html,
body {
    height: 100%;
    overflow: hidden;
}

.vip-page {
    background: #ffffff;
    height: 100%;
    .vip-home {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        .close {
            position: absolute;
            right: 24px;
            top: 28px;
            width: 40px;
            height: 40px;
            background: url(./images/<EMAIL>) no-repeat;
            background-size: 40px 40px;
            z-index: 1;
        }
        .loading {
            flex: 1;
            position: relative;
        }
        .scroll_view {
            overflow-y: auto;
        }
        .header {
            padding: 40px 0 0 40px;
            .line {
                display: flex;
                align-items: center;
            }
            .title {
                .fontSizeWithElder(40px);
                color: #333;
                font-weight: bold;
            }
            .mark {
                color: #29c0fb;
                border: 1px solid #29c0fb;
                border-radius: 6px;
                .fontSizeWithElder(22px);
                line-height: 32px;
                padding: 0 6px;
                margin-left: 10px;
            }
            .desc {
                .fontSizeWithElder(28px);
                color: #666;
            }
        }
        .content {
            margin-top: 38px;
            .image {
                margin: 0 30px;
            }
            .pay-list {
                padding: 20px 30px;
            }
        }
        .footer {
            padding-bottom: calc(constant(safe-area-inset-bottom) - 20px);
            padding-bottom: calc(env(safe-area-inset-bottom) - 20px);
            .agreement {
                .fontSizeWithElder(24px);
            }
            .line {
                padding: 10px 30px;
                display: flex;
                &.between {
                    justify-content: space-between;
                }
            }
            .icon {
                margin-right: 30px;
                background-repeat: no-repeat;
                background-size: 44px 44px;
                background-position: center 8px;
                .fontSizeWithElder(24px);
                color: #333;
                padding-top: 50px;
                &.icon_kefu {
                    background-image: url(./images/<EMAIL>);
                }
                &.icon_xiangqing {
                    background-image: url(./images/<EMAIL>);
                }
            }
            .main {
                flex: 1;
                position: relative;
                button {
                    width: 100%;
                    height: 80px;
                    background: linear-gradient(315deg, #ff4a40 0%, #ff7d76 100%);
                    border-radius: 40px;
                    color: #fff;
                    border: none;
                    text-align: center;
                    &.disabled {
                        background: #999;
                    }
                    .price {
                        display: block;
                        .fontSizeWithElder(32px);
                        line-height: 1.2;
                    }
                    .delPrice {
                        display: block;
                        .fontSizeWithElder(20px);
                        line-height: 1.2;
                    }
                }
                .mark {
                    position: absolute;
                    top: -30px;
                    right: 8px;
                    padding: 0 20px;
                    text-align: center;
                    background: linear-gradient(111deg, #ffd74f 0%, #ffc634 100%);
                    border-radius: 0px 20px 0px 16px;
                    border-radius: 0 20px 0 16px;
                    color: #8c2801;
                    .fontSizeWithElder(22px);
                    line-height: 40px;
                }
            }
        }
    }
}
</style>
