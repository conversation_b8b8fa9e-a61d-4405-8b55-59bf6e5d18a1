<template>
    <div class="subscribe-dialog">
        <div class="close" @click="close"></div>
        <div class="t1">直播预约成功!</div>
        <div class="t2">主播开播时会发送开播通知，订阅微信提醒，主播开播早知道！</div>
        <div class="image"><img src="../../assets/images/weixin-bg.png"></div>
        <div class="btn" @click="subscribe">订阅微信提醒</div>
    </div>
</template>

<script>
import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {URLParams, setEmbeddedHeight, toast, webOpen, trackEvent} from '../../utils/tools'
import {webClose} from '../../utils/jump'
import {subscribeNotify} from '../../server/topLesson'
import {getUserInfoForWX} from '../../server/permission'

MCProtocol.register('Vip.enableOutsideCancel', function(config) {
    return config
})

let pageName = '订阅直播消息'
export default {
    data() {
        return {
        }
    },
    provide() {
        return {
            pupopList: [],
        }
    },
    created() {
        document.title = '订阅直播消息'
        stat.setPageName(URLParams.pageName || URLParams.fromPage || pageName)
        setEmbeddedHeight(348)
    },
    mounted() {
        MCProtocol.Vip.enableOutsideCancel({
            enable: true,
        })

        let fragmentName1 = '直播预约成功弹窗'
        let actionType = '出现'

        // 精品课直播间页_直播预约成功弹窗_出现
        trackEvent({fragmentName1, actionType})
    },
    methods: {
        close() {
            webClose()
        },
        async subscribe() {
            subscribeNotify({
                channel: 2,
                anchorId: URLParams.anchorId,
            })
            let resData = await getUserInfoForWX({
                thirdPartyType: 'jiakao_wx_service',
            })
            if (resData.thirdPartyId) {
                toast('订阅成功，如需取消订阅，可打开APP设置页-微信提醒菜单关闭此通知服务。')
            } else {
                webOpen({
                    url: 'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-bind/guide.html',
                })
            }

            let fragmentName1 = '直播预约成功弹窗'
            let actionType = '点击'
            let actionName = '订阅微信提醒'

            // 精品课直播间页_直播预约成功弹窗_点击订阅微信提醒
            trackEvent({fragmentName1, actionType, actionName})
        },
    },
}
</script>

<style lang="less">
.subscribe-dialog {
    position: relative;
    background: linear-gradient(180deg,#e3f8ff, #ffffff 23%);
    overflow: hidden;
    .close {
        position: absolute;
        z-index: 2;
        right: 14px;
        top: 14px;
        width: 50px;
        height: 50px;
        background: url(../../assets/images/<EMAIL>) no-repeat;
        background-size: 50px 50px;
    }
    .t1 {
        font-size: 40px;
        text-align: center;
        font-weight: bold;
        margin-top: 40px;
    }
    .t2 {
        font-size: 28px;
        text-align: center;
        margin: 6px 50px 0 60px;
        color: #6E6E6E;
    }
    .image {
        width: 700px;
        margin: 28px auto 0;
    }
    .btn {
        margin: 20px auto 0;
        width: 690px;
        height: 80px;
        background: linear-gradient(135deg,#67cef8, #1e74fa);
        border-radius: 440px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 30px;
        color: #fff;
    }
}
</style>
