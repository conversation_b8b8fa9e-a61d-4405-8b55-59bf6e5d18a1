import {once} from 'lodash-es'
import {httpRequest, memoizeAsync} from '../utils/tools'

export function getValidActive(params, config) {
    return httpRequest({
        url: 'api/open/promotion/get-kemu-all-activity-info.htm',
        params: params,
        host: 'squirrel',
        ...config,
    }).then((data) => {
        return {
            ...data,
            hasActive: data.serverTime > data.startTime && data.serverTime < data.endTime,
        }
    })
}

export const getValidActiveData = once(getValidActive)

export function queryTaskProgress(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/query-task-progress.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function receivePoint(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/receive-point.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function reportTaskProgress(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/report-task-progress.htm',
        params: params,
        host: 'activity',
        method: 'post',
        ...config,
    })
}

export function queryTotalPoint(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/query-total-point.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}
export function queryPointAccount(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/query-point-account.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}
export function queryRecentPresentShip(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/query-recent-present-ship.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}
export function drawPresent(params, config) {
    return httpRequest({
        url: 'api/open/broad-cast-room-task/draw-present.htm',
        params: params,
        host: 'activity',
        method: 'post',
        ...config,
    })
}

export function getActiveConfig(params, config) {
    return httpRequest({
        url: 'api/open/live-generic-config/get-config.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
    // MOCK active12config
    // .then(() => {
    //     let resData = {
    //         value: '{"isOpen":true,"time":10,"groupkey":"channel_ke1ke4"}',
    //     }
    //     return Promise.resolve(resData)
    // })
}

export function fillInAddress(params, config) {
    return httpRequest({
        url: 'api/open/live-activity/fill-in-address.htm',
        params: params,
        host: 'monkey',
        method: 'post',
        ...config,
    })
}

export function getPrizeList(params, config) {
    return httpRequest({
        url: 'api/open/live-activity/get-prize-list.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getActivityInfo(params, config) {
    return httpRequest({
        url: 'api/open/live-activity/get-activity-info.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

function _getActivityData(params, config) {
    return httpRequest({
        url: 'api/open/live-activity/get-activity-data.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export const getActivityData = memoizeAsync(_getActivityData)

export function submitAnswer(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/report.htm',
        params: params,
        host: 'activity',
        method: 'post',
        ...config,
    })
}

export function getQuestionResultCount(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/single-answer-statistics.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getExamOverview(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/user-score-detail.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getExamDetail(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/user-exam-detail.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getNoShipPresent(params, config) {
    return httpRequest({
        url: 'api/open/user-present/find-user-ship-present.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function addSharePresent(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/add-share-present.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getBaseInfo(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/get-activity-info.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getActivityId(params, config) {
    return httpRequest({
        url: 'api/open/live-interaction/get-activity-id.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getExamSituation(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/get-user-answer-situation.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getAnswerSheet(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/get-user-answer-sheet.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getRankList(params, config) {
    return httpRequest({
        url: 'api/open/online-exam/get-exam-rank.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getBadgeData(params, config) {
    return httpRequest({
        url: 'api/open/badge/get-badge-data.htm',
        params: params,
        host: 'misc',
        ...config,
    })
}

export function getPromotionActivityInfo(params, config) {
    return httpRequest({
        url: 'api/open/promotion-activity/base-info-by-id.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getPromotionActivityRule(params, config) {
    return httpRequest({
        url: 'api/open/promotion-activity/get-activity-rule.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}

export function getGuidedConditionMatch(params, config) {
    return httpRequest({
        url: 'api/open/live-room/guided-pop-ups-condition-match.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}