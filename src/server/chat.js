import {httpRequest, getUUID, URLParams} from '../utils/tools'

export function getRoomInfo(params, config) {
    if (!URLParams._deviceId) {
        params._deviceId = getUUID()
    }
    return httpRequest({
        url: 'api/web/live/danmu-config.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function getIntoSendSysMsg(params, config) {
    return httpRequest({
        url: 'api/web/live-sys-message/get-into-send-sys-msg.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function sendIntoSendSysMsg(params, config) {
    return httpRequest({
        url: 'api/web/live-sys-message/send-into-send-sys-msg.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getQuicklyCommentList(params, config) {
    return httpRequest({
        url: 'api/web/live-quickly-comment/get-quickly-comment-list.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getQuicklyBarrages(params, config) {
    return httpRequest({
        url: 'api/web/live-room/get-quick-barrages.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function followAnchor(params, config) {
    return httpRequest({
        url: 'api/open/live-fans/follow-anchor.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function sendInteractionIfNeeded(params, config) {
    return httpRequest({
        url: 'api/open/live-interaction/send-interaction-if-needed.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}