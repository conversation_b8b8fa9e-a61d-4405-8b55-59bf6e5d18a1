import {flatten} from 'lodash-es'
import {httpRequest} from '../utils/tools'

export function getSpecialStrengthen(params, config) {
    return httpRequest({
        url: 'api/open/special-strengthen/get-by-tag-id.htm',
        params: params,
        host: 'panda',
        ...config,
    }).then(res => {
        res.coursewareDataList = flatten(
            (res.coursewareDataList || []).map(item => item.imageUrls || [])
        )
            .map(item => item.imageUrl)
            .slice(0, 2)
        return res
    })
}
