import {once} from 'lodash-es'
import {httpRequest} from '../utils/tools'

export function getConfig(params, config) {
    return httpRequest({
        url: 'api/web/v4/config/get.htm',
        params: params,
        host: 'config',
        ...config,
    })
}
export function getAskStatus(params, config) {
    return httpRequest({
        url: 'api/open/want-ask/get-status.htm',
        params: params,
        host: 'misc',
        ...config,
    })
}

export function provide(params, config) {
    return httpRequest({
        url: 'api/open/sys/provide.htm',
        params: params,
        host: 'squirrel',
        ...config,
    })
}

export function getAllAbTest(params, config) {
    return httpRequest({
        url: 'api/open/ab-test/get-all-ab-test.htm',
        params: params,
        host: 'swallow',
        ...config,
    })
}

export function getSwallowConfig(params, config) {
    return httpRequest({
        url: 'api/web/config/get-config.htm',
        params: params,
        host: 'swallow',
        ...config,
    })
}

export const getConfigCache = once(getConfig)

export const getPayDetainmentConfig = once(async () => {
    try {
        const resData = await getSwallowConfig({key: 'jk_live_sales_wechat'})
        resData.goodsList = (resData?.goodsList || '').split(',')
        return resData
    } catch (error) {
        return false
    }
})