// import {find} from 'lodash-es'
import {httpRequest, formatPriceRecursion, formatPrice, getProvide} from '../utils/tools'
import {
    newGetOrderStatus,
    newGetGroupSessionInfo,
    newCreateMobileOrder,
    newGetUserCoupons,
    newGetPromotionSessions,
    newComparePrice,
} from './newGoods'
import {getPromotionActivityInfo} from './active'

export async function getOrderStatus(params, config) {
    if (await getProvide()) {
        return newGetOrderStatus(params, config)
    } else {
        return httpRequest({
            url: 'api/web/goods/get-order-status.htm',
            params: params,
            host: 'sirius',
            ...config,
        })
    }
}

export async function bindGoods(params, config) {
    if (await getProvide()) {
        return httpRequest({
            url: 'api/open/order/bind.htm',
            params: {
                orderNos: params.orderNumbers,
            },
            host: 'squirrel',
            method: 'post',
            ...config,
        })
    } else {
        return httpRequest({
            url: 'api/open/goods/bind.htm',
            params: params,
            host: 'sirius',
            ...config,
        })
    }
}
export async function checkOrderBinding(params, config) {
    if (await getProvide()) {
        return httpRequest({
            url: 'api/open/order/is-bound.htm',
            params: {
                orderNos: params.orderNumber,
            },
            host: 'squirrel',
            ...config,
        }).then(resData => {
            let data = resData.itemList
            data = data[0]
            return {
                bind: data.bound,
            }
        })
    } else {
        return httpRequest({
            url: 'api/web/goods/binding.htm',
            params: params,
            host: 'sirius',
            ...config,
        })
    }
}
export async function getSessionInfo(params, config) {
    let activityData = {}
    let {activityId, activityType, activityExt} = {}
    if (params.promotionActivityId && params.promotionActivityId !== '0') {
        try {
            activityData = await getPromotionActivityInfo({
                activityId: params.promotionActivityId,
            })
        } catch (error) {}
        activityId = params.promotionActivityId
        activityType = activityData.type
        activityExt = activityData.activityExt
    }
    let resData
    resData = await getGoodsDetail(params, config)
    if (activityType === 21) {
        resData.promotionActivityData = {
            activityId,
            activityExt,
        }
    }
    return Object.assign({promotionActivityData: {}}, resData)
}
export async function getGoodsDetail(params, config) {
    if (await getProvide()) {
        return newGetGroupSessionInfo({...params, groupKeys: [params.groupKey]}, config).then(
            goodInfoList => {
                return goodInfoList[0]
            }
        )
    } else {
        return httpRequest({
            url: 'api/open/goods-session-group/detail.htm',
            params: params,
            host: 'sirius',
            ...config,
        }).then(resData => {
            let data = formatPriceRecursion(resData)
            return data
        })
    }
}
export async function getUserPromotion(params, config) {
    if (await getProvide()) {
        return newGetPromotionSessions(params)
    } else {
        return httpRequest({
            url: 'api/open/sales-promotion/get-user-promotion.htm',
            params: params,
            host: 'sirius',
            ...config,
        }).then(resData => {
            let data = formatPriceRecursion(resData)
            return data
        })
    }
}
export function getPromotionExtraInfo(params, config) {
    return httpRequest({
        url: 'api/web/sales-promotion/query-extra-info.htm',
        params: params,
        host: 'sirius',
        ...config,
    })
}
export async function createGoodsOrder(params, config) {
    if (await getProvide()) {
        return newCreateMobileOrder(params, config)
    } else {
        return httpRequest({
            url: 'api/web/goods/create-mobile-order.htm',
            params: params,
            host: 'sirius',
            ...config,
        })
    }
}
export async function getCouponList(params, config) {
    if (await getProvide()) {
        return newGetUserCoupons(params, config)
    } else {
        return httpRequest({
            url: 'api/web/goods-coupon/get-goods-coupon-user-list.htm',
            params: {
                tiku: params.tiku,
                sessionIds: params.sessionIds,
                groupKey: params.groupKey,
            },
            host: 'sirius',
            ...config,
        })
        // MOCK
        // .catch(() => {
        //     let resData = {
        //         itemList: [
        //             {
        //                 canUse: true,
        //                 couponCode: 123123,
        //                 validEndTime: +new Date() + 1000000,
        //                 goodsCouponData: {
        //                     priceCent: 20,
        //                 },
        //             },
        //         ],
        //     }
        //     return Promise.resolve(resData)
        // })
    }
}
export async function comparePrice(params, config) {
    if (await getProvide()) {
        return newComparePrice(params, config)
    } else {
        return httpRequest({
            url: 'api/open/goods-session-group/compare-price.htm',
            params: params,
            host: 'sirius',
            ...config,
        }).then(data => {
            const itemList = data.comparedGoodsList || []
            const priceDiff = data.priceDiff
            let allPrice = 0
            for (let i = 0; i < itemList.length; i++) {
                if (itemList[i].price) {
                    allPrice += itemList[i].price
                    itemList[i].price = formatPrice(itemList[i].price)
                } else {
                    itemList[i].price = ''
                }
            }
            return {
                allPrice: formatPrice(allPrice),
                groupItems: itemList,
                savePrice: formatPrice(priceDiff),
            }
        })
        // MOCK
        // .catch(() => {
        //     let resData = {
        //         itemList: [
        //             {
        //                 canUse: true,
        //                 couponCode: 123123,
        //                 validEndTime: +new Date() + 1000000,
        //                 goodsCouponData: {
        //                     priceCent: 20,
        //                 },
        //             },
        //         ],
        //     }
        //     return Promise.resolve(resData)
        // })
    }
}
