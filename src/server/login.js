import {LOCATION} from '../utils/constant'
import {httpRequest} from '../utils/tools'

export function smsCheck(params, config) {
    return httpRequest({
        url: 'api/web/v3/login-sms/check.htm',
        params: {
            ...params,
            _appName: 'jiakaobaodian',
            _platform: 'web',
            _authVersion: '1.5',
        },
        host: 'auth',
        method: 'post',
        ...config,
    }).then(data => {
        return data.smsId
    })
}

export async function smsLogin(params, config) {
    await httpRequest({
        url: 'api/web/v3/login-sms/login.htm',
        params: {
            ...params,
            _appName: 'jiakaobaodian',
            _platform: 'web',
            _authVersion: '1.5',
        },
        host: 'auth',
        method: 'post',
        ...config,
    }).then(data => {
        localStorage.setItem(LOCATION.USERINFO, JSON.stringify(data))
        localStorage.setItem(LOCATION.AUTHTOKEN, data.authToken)
        return data.smsId
    })
}
