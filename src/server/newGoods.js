import {once} from 'lodash-es'
import {httpRequest, formatPrice, isAndroid, isMucang} from '../utils/tools'
import {getSwallowConfig} from './common'

export const ActivityType = {
    // 升级优惠
    upgrade: 'upgrade',
}
const promotionTypeMap = {
    3: 'lucky_bag',
    5: 'zhibo_new_comer',
    6: 'zhibo_not_pay',
    7: 'order_rush',
}

export async function newGetOrderStatus(params, config) {
    return httpRequest({
        url: 'api/open/order/get-order-status.htm',
        params: {
            orderNos: params.orderNumber,
        },
        host: 'squirrel',
        ...config,
    }).then(resData => {
        let data = resData.itemList.map(item => {
            return {
                orderNo: item.orderNo,
                orderStatus: item.orderStatus === 1 ? 0 : item.orderStatus,
            }
        })
        data = data[0] || {}
        return {
            status: data.orderStatus,
        }
    })
}

export async function newGetGroupSessionInfo(params, config) {
    let groupKeys = params.groupKeys
    delete params.groupKeys
    const goodInfoList = []
    await Promise.all([
        httpRequest({
            url: 'api/web/sales/get-channel-goods-detail.htm',
            params: {
                ...params,
                channelCodes: groupKeys.join(','),
            },
            host: 'squirrel',
            ...config,
        }),
        httpRequest({
            url: 'api/web/sales/get-channel-goods-price.htm',
            params: {
                ...params,
                channelCodes: groupKeys.join(','),
            },
            host: 'squirrel',
            ...config,
        }),
        getNoIpaList(),
    ]).then(async ([goodRes, priceRes, noIpaList]) => {
        const sessionIdsRes = await httpRequest({
            url: 'api/web/mapping/get-session-ids.htm',
            params: {
                packageCodes: goodRes.itemList.map(item => item.goods.dataCode).join(','),
            },
            host: 'squirrel',
        })
        let PTGoodsList = noIpaList?.PTGoodsList
        PTGoodsList = (PTGoodsList || '').split(',')

        goodRes.itemList.forEach((item, index) => {
            const isTraining = PTGoodsList.indexOf(groupKeys[index]) > -1
            const priceItem = priceRes.itemList[index]
            const sessionIdsItem = sessionIdsRes.itemList[index]
            const payPrice = priceItem.salePrice

            let inActivity
            if (item.promotion?.discountStatus === 2) {
                const preDiscountPrice = priceItem.channelPrice
                const discountedPrice = String(Math.floor(+preDiscountPrice - +payPrice))

                inActivity = {
                    preDiscountPrice,
                    discountedPrice,
                    discountStartTime: item.promotion?.discountStartTime,
                    discountEndTime: item.promotion?.discountEndTime,
                }
            }
            const goodInfo = {
                entityGoods: isTraining,
                goodsType: isTraining ? 'training' : 'vip',
                appleId: priceItem.appleGoodsId,
                bought: priceItem.userBoughtInfo.effected,
                expired: priceItem.userBoughtInfo.expired,
                sessionIdList: [groupKeys[index]],
                price: payPrice,
                originalPrice: priceItem.channelPrice,
                suggestedPrice: priceItem.suggestedPrice,
                upgrade: priceItem.activityType === ActivityType.upgrade,
                name: item.goods.goodsName,
                validDays: item.goods.durationDescription.replace(/[^0-9]/g, ''),
                groupKey: groupKeys[index],
                inActivity,
                discountInfo: {
                    preDiscountApplePrice: priceItem.channelPrice,
                    preDiscountPrice: priceItem.channelPrice,
                    ...item.promotion,
                },

                tips: item.uiConfig,
                channelCode: item.channelCode,
                squirrelGoodsInfo: {
                    sessionIds: sessionIdsItem.sessionIds,
                    appleGoodsId: priceItem.appleGoodsId,
                    goodsDataName: item.goods.goodsName,
                    goodsDataCode: item.goods.dataCode,
                    goodsDataType: item.goods.dataType,
                    channelCode: item.channelCode,
                    activityType: priceItem.activityType,
                    activityCode: priceItem.activityCode,
                    priceConfigCode: priceItem.priceConfigCode,
                },
            }

            goodInfoList.push(goodInfo)
        })
    })
    return goodInfoList
}

export async function newCreateOrder(params) {
    let squirrelGoodsInfo = params.squirrelGoodsInfo
    delete params.squirrelGoodsInfo
    return httpRequest({
        url: 'api/web/order/place-order.htm',
        host: 'squirrel',
        method: 'post',
        params: {
            ...squirrelGoodsInfo,
            orderRef: params.ref,
            typeCode: params.payType,
            platformType: 'mobile',
            ...params,
        },
    }).then(data => {
        data.orderNumber = data.orderNo
        return data
    })
}

export async function newCreateMobileOrder(params) {
    let url
    if (!isMucang || isAndroid || params.entityGoods) {
        url = 'api/web/order/place-order.htm'
    } else {
        url = 'api/open/order/place-apple-order.htm'
    }
    let squirrelGoodsInfo = params.squirrelGoodsInfo
    delete params.squirrelGoodsInfo
    // 移除params.activityType，以squirrelGoodsInfo里面的activityType为准
    delete params.activityType
    return httpRequest({
        url,
        host: 'squirrel',
        method: 'post',
        params: {
            ...squirrelGoodsInfo,
            orderRef: params.ref,
            typeCode: params.payType,
            platformType: 'mobile',
            ...params,
        },
    }).then(data => {
        data.orderNumber = data.orderNo
        return data
    })
}

export const newGetUserCoupons = async (params, config) => {
    const res = await httpRequest({
        url: 'api/web/coupon/list.htm',
        host: 'squirrel',
        params: {
            goodsDataType: params.dataType,
            goodsDataCode: params.dataCode,
            tiku: params.tiku,
        },
        ...config,
    })

    const coupons = res.itemList.map(item => ({
        couponUniqKey: item.couponTplCode,
        couponCode: item.couponCode,
        priceCent: item.discountValue * 100,
        goodsCouponData: {
            name: item.couponName,
            desc: item.description,
            priceCent: item.discountValue * 100,
            uniqKey: item.couponTplCode,
        },
        discountType: item.discountType,
        canUse: item.canUse,
        expired: item.status === 3,
        used: item.status === 2,
    }))

    return {itemList: coupons}
}

export async function newGetPromotionSessions(params) {
    const res = await httpRequest({
        url: 'api/web/promotion/get-promotion-detail.htm',
        host: 'squirrel',
        params: {
            ...params,
            activityType: promotionTypeMap[params.promotionType],
        },
    })

    if (res.itemList.length === 0) {
        return res
    }

    const sessionIdsRes = await httpRequest({
        url: 'api/web/mapping/get-session-ids.htm',
        host: 'squirrel',
        params: {
            packageCodes: res.itemList.map(item => item.goods.dataCode).join(','),
        },
    })
    let promotion = res.itemList.map((item, index) => {
        return {
            appleId: item.goodsPrice.appleGoodsId,
            bought: !item.canBuy,
            canBuy: item.canBuy,
            groupKey: item.channelCode,
            kemu: +item.promotionDetail.kemu,
            label: null,
            name: item.goods.goodsName,
            promotionDetail: {
                ...item.promotionDetail,
                promotionPriceInfo: {
                    ...item.goodsPrice,
                    originalPrice: item.goodsPrice.channelPrice,
                    price: item.goodsPrice.salePrice,
                },
            },
            sessionIdList: [],
            upgrade: item.goodsPrice.activityType === ActivityType.upgrade,
            upgradeStrategyCode: item.goodsPrice.upgradeCode,
            validDays: item.goods.durationDescription,
            squirrelGoodsInfo: {
                sessionIds: sessionIdsRes[index],
                appleGoodsId: item.goodsPrice.appleGoodsId,
                goodsDataName: item.goods.goodsName,
                goodsDataCode: item.goods.dataCode,
                goodsDataType: item.goods.dataType,
                channelCode: item.channelCode,
                activityType: item.goodsPrice.activityType,
                activityCode: item.goodsPrice.activityCode,
                priceConfigCode: item.goodsPrice.priceConfigCode,
            },
        }
    })
    return {itemList: promotion}
}

export const newComparePrice = async (params, config) => {
    let [goodRes, priceRes] = await Promise.all([
        httpRequest({
            url: 'api/web/sales/get-channel-goods-detail.htm',
            params: {
                ...params,
                channelCodes: params.groupKey,
            },
            host: 'squirrel',
            ...config,
        }),
        httpRequest({
            url: 'api/web/sales/get-channel-goods-price.htm',
            params: {
                ...params,
                channelCodes: params.groupKey,
            },
            host: 'squirrel',
            ...config,
        }),
    ])

    const goodsItem = goodRes.itemList[0]
    const priceItem = priceRes.itemList[0]
    const comparePrice = await httpRequest({
        url: 'api/web/price-compare/list.htm',
        params: {
            ...params,
            channelCode: goodsItem.channelCode,
            dataCode: goodsItem.goods.dataCode,
            dataType: goodsItem.goods.dataType,
            activityType: priceItem.activityType,
            activityCode: priceItem.activityCode,
        },
        host: 'squirrel',
        ...config,
    })

    let allPrice = 0
    if (comparePrice && comparePrice.priceCompareList) {
        comparePrice.priceCompareList.forEach(item => {
            item.price = item.price ? item.price * 100 : 0
            allPrice += item.price
            item.price = item.price ? formatPrice(item.price) : 0
        })
    }

    return (
        comparePrice && {
            allPrice: formatPrice(allPrice),
            groupItems: comparePrice.priceCompareList,
            savePrice: formatPrice(+allPrice - comparePrice.price * 100),
        }
    )
}

export const getNoIpaList = once(async () => {
    try {
        const resData = await getSwallowConfig({key: 'jk_non_iap_goods_list'})
        return resData
    } catch (error) {
        return false
    }
})