import {httpRequest, URLParams} from '../utils/tools'

export function hasPermission(code) {
    // const suffixCarType = {
    //     car: '',
    //     bus: 'kc',
    //     truck: 'hc',
    //     moto: 'mt',
    // }[URLParams.carStyle]
    const suffixCourse = +URLParams.kemuNum === 4 ? 'K4' : 'K1'

    return httpRequest({
        url: 'api/open/permission/has-permission.htm',
        params: {
            permission: code + suffixCourse,
        },
        host: 'sirius',
    }).then(data => data.status === 1)
}

export function getLiveType(params, config) {
    return httpRequest({
        url: 'api/open/live/get-live-type.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getVipBages(params, config) {
    return httpRequest({
        url: 'api/web/vip-badge/vip-badges.htm',
        params: params,
        host: 'pony',
        ...config,
    })
}
export function getUserInfoForWX(params, config) {
    return httpRequest({
        url: 'api/open/third-party-user/get-user-info.htm',
        params: params,
        host: 'misc',
        ...config,
    })
}
