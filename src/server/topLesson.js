import {httpRequest, formatPriceRecursion, getProvide, URLParams} from '../utils/tools'
import {newCreateMobileOrder} from './newGoods'
import {find} from 'lodash-es'

export function getLiveDetail(params, config) {
    return httpRequest({
        url: 'api/web/live/get-live-room-detail.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function getLiveRoomDetail(params, config) {
    return httpRequest({
        url: 'api/web/live-room/get-detail.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function getLessonDetail(params, config) {
    return httpRequest({
        url: 'api/web/top-lesson/lesson-detail.htm',
        params: params,
        host: 'panda',
        ...config,
    }).then(async resData => {
        let data = formatPriceRecursion(resData)
        data.originalPrice = data.originPrice

        if ((await getProvide()) && data.channelCode) {
            let priceRes = await getChannelGoodsPrice({
                tiku: URLParams.carStyle,
                channelCodes: data.channelCode,
            })
            data = Object.assign(data, priceRes[0])
        }
        data.groupKey = data.channelCode
        return data
    })
}
export function getChannelGoodsPrice(params, config) {
    return httpRequest({
        url: 'api/web/sales/get-channel-goods-price.htm',
        params: params,
        host: 'squirrel',
        ...config,
    }).then(channeData => {
        let itemList = channeData.itemList
        let data = []
        itemList.forEach(priceItem => {
            data.push({
                price: priceItem.salePrice,
                originalPrice: priceItem.channelPrice,
                suggestedPrice: priceItem.suggestedPrice,
                applePriceId: priceItem.appleGoodsId,
                channelCode: priceItem.channelCode,
                squirrelGoodsInfo: {
                    appleGoodsId: priceItem.appleGoodsId,
                    goodsDataName: priceItem.goods.goodsName,
                    goodsDataCode: priceItem.goods.dataCode,
                    goodsDataType: priceItem.goods.dataType,
                    channelCode: priceItem.channelCode,
                    activityType: priceItem.activityType,
                    activityCode: priceItem.activityCode,
                    priceConfigCode: priceItem.priceConfigCode,
                },
            })
        })
        return Promise.resolve(data)
    })
}
export function inform(params, config) {
    return httpRequest({
        url: 'api/web/live/inform.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export async function orderPaid(params, config) {
    if (await getProvide()) {
        return httpRequest({
            url: 'api/open/order/get-order-status.htm',
            params: {
                orderNos: params.orderNumber,
            },
            host: 'squirrel',
            ...config,
        }).then(resData => {
            let data = resData.itemList.map(item => {
                return {
                    orderNo: item.orderNo,
                    orderStatus: item.orderStatus === 1 ? 0 : item.orderStatus,
                }
            })
            data = data[0] || {}
            return {
                orderPaid: data.orderStatus,
            }
        })
    } else {
        return httpRequest({
            url: 'api/open/top-lesson/order-paid.htm',
            params: params,
            host: 'misc',
            ...config,
        })
    }
}
export async function bindUser(params, config) {
    if (await getProvide()) {
        return httpRequest({
            url: 'api/open/order/bind.htm',
            params: {
                orderNos: params.orderNumber,
            },
            host: 'squirrel',
            method: 'post',
            ...config,
        })
    } else {
        return httpRequest({
            url: 'api/open/top-lesson/bind-user.htm',
            params: params,
            host: 'misc',
            ...config,
        })
    }
}
export function getLessonList(params, config) {
    return httpRequest({
        url: 'api/open/top-lesson/get-lesson-list.htm',
        params: params,
        host: 'panda',
        ...config,
    }).then(async resData => {
        let data = formatPriceRecursion(resData)
        data.itemList = data.itemList || []
        let channelCode = data.itemList
            .filter(item => item.channelCode)
            .map(item => item.channelCode)
        if ((await getProvide()) && channelCode.length) {
            channelCode = channelCode.join(',')
            let priceRes = await getChannelGoodsPrice({
                tiku: URLParams.carStyle,
                channelCodes: channelCode,
            })

            priceRes.forEach(item => {
                let course = find(data.itemList, {channelCode: item.channelCode})
                if (course) {
                    course.price = item.price
                }
            })
        }
        return data
    })
}
// todo 废弃
export function cancelLiveSubscribe(params, config) {
    return httpRequest({
        url: 'api/open/live-subscribe/cancel-subscribe.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
// todo 废弃
export function liveSubscribe(params, config) {
    return httpRequest({
        url: 'api/open/live-subscribe/subscribe.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function subscribeNotify(params, config) {
    return httpRequest({
        url: 'api/open/live-room/subscribe-notify.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function getSellRate(params, config) {
    return httpRequest({
        url: 'api/web/live-stock/get-remain-stock-info.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
export function getBatchSellRate(params, config) {
    return httpRequest({
        url: 'api/web/live-stock/batch-get-remain-stock-info.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
    // MOCK
    // .then(() => {
    //     let data = {
    //         itemList: [
    //             {enable: true, value: 1},
    //             {enable: true, value: 1},
    //         ],
    //     }
    //     return Promise.resolve(data)
    // })
}
export function getSellApplyScene(params, config) {
    return httpRequest({
        url: 'api/web/live-stock/get-apply-scene.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

// todo 废弃
export function getNextLives(params, config) {
    return httpRequest({
        url: 'api/web/live/get-next-lives.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
    // MOCK
    // .then(() => {
    //     let data = {
    //         itemList: [
    //             {
    //                 beginTime: 1640873400000,
    //                 cover:
    //                     'https://jiakao-web.mc-cdn.cn/jiakao-web/2021/11/12/11/df937659080648eea7f5b9159ddac167.png',
    //                 endTime: 1641028200000,
    //                 lessonItemId: 7573,
    //                 subscribeStatus: 0,
    //                 teacherCoverImg: null,
    //                 teacherHeadImg: null,
    //                 title: '直播间1',
    //             },
    //         ],
    //     }
    //     return Promise.resolve(data)
    // })
}

export async function createLessonOrder(params, config) {
    if (await getProvide()) {
        return newCreateMobileOrder(params, config)
    } else {
        return httpRequest({
            url: 'api/open/top-lesson/create-order.htm',
            params: params,
            host: 'misc',
            ...config,
        })
    }
}

export async function getDetailAndCatelog(params, config) {
    return httpRequest({
        url: 'api/open/top-lesson/get-top-less-detail-and-catelog.htm',
        params: params,
        host: 'panda',
        ...config,
    }).then(async resData => {
        let data = formatPriceRecursion(resData)
        data.originalPrice = data.originPrice
        data.suggestedPrice = data.originPrice

        if ((await getProvide()) && data.channelCode) {
            let priceRes = await getChannelGoodsPrice({
                tiku: URLParams.carStyle,
                channelCodes: data.channelCode,
            })
            data = Object.assign(data, priceRes[0])
        }
        return data
    })
}

// todo 废弃
export function getLiveConfig(params, config) {
    return httpRequest({
        url: 'api/web/live/live-config.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getLiveStatus(params, config) {
    return httpRequest({
        url: 'api/web/live/live-status.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getLiveRoomStatus(params, config) {
    return httpRequest({
        url: 'api/web/live-room/live-status.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getLiveRecord(params, config) {
    return httpRequest({
        url: 'api/web/live/live-record.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getLiveRoomRecord(params, config) {
    return httpRequest({
        url: 'api/web/live-room/get-live-record.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getSessionList(params, config) {
    return httpRequest({
        url: 'api/web/live-session/get-recommend-list.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getNextVipLive(params, config) {
    return httpRequest({
        url: 'api/web/vip-live/get-next-live.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getLatestLive(params, config) {
    return httpRequest({
        url: 'api/web/live/latest-live.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getResource(params, config) {
    return httpRequest({
        url: 'api/web/live-room/get-resource.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getAdvertDetail(params, config) {
    return httpRequest({
        url: 'api/web/live/get-advert-detail.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getResourceDetail(params, config) {
    return httpRequest({
        url: 'api/web/live-room/get-resource-detail.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getGoodsList(params, config) {
    function hasPermission(code) {
        return httpRequest({
            url: 'api/web/top-lesson/lesson-detail.htm',
            params: {
                id: code,
            },
            host: 'panda',
        }).then(data => data.hasPermission)
    }
    return httpRequest({
        url: 'api/open/live-goods/get-goods-list.htm',
        params: params,
        host: 'monkey',
        ...config,
    }).then(async resData => {
        resData = resData.itemList || resData
        await Promise.all(resData.map(async item => {
            if (item.type === 'lesson') {
                item.bought = await hasPermission(item.goodsKey)
            }
        }))
        return resData
    })
}

export function getLiveGoodsDetail(params, config) {
    return httpRequest({
        url: 'api/web/live-goods/goods-detail-data.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}

export function getApproGoodsConfig(params, config) {
    return httpRequest({
        url: 'api/open/live-room/get-paid-live-preview-config.htm',
        params: params,
        host: 'monkey',
        ...config,
    })
}
