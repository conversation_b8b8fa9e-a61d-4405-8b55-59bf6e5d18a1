import {httpRequest} from '../utils/tools'

export function getTrainingRecommendChannelCode(params, config) {
    const {carType, kemu, sceneCode} = params
    let data = {}
    if (kemu === '1' && sceneCode === '102') {
        data = {
            category1: carType,
            category2: 'mfxx',
        }
    } else {
        data = {
            category1: carType,
            category2: `k${kemu}`,
        }
    }
    return httpRequest({
        url: 'api/web/customer/recommend-channel-code.htm',
        params: data,
        host: 'parrot',
        ...config,
    })
}

export function getTrainingQuoteDetail(params, config) {
    return httpRequest({
        url: 'api/web/quote/get-quote-detail.htm',
        params: params,
        host: 'parrot',
        ...config,
    })
}
