import {once} from 'lodash-es'
import {httpRequest} from '../utils/tools'

export function getLotteryCodes(params, config) {
    return httpRequest({
        url: 'api/web/xueche-index/index-select-codes.htm',
        params: params,
        host: 'activity',
        ...config,
    })
    // MOCK
    // .then(() => {
    //     let resData = {
    //         codeInfo: [
    //             {
    //                 liveId: 66,
    //                 code1: [{code: 121}, {code: 122}],
    //                 sharedCodes: [],
    //             },
    //             {
    //                 liveId: 67,
    //                 code1: [{code: 123}],
    //                 sharedCodes: [],
    //             },
    //         ],
    //     }
    //     return Promise.resolve(resData)
    // })
}
export async function createAppOrder(params, config) {
    const {goods, channelCode} = await getGoodsPrice({channelCodes: params.channelCodes})
    const {dataCode, dataType} = goods
    return httpRequest({
        url: 'api/web/xue-che/create-order.htm',
        params: {
            ...params,
            goodsDataCode: dataCode,
            goodsDataType: dataType,
            channelCode,
        },
        host: 'activity',
        method: 'post',
        ...config,
    })
}
export function orderUnbind(params, config) {
    return httpRequest({
        url: 'api/web/xue-che/order-unbind.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}
export function getNextLotteryData(params, config) {
    return httpRequest({
        url: 'api/web/xueche-index/index-header-next.htm',
        params: params,
        host: 'activity',
        ...config,
    })
}
export function getChannelGoodsPrice(params, config) {
    return httpRequest({
        url: 'api/web/sales/get-channel-goods-price.htm',
        params: params,
        host: 'squirrel',
        ...config,
    }).then(res => res.itemList[0])
}

export const getGoodsPrice = once(getChannelGoodsPrice)