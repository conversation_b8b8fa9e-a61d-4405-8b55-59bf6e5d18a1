import Vue from 'vue'
import Vuex from 'vuex'
import state from './state'
import mutations from './mutations'
import actions from './actions'

Vue.use(Vuex)
const store = new Vuex.Store({
    state,
    mutations,
    actions,
    getters: {
        payList(state, payload) {
            return state.payConfig.payList
        },
        checkdPayType(state, payload) {
            return state.payConfig.checkdPayType
        },
        checkAgreement(state, payload) {
            store.dispatch('checkAgreement')
            return state.payConfig.checkAgreement
        },
        readed(state, payload) {
            return state.payConfig.readed
        },
        readed2(state, payload) {
            return state.payConfig.readed2
        },
    },
})
export default store
