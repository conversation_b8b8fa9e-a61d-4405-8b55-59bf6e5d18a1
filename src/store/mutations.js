import Vue from 'vue'

const updateBizConfig = (state, payload) => {
    Object.keys(payload).forEach(item => {
        state.bizConfig[item] = payload[item]
    })
}
const updateRoomStatus = (state, payload) => {
    Object.keys(payload).forEach(item => {
        state.roomStatus[item] = payload[item]
    })
}
const setRoomDetail = (state, payload) => {
    state.roomDetail = payload
}
const updateRoomDetail = (state, payload) => {
    Object.keys(payload).forEach(item => {
        state.roomDetail[item] = payload[item]
    })
}
const setPendantResource = (state, payload) => {
    state.pendantResource = payload
}
const setPushCoupon = (state, payload) => {
    state.pushCoupon = payload
}
const updateSelectCoupons = (state, payload) => {
    Object.keys(payload).forEach(item => {
        Vue.set(state.selectCoupons, item, payload[item])
    })
}
const setRedPacketActive = (state, payload) => {
    state.redPacketActive = payload
}
const updateRemainSotckInfo = (state, payload) => {
    Object.keys(payload).forEach(item => {
        state.remainSotckInfo[item] = payload[item]
    })
}
const setLandscape = (state, payload) => {
    state.isLandscape = payload
}
const setShowDanmu = (state, payload) => {
    state.isShowDanmu = payload
}

const updateLotteryData = (state, payload) => {
    Object.keys(payload).forEach(item => {
        state.lotteryData[item] = payload[item]
    })
}

const updatePayConfig = (state, payload) => {
    Object.keys(payload).forEach(item => {
        state.payConfig[item] = payload[item]
    })
}
const setRoomResource = (state, payload) => {
    state.roomResource = payload
}

export default {
    updateBizConfig,
    updateRoomStatus,
    setRoomDetail,
    updateRoomDetail,
    setPendantResource,
    setPushCoupon,
    updateSelectCoupons,
    setRedPacketActive,
    updateRemainSotckInfo,
    setLandscape,
    setShowDanmu,
    updateLotteryData,
    updatePayConfig,
    setRoomResource,
}
