import {URLParams, isIOS, compareVersionComplex} from '../utils/tools'
import {hasReaded, hasReaded2} from '../utils/agreementHelper'
let bizConfig = {
    motionVisible: false,
    nopayMotionVisible: false,
    saleMode: false,
    stMode: false,
    quickCommentVisible: false,
    webrtcStream: false,
    isRealLive: false,
    playStatus: 0,
    hasActivityRule: false,
}
const roomStatus = {
    pv: null,
    status: 0,
    playbackDone: false,
    danmuStatus: -1,
    sessionId: 0,
}
const roomDetail = {}
const pushCoupon = {}
const selectCoupons = {}
const redPacketActive = {}
const remainSotckInfo = {
    dataList: [
        {
            sellRateEnable: false,
            sellRateNum: 100,
        },
    ],
    applyScene: '',
}
const payConfig = {payList: [], checkdPayType: 1, checkAgreement: false, readed: hasReaded(), readed2: hasReaded2()}
const isLandscape = false
// 老年版默认关闭弹幕
const isShowDanmu = +URLParams.patternCode !== 102
const isShowWebHeader = (() => {
    if (isIOS) {
        return compareVersionComplex('8.4.6') > 0
    } else {
        return compareVersionComplex('8.3.9') > 0
    }
})()

const lotteryData = {point: 0}

const roomResource = {
    cardDetail: {},
    bannerDetail: {},
    pendantDetail: {},
    warmupDetail: {},
}

const pendantResource = {}

export default {
    bizConfig,
    roomStatus,
    roomDetail,
    pushCoupon,
    selectCoupons,
    redPacketActive,
    remainSotckInfo,
    isLandscape,
    isShowDanmu,
    isShowWebHeader,
    lotteryData,
    payConfig,
    roomResource,
    pendantResource,
}
