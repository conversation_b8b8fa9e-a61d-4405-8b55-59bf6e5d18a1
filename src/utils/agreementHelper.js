import {once} from 'lodash-es'
import {URLParams} from '../utils/tools'
import {getConfigCache} from '../server/common'

const storageKey = 'checkboxKey'
const storageKey2 = 'checkboxKey2'

export const getCheckAgreement = once(async () => {
    const resData = await getConfigCache({carStyle: URLParams.carStyle})
    return resData.jk_buy_agreement === 'true'
})

export function hasReaded() {
    const storageReaded = window.localStorage.getItem(storageKey)
    return storageReaded === 'true'
}
export function setReaded(value) {
    window.localStorage.setItem(storageKey, String(value))
}

export function hasReaded2() {
    const storageReaded = window.localStorage.getItem(storageKey2)
    return storageReaded === 'true'
}
export function setReaded2(value) {
    window.localStorage.setItem(storageKey2, String(value))
}
