import comfirmComp from '../components/confirm.vue'

let instance
const confirm = {
    install(Vue) {
        Vue.prototype.$comfirm = config => {
            let {
                title,
                message,
                confirmText,
                showCancelButton,
                showConfirmButton,
                callback,
            } = config
            let ComfirmConstructor = Vue.extend(comfirmComp)

            instance = new ComfirmConstructor({
                propsData: {title, message, confirmText, showCancelButton, showConfirmButton},
            })
            instance.$on('cancel', function() {
                let flag = false
                callback(flag)
                instance.vm.visible = false
            })
            instance.$on('comfirm', function() {
                let flag = true
                callback(flag)
                instance.vm.visible = false
            })

            instance.vm = instance.$mount()
            document.body.appendChild(instance.vm.$el)
            instance.vm.visible = true
        }
    },
}
export default confirm
