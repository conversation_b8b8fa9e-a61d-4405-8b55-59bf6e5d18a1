import confirmProtocolComp from '../components/confirmProtocol.vue'

let instance
const confirm = {
    install(Vue) {
        Vue.prototype.$confirmProtocol = (config = {}) => {
            let {confirmCallback, type} = config
            let ConfirmProtocolConstructor = Vue.extend(confirmProtocolComp)

            instance = new ConfirmProtocolConstructor({propsData: {
                type,
            }})
            instance.$on('cancel', function() {
                instance.vm.visible = false
            })

            instance.vm = instance.$mount()
            document.body.appendChild(instance.vm.$el)
            instance.vm.visible = true

            return new Promise(resolve => {
                instance.$on('confirm', function() {
                    let flag = true
                    confirmCallback && confirmCallback()
                    resolve(flag)
                    instance.vm.visible = false
                })
            })
        }
    },
}
export default confirm
