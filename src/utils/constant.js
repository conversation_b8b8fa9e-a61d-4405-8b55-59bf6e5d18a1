export const agreementURL =
    'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=jkbdVIP'
export const agreementURL2 =
    'https://laofuzi.kakamobi.com/protocol/protocol.html?protocolKey=sjbAgreement'
export const helpURL =
    'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-downApp/jkbdgw.html?open=true'
export const commentUrl = 'http://saturn.nav.mucang.cn/tag/detail?tagId=37456'

export const vipSaveDataKey = 'orderInfo'
export const lessonSaveDataKey = 'lessonOrderInfo'

export const liveRoomURL = 'http://jiakao.nav.mucang.cn/live/room'
export const topLessonliveRoomURL = 'http://jiakao.nav.mucang.cn/topLesson/live'
export const VIP_BUYED_URL = 'https://laofuzi.kakamobi.com/jkbd-vip/index/buyed.html'

export const ROOM_TYPE = {
    CGZB: 1, // 常规直播
    XCJ66: 2, // 66学车节
    GGZB: 3, // 公共直播
    HYZX: 4, // 会员专享直播
    ZBZB: 5, // 长辈专属直播
    XBJX: 6, // 小班教学
    KQFD: 7, // 考前辅导
    ZBDH: 8, // 直播带货
    ZXGK: 9, // 专项攻克
    ZDSTB_OLD: 10, // 重点刷题班
    ZDSTB: 11, // 付费刷题班
    ZDSTB_FREE: 12, // 免费刷题班
    ZBMK: 12, // 直播模考
}

export const RESOURCE_TYPE = {
    1: 'normal',
    2: 'vip',
    3: 'lesson',
    6: 'training',
}

export const LOCATION = {
    AUTHTOKEN: 'mucang_authToken',
    USERINFO: 'mucang_userInfo',
}

export const wxAppId = 'wx39ddb2e3b2dcb858'

export const boughtURL =
    'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-zhibojian/bought.html'

export const trainingBoughtURL =
    'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-personal-training/train-plan.html'

export const awardListURL =
    'https://laofuzi.kakamobi.com/coupon/award.html'

export const replaceInviteUrl = 'https://share-m.kakamobi.com/activity.kakamobi.com/jkbd-vip/pages/replaceInvite.html#/replaceInvite'