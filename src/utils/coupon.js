import {pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {getCouponList} from '../server/goods'
import {openVipWebView, getProvide, getUrl, getAuthToken, isIOS, URLParams, formatPrice} from './tools'

/** 获取用户最佳优惠券 */
export async function getBestCoupon(vipGoodsDetail, entityGoods) {
    // 安卓已登录情况下才有优惠券
    if ((isIOS && !entityGoods) || !(await getAuthToken())) {
        return {
            couponCode: '',
            priceCent: '',
        }
    }

    let squirrelGoodsInfo = vipGoodsDetail.squirrelGoodsInfo || {}
    const resData = await getCouponList({
        tiku: URLParams.carStyle,
        sessionIds: vipGoodsDetail.sessionIdList.join(','),
        groupKey: vipGoodsDetail.groupKey,
        dataType: squirrelGoodsInfo.goodsDataType,
        dataCode: squirrelGoodsInfo.goodsDataCode,
    })

    let coupon = resData.itemList.filter(item => item.canUse)

    if (!coupon.length) {
        return {
            couponCode: '',
            priceCent: '',
        }
    }

    let maxPrice = 0
    let maxIndex = 0
    for (let i = 0; i < coupon.length; i++) {
        const iData = coupon[i]
        if (iData.priceCent > maxPrice) {
            maxPrice = iData.priceCent
            maxIndex = i
        }
    }
    const bestCoupon = coupon[maxIndex]
    return {
        couponCode: bestCoupon.couponCode,
        priceCent: formatPrice(bestCoupon.priceCent),
    }
}

export async function selectUserCoupon(vipGoodsDetail, currentCouponCode) {
    openVipWebView({
        url: getUrl('https://laofuzi.kakamobi.com/jkbd-vip/index/couponDetail.html', {
            apiHost: (await getProvide()) ? 'squirrel' : 'sirius',
            groupKey: vipGoodsDetail.groupKey,
            sessionIds: JSON.stringify(vipGoodsDetail.sessionIdList),
            selCouponCode: currentCouponCode,
        }),
    })

    await new Promise(resolve => {
        pageSwitch.onPageShow(resolve)
    })

    const key = vipGoodsDetail.groupKey + '_selectCoupon'
    const selectedCoupenStr = localStorage.getItem(key) || sessionStorage.getItem(key)
    const selectedCoupon = selectedCoupenStr && JSON.parse(selectedCoupenStr)

    if (selectedCoupon) {
        localStorage.removeItem(key)
        sessionStorage.setItem(key, selectedCoupenStr)
        return {
            couponCode: selectedCoupon.couponCode,
            priceCent: formatPrice(selectedCoupon.priceCent),
        }
    }
    return null
}