import list from '../libs/district.json'
var zxCitys = ['120000', '110000', '500000', '310000']
function getDistrict() {
    var result = []
    for (var i = 0; i < list.length; i++) {
        var city = list[i].cities || []

        var item = {
            province: {
                name: list[i].name,
                code: list[i].code,
            },
            citys: [],
        }
        for (var n = 0; n < city.length; n++) {
            if (city[n].counties.length === 0) {
                city[n].counties = [
                    {
                        code: city[n].code,
                        name: city[n].name,
                        parent: city[n].code,
                        pinyin: city[n].pinyin,
                        simpleName: city[n].simpleName,
                        type: 'COUNTY',
                    },
                ]
            }
            item.citys.push({
                name: city[n].name,
                code: city[n].code,
                counties: city[n].counties,
            })
        }

        if (zxCitys.indexOf(item.province.code) > -1) {
            item.citys = [
                {
                    name: item.province.name,
                    code: item.province.code,
                    counties: list[i].cities,
                },
            ]
        }

        result.push(item)
    }

    return result
}
let district = getDistrict()
export function provinces() {
    var list = []
    for (var i = 0; i < district.length; i++) {
        list.push(district[i].province)
    }
    return list
}

export function getCitysOfProvince(province) {
    var list = []
    for (var i = 0; i < district.length; i++) {
        if (district[i].province.code === province) {
            list = district[i].citys
        }
    }
    return list
}

export function getAreasOfCity(cityCode) {
    var areas = []
    for (var i = 0; i < district.length; i++) {
        var cities = district[i].citys
        if (cities && cities.length) {
            for (var j = 0; j < cities.length; j++) {
                if (cities[j].code === cityCode) {
                    areas = cities[j].counties
                }
            }
        }
    }
    return areas
}

export function getProAndCityByAreaCode(areaCode) {
    var data = {
        provinceVal: null,
        cityVal: null,
        areaVal: null,
    }
    for (var i = 0; i < district.length; i++) {
        var cities = district[i].citys
        if (cities && cities.length) {
            for (var j = 0; j < cities.length; j++) {
                var counties = cities[j].counties
                for (var k = 0; k < counties.length; k++) {
                    if (counties[k].code === areaCode) {
                        data.provinceVal = district[i].province
                        data.cityVal = cities[j]
                        data.areaVal = counties[k]

                        return data
                    }
                }
            }
        }
    }
}
