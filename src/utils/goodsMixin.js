
import {mapState, mapMutations} from 'vuex'
import {URLParams, getURLParams, getUrl, webOpen, isMucang, trackEvent, getAuthToken, goLogin} from './tools'
import {RESOURCE_TYPE} from '../utils/constant'
import {getBestCoupon} from '../utils/coupon'
import {getSessionInfo, comparePrice} from '../server/goods'
import {getAdvertDetail, getResourceDetail, getLessonDetail} from '../server/topLesson'

export default {
    computed: {
        ...mapState(['roomDetail', 'bizConfig']),
    },
    methods: {
        ...mapMutations([
            'updateSelectCoupons',
        ]),
        async getGoodsItem(advertDetail, showErrorTips) {
            let promotionActivityId = this.bizConfig.playStatus === 1 && this.roomDetail.promotionActivityId
            return this.getGoodsData(promotionActivityId, advertDetail, showErrorTips)
        },
        async getGoodsEntry(advertDetail, showErrorTips) {
            let promotionActivityId = URLParams.promotionActivityId
            return this.getGoodsData(promotionActivityId, advertDetail, showErrorTips)
        },
        async getGoodsData(promotionActivityId, advertDetail, showErrorTips) {
            let resData
            if (advertDetail.advertType === 'vip') {
                try {
                    resData = await getSessionInfo({
                        tiku: URLParams.carStyle,
                        groupKey: advertDetail.goodsKey,
                        promotionActivityId,
                    })
                    this.getCoupon(resData, resData.entityGoods)
                    // TODO 多次请求
                    let gapPrice = await this.comparePrice(advertDetail.goodsKey)
                    resData.gapPrice = gapPrice
                } catch (error) {
                    if (showErrorTips && error.errorCode === 401001) {
                        console.log('设备超限')
                        this.$comfirm({
                            title: '设备超限',
                            message: error.message,
                            showCancelButton: true,
                            showConfirmButton: true,
                            callback: () => {},
                        })
                        showErrorTips = false
                    }
                }
            } else if (advertDetail.advertType === 'lesson') {
                resData = await getLessonDetail({
                    carType: URLParams.carStyle,
                    id: advertDetail.goodsKey,
                })
            } else if (advertDetail.advertType === 'training') {
                resData = await getSessionInfo({
                    tiku: URLParams.carStyle,
                    groupKey: advertDetail.goodsKey,
                })
                resData.entityGoods = true
                resData.goodsType = advertDetail.advertType
                this.getCoupon(resData, resData.entityGoods)
            }
            return resData
        },
        async comparePrice(groupKey) {
            const resData = await comparePrice({
                tiku: URLParams.carStyle,
                sceneCode: URLParams.sceneCode,
                patternCode: URLParams.patternCode,
                groupKey: groupKey,
            })
            if (resData) {
                const savePrice = resData.savePrice
                return savePrice
            } else {
                return 0
            }
        },
        async getCoupon(goodsDetail, entityGoods) {
            let coupon = await getBestCoupon(goodsDetail, entityGoods)

            if (coupon.couponCode) {
                this.updateSelectCoupons({[goodsDetail.groupKey + '_selectCoupon']: coupon})
            }
        },
        async getAdvertDetail(advertDetail) {
            let res
            if (advertDetail.resourceId) {
                res = await getResourceDetail({
                    resourceId: advertDetail.resourceId,
                    carType: URLParams.carStyle,
                    sessionId: URLParams.id,
                })
                res.advertType = RESOURCE_TYPE[res.type]
            } else {
                res = await getAdvertDetail({
                    advertId: advertDetail.advertId,
                    carType: URLParams.carStyle,
                    sessionId: URLParams.id,
                })
            }
            res.detailUrl = res.detailUrl || res.jumpUrl
            res.goodsKey = advertDetail.goodsKey
            res.groupKey = res.goodsUniqueKey = res.goodsKey
            return res
        },
        popupMotion(advert, motionDetail, report = true, fullScreen = false) {
            this.$EventBus.$emit('setOrientation', 'portrait', async () => {
                if (advert.advertType === 'training') {
                    const authToken = await getAuthToken()
                    if (!authToken) {
                        goLogin({refresh: true})
                        return
                    }
                }
                this.fullScreen = fullScreen
                if (advert.advertType === 'vip' || advert.advertType === 'lesson' || advert.advertType === 'training') {
                    if (
                        (advert.advertType === 'lesson' && advert.bought) ||
                        (advert.advertType === 'training' && advert.bought) ||
                        (advert.advertType === 'vip' &&
                            advert.bought &&
                            !advert.upgrade)
                    ) {
                        this.openDetail(motionDetail.detailUrl)
                    } else {
                        this.payPopupVisible = true
                        if (report) {
                            let fragmentName1 = this.fragmentName1
                            let actionType = '点击'
                            let actionName = '去支付'

                            // 精品课直播间页_右方商品图标_点击去支付
                            trackEvent({
                                fragmentName1,
                                actionType,
                                actionName,
                                payPathType: 0,
                                groupKey: advert.goodsKey,
                                goodsUniqueKey: advert.goodsKey,
                                payStatus: 2,
                            })
                        }
                    }
                } else if (motionDetail.detailUrl) {
                    let url = motionDetail.detailUrl
                    let from = getURLParams(null, url).from
                    // 普通运营位，如果配置的链接没有from值，则使用当前直播间页面的from值
                    if (!from && URLParams.from) {
                        url = getUrl(url, {
                            from: URLParams.from,
                        })
                    }
                    url = url.replace(/phS(\w+)phE/g, function(str, key) {
                        if (key === 'liveId') {
                            return URLParams.id
                        }
                        return str
                    })
                    webOpen({
                        url: url,
                        titleBar: true,
                    })

                    let actionType = '点击'
                    let actionName = this.fragmentName1

                    // 精品课直播间页_点击右方商品图标
                    trackEvent({
                        actionType,
                        actionName,
                    })
                }
            })
        },
        openDetail(detailUrl) {
            if (!isMucang) {
                let url = detailUrl
                if (detailUrl.match(/^http(s)?:\/\/(jiakao.nav.mucang.cn\/vip\/new-vip)/)) {
                    let page = getURLParams(null, detailUrl).page
                    url = page || detailUrl
                }
                webOpen({
                    url,
                })
            } else if (detailUrl) {
                webOpen({
                    url: detailUrl,
                })
            }
        },
    },
}
