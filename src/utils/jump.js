import {MCProtocol} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
export const replace = url => {
    stat.trackExit()
    setTimeout(() => {
        window.location.replace(url)
    }, 100)
}
export const reload = () => {
    stat.trackExit()
    setTimeout(() => {
        window.location.reload()
    }, 100)
}

export const navigateTo = url => {
    stat.trackExit()
    setTimeout(() => {
        window.location.href = url
    }, 100)
}

export const webClose = () => {
    stat.trackExit()
    MCProtocol.Core.Web.close()
}
