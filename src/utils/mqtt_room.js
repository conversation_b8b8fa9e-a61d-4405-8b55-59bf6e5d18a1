require('../libs/mqttws31')

function MQTTconnect(data, config, messageReciver, onConnectCb) {
    let mqtt
    let {roomId, cleansession, reconnectTimeout, debug} = config

    function onConnect() {
        debug && console.info('连接上了')
        mqtt.subscribe(data.subTopic + '/message/' + roomId, {qos: 0})
        mqtt.subscribe(data.subTopic + '/status/' + roomId, {qos: 0})
        onConnectCb(mqtt)
    }

    function onConnectionLost(response) {
        debug && console.info('connectionLost', response)
        if (response.errorCode !== 0) {
            setTimeout(MQTTconnect, reconnectTimeout, data, config, messageReciver, onConnectCb)
        }
    }

    function onMessageArrived(message) {
        let payload = JSON.parse(message.payloadString)
        debug && console.info('收到了', payload)
        messageReciver(payload)
    }

    /* global Paho */
    mqtt = new Paho.MQTT.Client(
        data.host, // MQTT 域名
        data.port, // WebSocket 端口，如果使用 HTTPS 加密则配置为443,否则配置80
        data.clientId // 客户端 ClientId
    )
    mqtt.onConnectionLost = onConnectionLost
    mqtt.onMessageArrived = onMessageArrived

    let options = {
        timeout: 3,
        onSuccess: onConnect,
        mqttVersion: 4,
        cleanSession: cleansession,
        onFailure: function(message) {
            debug && console.info('failure', message)
            setTimeout(MQTTconnect, reconnectTimeout, data, config, messageReciver, onConnectCb)
        },
    }

    if (data.username != null) {
        options.userName = data.username
        options.password = data.password
        options.useSSL = data.useSsl // 如果使用 HTTPS 加密则配置为 true
    }
    mqtt.sendMessage = function(message) {
        let _message = new Paho.MQTT.Message(
            JSON.stringify({
                text: message,
                messageExtraInfo: config.messageExtraInfo,
            })
        )
        _message.destinationName = data.pubTopic + '/message/' + roomId
        mqtt.send(_message)
        debug && console.info('发送了', _message)
    }
    mqtt.reconnect = function() {
        MQTTconnect(data, config, messageReciver, onConnectCb)
    }
    mqtt.connect(options)
}

export default function createMqtt(data, config, messageReciver, onConnectCb) {
    return MQTTconnect(data, config, messageReciver, onConnectCb)
}
