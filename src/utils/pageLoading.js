import Vue from 'vue'
import pageLoadingComp from '../components/pageLoading.vue'

const PageLoadingConstructor = Vue.extend(pageLoadingComp)

PageLoadingConstructor.prototype.close = function() {
    setTimeout(() => {
        if (this.$el && this.$el.parentNode) {
            this.$el.parentNode.removeChild(this.$el)
        }
        this.$destroy()
    }, 100)
    this.visible = false
}

const PageLoading = (options = {}) => {
    let parent = document.body
    let instance = new PageLoadingConstructor({
        el: document.createElement('div'),
        data: options,
    })

    parent.appendChild(instance.$el)
    Vue.nextTick(() => {
        instance.visible = true
    })

    return instance
}

export default PageLoading
