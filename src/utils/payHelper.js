import Vue from 'vue'
import {stat, abtest} from '@jiakaobaodian/jiakaobaodian-utils'
import {throttle, findIndex} from 'lodash-es'
import {Pay} from '@simplex/simple-pay'
import {MCProtocol} from '@simplex/simple-base'
import {vipSaveDataKey, lessonSaveDataKey} from './constant'
import {
    webOpen,
    getData,
    saveData,
    isVIVO,
    getAuthToken,
    toast,
    listenBuySuccess,
    URLParams,
    isIOS,
    isAndroid,
    goLogin,
    isMucang,
    isWeixin,
    toAwait,
} from './tools'
import {reload, webClose} from '../utils/jump'
import {getOrderStatus, checkOrderBinding, bindGoods, createGoodsOrder} from '../server/goods'
import {createLessonOrder, orderPaid, bindUser} from '../server/topLesson'
import {getActiveConfig} from '../server/active'

MCProtocol.register('Vip.cachedOrders', function(config) {
    return config
})
MCProtocol.register('Vip.saveOrder', function(config) {
    return config
})
MCProtocol.register('Vip.removeOrder', function(config) {
    return config
})
MCProtocol.register('Vip.didFinishBindingOrders', function(config) {
    return config
})

function getOrder(orderType, useNewApi) {
    if (useNewApi) {
        return new Promise(resolve => {
            MCProtocol.Vip.cachedOrders({
                serviceName: orderType,
                params: {lessonId: URLParams.id},
                callback: data => {
                    console.log('cachedOrders ' + orderType, data)
                    if (data.success) {
                        resolve({orderNumber: data.data && data.data.result && data.data.result[0]})
                    }
                },
            })
        })
    } else {
        let saveDataKey
        if (orderType === 'vip') {
            saveDataKey = vipSaveDataKey
        } else if (orderType === 'lesson') {
            saveDataKey = lessonSaveDataKey
        }
        return getData(saveDataKey)
    }
}
function removeOrder(orderNumber, orderType, useNewApi) {
    if (useNewApi) {
        MCProtocol.Vip.removeOrder({
            orderNumber,
            serviceName: orderType,
            params: {lessonId: URLParams.id},
        })
    } else {
        let saveDataKey
        if (orderType === 'vip') {
            saveDataKey = vipSaveDataKey
        } else if (orderType === 'lesson') {
            saveDataKey = lessonSaveDataKey
        }
        saveData({
            key: saveDataKey,
            value: '{}',
        })
    }
}
function saveOrder(orderNumber, orderType, useNewApi) {
    if (useNewApi) {
        return new Promise(resolve =>
            MCProtocol.Vip.saveOrder({
                orderNumber,
                serviceName: orderType,
                params: {lessonId: URLParams.id},
                callback: resolve,
            })
        )
    } else {
        let saveDataKey
        if (orderType === 'vip') {
            saveDataKey = vipSaveDataKey
        } else if (orderType === 'lesson') {
            saveDataKey = lessonSaveDataKey
        }
        saveData({
            key: saveDataKey,
            value: JSON.stringify({orderNumber: orderNumber}),
        })
    }
}
async function getOrderBindStatus(orderNumber, orderType) {
    // POINT
    if (orderType === 'vip') {
        const resData = await getOrderStatus({orderNumber})
        if (resData.status >= 1) {
            let status = resData.status
            const bindResData = await checkOrderBinding({orderNumber})
            return {
                status,
                bind: bindResData.bind,
            }
        } else {
            return {
                status: resData.value,
                bind: resData.value,
            }
        }
    } else if (orderType === 'lesson') {
        const paidResData = await orderPaid({orderNumber})
        return {
            status: Number(paidResData.orderPaid),
            bind: false,
        }
    }
}
function bindOrder(orderNumber, orderType) {
    if (!orderNumber) return
    let bindFun
    if (orderType === 'vip') {
        bindFun = bindGoods({orderNumbers: orderNumber})
    } else if (orderType === 'lesson') {
        bindFun = bindUser({orderNumber: orderNumber})
    }
    return bindFun
}
async function saveNopayOrder(goodsKey) {
    let {value: unPaidDurationTime} = await getActiveConfig({key: 'live_unpaid_durationTime'})
    unPaidDurationTime = unPaidDurationTime || 5 * 60
    if (!goodsKey) return
    let t = await getData('live_unpaid_order_list')
    if (!(t && t.length)) {
        t = []
    }
    const save = () => {
        t.push({goodsKey, expiredTime: +new Date() + (unPaidDurationTime * 1000)})
        saveData({
            key: 'live_unpaid_order_list',
            value: JSON.stringify(t),
        })
        setTimeout(() => {
            Vue.$EventBus.$emit('updateNopayExpiredTime')
        }, 60)
    }
    if (!t.length) {
        save()
    } else {
        const i = findIndex(t, {goodsKey})
        if (i === -1) {
            save()
        } else if (t[i].expiredTime < +new Date()) {
            t.splice(i, 1)
            save()
        }
    }
}

export async function getNopayExpiredTime(goodsKey) {
    let t = await getData('live_unpaid_order_list')
    if (!(t && t.length)) {
        t = []
    }
    const i = findIndex(t, {goodsKey})
    return t[i] && t[i].expiredTime
}

export async function queryOrderStatus(orderType, actionConfig) {
    // POINT
    const data = await getOrder(orderType, actionConfig.useNewApi)
    let orderNumber = data && data.orderNumber
    if (orderNumber) {
        const resData = await getOrderBindStatus(orderNumber, orderType)
        if (resData.status >= 1) {
            // 订单已支付，绑定订单
            console.log(orderNumber + '：' + orderType + '订单已支付，查看绑定状态')

            actionFactory(
                {
                    orderNumber,
                    orderType,
                    scene: 'query',
                    isBind: resData.bind,
                    isPaid: resData.status >= 1,
                },
                actionConfig
            )
            return Promise.reject(new Error('存在已支付订单'))
        } else {
            console.log(orderNumber + '：' + orderType + '未支付的订单直接清除')
            removeOrder(orderNumber, orderType, actionConfig.useNewApi)
            return {orderNumber}
        }
    } else {
        return {orderNumber: ''}
    }
}

export const createMobileOrder = throttle(async function createMobileOrder(config, actionConfig, success, error) {
    const authToken = await getAuthToken()
    if (!isMucang && isWeixin) {
        if (!authToken) {
            goLogin({refresh: true})
            return
        }
        doCreateMobileOrder(config, actionConfig, success, error)
    } else if (isVIVO()) {
        if (authToken) {
            doCreateMobileOrder(config, actionConfig, success, error)
        } else {
            await goLogin()
        }
    } else {
        doCreateMobileOrder(config, actionConfig, success, error)
    }
}, 1000)

export const payLessonGroup = throttle(async function payLessonGroup(config, actionConfig, success, error) {
    let orderType = 'lesson'
    let pageData = config.pageData || {}
    delete config.pageData
    const loading = Vue.$pageLoading({
        frozen: true,
    })
    let [err1] = await toAwait(queryOrderStatus(orderType, actionConfig))
    if (err1) {
        loading.close()
        return
    }
    let params = {
        lessonId: config.lessonGroupId,

        extraInfo: config.extraInfo,
        extraData: config.extraInfo,
        pageData: JSON.stringify({
            [Vue.bizIdName]: URLParams.id,
            pageName: config.page,
            fragmentName1: config.fragmentName1,
            pushCode: stat.getPushCode(),
            payPathType: config.payPathType,
            carStyle: URLParams.carStyle,
            kemu: URLParams.kemu,
            score12: URLParams.score12,
            fromPageCode: URLParams.from,
            fromPathCode: stat.getFromPathCode(),
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            fromItemCode: URLParams.fromItemCode || '',
            abTest: (await abtest.getAbtest()).abTest,
            ...pageData,
        }),
        ...config,
    }
    let [err2, resData] = await toAwait(createLessonOrder(params, {
        showErrorInfo: true,
    }))
    setTimeout(() => {
        loading.close()
    }, 500)
    if (err2) {
        return
    }
    let {orderNumber} = resData
    gopay(
        {
            // goodsKey: config.lessonGroupId,
            content: resData.content,
            appleId: config.appleId,
            payChannel: config.payChannel,
            // entityGoods: config.entityGoods,
            orderType,
            orderNumber,
        },
        actionConfig,
        success,
        error
    )

    const authToken = await getAuthToken()
    if (!authToken) {
        // 安卓端 未支付课程订单 不通过Vip.saveOrder存订单号
        if (!(isAndroid && actionConfig.useNewApi)) {
            // new-vip协议打开的，主动存缓存
            saveOrder(orderNumber, orderType, actionConfig.useNewApi)
        }
    }
}, 1000)

async function doCreateMobileOrder(config, actionConfig, success, error) {
    let orderType = 'vip'
    let pageData = config.pageData || {}
    delete config.pageData
    const loading = Vue.$pageLoading({
        frozen: true,
    })
    let [err1] = await toAwait(queryOrderStatus(orderType, actionConfig))
    if (err1) {
        loading.close()
        return
    }
    let params = {
        extraInfo: config.extraInfo,
        extraData: config.extraInfo,
        pageData: JSON.stringify({
            [Vue.bizIdName]: URLParams.id,
            pageName: config.page,
            fragmentName1: config.fragmentName1,
            pushCode: stat.getPushCode(),
            payPathType: config.payPathType,
            carStyle: URLParams.carStyle,
            kemu: URLParams.kemu,
            score12: URLParams.score12,
            fromPageCode: URLParams.from,
            fromPathCode: stat.getFromPathCode(),
            sceneCode: URLParams.sceneCode,
            patternCode: URLParams.patternCode,
            fromItemCode: URLParams.fromItemCode || '',
            abTest: (await abtest.getAbtest()).abTest,
            ...pageData,
        }),
        ...config,
    }
    let [err2, resData] = await toAwait(createGoodsOrder(params, {
        showErrorInfo: true,
    }))
    setTimeout(() => {
        loading.close()
    }, 500)
    if (err2) {
        return
    }
    if (resData.paid) {
        // 告诉app去刷新服务器接口，更新用户权限
        MCProtocol.Vip.boughtByCoupon()
        actionFactory(
            {
                orderNumber: resData.orderNumber,
                orderType,
                scene: 'buySuccess',
            },
            actionConfig
        )
        success && success({orderNumber: resData.orderNumber})
    } else {
        let {orderNumber} = resData
        gopay(
            {
                goodsKey: config.groupKey,
                content: resData.content,
                appleId: config.appleId,
                payChannel: config.payChannel,
                entityGoods: config.entityGoods,
                orderType,
                orderNumber,
            },
            actionConfig,
            success,
            error
        )

        const authToken = await getAuthToken()
        if (!authToken) {
            // 安卓端 未支付课程订单 不通过Vip.saveOrder存订单号
            if (!(isAndroid && actionConfig.useNewApi)) {
                saveOrder(orderNumber, orderType, actionConfig.useNewApi)
            }
        }
    }
}

async function gopay(orderInfo, actionConfig, success, error) {
    let {orderType, entityGoods, orderNumber, content, appleId, payChannel} = orderInfo
    if (isWeixin) {
        Pay.weixin.mp({
            content,
            callBack: function() {
                actionFactory(
                    {
                        orderNumber,
                        orderType,
                        scene: 'buySuccess',
                    },
                    actionConfig
                )
                success && success({orderNumber})
            },
            cancel: function() {
                error && error()
            },
        })
    } else if (isIOS && !entityGoods) {
        MCProtocol.Vip.makeApplePayment({
            appleId,
            content,
            orderNumber,
            serviceName: orderType,
            callback: data => {
                console.log('makeApplePayment参数', {
                    appleId,
                    content,
                    orderNumber,
                    serviceName: orderType,
                })
                console.log('makeApplePayment', data)
                if (data.success && data.data.status === 'success') {
                    actionFactory(
                        {
                            orderNumber,
                            orderType,
                            scene: 'buySuccess',
                        },
                        actionConfig
                    )
                    success && success({orderNumber})
                } else {
                    saveNopayOrder(orderInfo.goodsKey)
                    Vue.$EventBus.$emit('weizhifu')
                    error && error()
                }
            },
        })
    } else {
        let extraData = {
            vipType: 'vip_jk',
        }

        let url =
            'http://pay.nav.mucang.cn/pay?payType=vip&content=' +
            encodeURIComponent(content) +
            '&orderNumber=' +
            orderNumber +
            '&extraData=' +
            JSON.stringify(extraData) +
            '&payChannel=' +
            payChannel
        window.location.href = url
        try {
            await listenBuySuccess(orderNumber, orderType)
            actionFactory(
                {
                    orderNumber,
                    orderType,
                    scene: 'buySuccess',
                },
                actionConfig
            )
            success && success({orderNumber})
        } catch (e) {
            saveNopayOrder(orderInfo.goodsKey)
            Vue.$EventBus.$emit('weizhifu')
            error && error()
        }
    }
}

async function bindSuccess(environ, config) {
    // POINT
    let {orderNumber, orderType} = environ
    let {closeSelf, reFresh} = config
    await bindOrder(orderNumber, orderType)
    console.log(orderNumber + '：' + orderType + '绑定成功，删除订单')
    removeOrder(orderNumber, orderType, config.useNewApi)
    console.log('removeOrder', URLParams.id)
    if (config.sendMessage) {
        console.log('Vip.didFinishBindingOrders')
        MCProtocol.Vip.didFinishBindingOrders({
            serviceName: orderType,
            callback: data => {
                console.log('didFinishBindingOrders', data)
            },
        })
    }

    if (closeSelf) {
        webClose()
    } else if (reFresh) {
        toast(
            '您购买的服务已到账，快去使用吧！',
            2000,
            () => {
                reload()
            },
            {frozen: true}
        )
    }
}
async function actionFactory(environ, config) {
    let {scene, isBind, isPaid, orderNumber, orderType} = environ
    let {toStatus, closeSelf, reFresh, toLogin} = config

    if (scene === 'query') {
        if (isBind && isPaid) {
            console.log(orderNumber + '：已绑定的订单直接清除')
            removeOrder(orderNumber, orderType, config.useNewApi)
            if (closeSelf) {
                webClose()
            } else if (reFresh) {
                toast(
                    '您购买的服务已到账，快去使用吧！',
                    2000,
                    () => {
                        reload()
                    },
                    {frozen: true}
                )
            }
        } else {
            const authToken = await getAuthToken()
            // 如果未绑定，跳转登录
            if (!authToken) {
                // ios客户端，需要检测页面是否可见
                if (document.visibilityState === 'visible' || isAndroid) {
                    if (toStatus) {
                        webOpen({
                            url: `https://laofuzi.kakamobi.com/jiakaobaodian-zhibojian/status.html?orderType=${orderType}&id=${URLParams.id}`,
                            closeCurrent: +closeSelf,
                        })
                        if (closeSelf && isAndroid) {
                            webClose()
                        }
                    } else if (toLogin) {
                        toast('检测到您已购买，请登录后使用！', 2000, async () => {
                            await goLogin()
                            bindSuccess(environ, config)
                        })
                    }
                }
            } else {
                bindSuccess(environ, config)
            }
        }
    } else if (scene === 'buySuccess') {
        const authToken = await getAuthToken()
        if (!authToken) {
            // 安卓端 支付成功后 存到Vip.saveOrder
            if (isAndroid && config.useNewApi) {
                await saveOrder(orderNumber, orderType, config.useNewApi)
            }
            if (toStatus) {
                webOpen({
                    url: `https://laofuzi.kakamobi.com/jiakaobaodian-zhibojian/status.html?orderType=${orderType}&id=${URLParams.id}`,
                    closeCurrent: +closeSelf,
                })
                if (closeSelf && isAndroid) {
                    webClose()
                }
            } else if (toLogin) {
                toast('检测到您已购买，请登录后使用！', 2000, async () => {
                    await goLogin()
                    bindSuccess(environ, config)
                })
            }
        } else {
            if (closeSelf) {
                // 专项合集页直接刷新
                if (location.pathname.indexOf('collection.html') !== -1) {
                    setTimeout(() => location.reload(), 1000)
                } else {
                    webClose()
                }
            } else if (reFresh) {
                toast(
                    '您购买的服务已到账，快去使用吧！',
                    2000,
                    () => {
                        reload()
                    },
                    {frozen: true}
                )
            }
        }
    }
}

export function getPayAfterStrategy(isInDialog, goodsType) {
    if (goodsType === 'lesson') {
        if (isInDialog) {
            return {
                toStatus: true,
                closeSelf: true,
                reFresh: false,
                toLogin: false,
                sendMessage: true,
                useNewApi: true,
            }
        } else {
            return {
                toStatus: false,
                closeSelf: false,
                reFresh: true,
                toLogin: true,
                sendMessage: false,
                useNewApi: false,
            }
        }
    } else if (goodsType === 'training') {
        if (isInDialog) {
            return {
                toStatus: false,
                closeSelf: false,
                reFresh: false,
                toLogin: false,
                sendMessage: false,
                useNewApi: true,
            }
        } else {
            return {
                toStatus: false,
                closeSelf: false,
                reFresh: false,
                toLogin: false,
                sendMessage: false,
                useNewApi: false,
            }
        }
    } else {
        if (isInDialog) {
            return {
                toStatus: true,
                closeSelf: true,
                reFresh: false,
                toLogin: false,
                sendMessage: false,
                useNewApi: true,
            }
        } else {
            return {
                toStatus: false,
                closeSelf: false,
                reFresh: true,
                toLogin: true,
                sendMessage: false,
                useNewApi: false,
            }
        }
    }
}
