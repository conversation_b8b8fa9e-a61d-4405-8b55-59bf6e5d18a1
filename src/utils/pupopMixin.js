import {findIndex} from 'lodash-es'
export default {
    inject: {
        pupopList: {
            default: () => [],
        },
    },
    watch: {
        show(val) {
            if (val) {
                this.pupopList.push({
                    _uid: this._uid,
                    callback: () => {
                        this.close()
                    },
                })
            } else {
                let index = findIndex(this.pupopList, {_uid: this._uid})
                this.pupopList.splice(index, 1)
            }
        },
    },
}
