import {stat, pageSwitch} from '@jiakaobaodian/jiakaobaodian-utils'
import {URLParams} from './tools'
import {
    getLiveStatus,
    getLiveRoomStatus,
} from '../server/topLesson'
let pingTimer = null
export default {
    created() {
        this.startPing()
        pageSwitch.onPageShow(() => {
            this.startPing()
        })
        pageSwitch.onPageHide(() => {
            this.stopPing()
        })
    },
    beforeDestroy() {
        clearInterval(pingTimer)
    },
    methods: {
        startPing() {
            this.ping()
            pingTimer = setInterval(() => {
                this.ping()
            }, 3000)
        },
        stopPing() {
            clearInterval(pingTimer)
        },
        async ping() {
            let res
            if (this.liveRoomMode) {
                try {
                    res = await getLiveRoomStatus(
                        {
                            anchorId: URLParams.anchorId,
                            fromPageCode: URLParams.from,
                            fromPathCode: stat.getFromPathCode(),
                        },
                        {noConsole: true}
                    )
                } catch (error) {
                    this.pingError<PERSON><PERSON><PERSON>(error)
                }
            } else {
                res = await getLiveStatus(
                    {
                        id: URLParams.id,
                        fromPageCode: URLParams.from,
                        fromPathCode: stat.getFromPathCode(),
                    },
                    {noConsole: true}
                )
            }
            let {pv, status, recordStatus, danmuStatus, sessionId} = res
            if (status === 4) {
                status = 1
            }
            const roomStatus = {pv, status, playbackDone: recordStatus === 3, danmuStatus, sessionId}
            this.$store.commit('updateRoomStatus', roomStatus)
            let playStatus = 3
            if (roomStatus.status === 1) {
                playStatus = 1
            } else if (roomStatus.status === 3 && roomStatus.playbackDone) {
                playStatus = 2
            } else {
                playStatus = 3
            }
            this.$store.commit('updateBizConfig', {
                playStatus,
            })
        },
    },
}
