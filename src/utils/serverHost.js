import {NODE_ENV, VUE_APP_MODE} from './environment'
export const hosts = {}
export const signs = {}
export const resigns = {}

function addHost(key, config) {
    let host
    let {sign, resign} = config
    if (VUE_APP_MODE === 'prod') {
        host = config.host
    } else if (VUE_APP_MODE === 'test' || NODE_ENV === 'development') {
        host = config.testHost
    } else if (VUE_APP_MODE === 'dev') {
        host = config.devHost
    } else {
        host = config.host
    }
    hosts[key] = host
    signs[key] = sign
    resigns[key] = resign
}

addHost('sirius', {
    host: 'https://sirius.kakamobi.cn/',
    devHost: 'https://sirius2.dev.mucang.cn/',
    testHost: 'https://sirius2.ttt.mucang.cn/',
    sign: '*#06#c2uXpo9IeKaIkpyJdXipfGxs',
    resign: 'hello',
})
addHost('misc', {
    host: 'https://jiakao-misc.kakamobi.cn/',
    devHost: 'https://jiakao-misc.dev.mucang.cn/',
    testHost: 'https://jiakao-misc.ttt.mucang.cn/',
    sign: '*#06#j5moQpWNkIhrjaSFgodFh52T',
    resign: 'debug',
})
addHost('activity', {
    host: 'https://jiakao-activity.kakamobi.cn/',
    devHost: 'https://jiakao-activity.dev.mucang.cn/',
    testHost: 'https://jiakao-activity.ttt.mucang.cn/',
    sign: '*#06#j5moQpWNkIhrjaSFgodFh52T',
    resign: 'hello',
})
// addHost('danmu', {
//     host: 'https://danmu.kakamobi.cn/',
//     devHost: 'https://danmu.dev.mucang.cn/',
//     testHost: 'https://danmu.ttt.mucang.cn/',
//     sign: '*#06#mJKJRYSmi319h4hvcqWMiWpq',
//     resign: 'xx-oo-dd',
// })
addHost('monkey', {
    host: 'https://monkey.kakamobi.cn/',
    devHost: 'https://monkey.dev.mucang.cn/',
    testHost: 'https://monkey.ttt.mucang.cn/',
    sign: '*#06#PGuPbJiIkz2PeItsc5qKhItG',
    resign: 'debug',
})

addHost('swallow', {
    host: 'https://swallow.kakamobi.cn/',
    devHost: 'https://swallow.dev.mucang.cn/',
    testHost: 'https://swallow.ttt.mucang.cn/',
    sign: '*#06#j5moQpWNkIhrjaSFgodFh52T',
    resign: 'hello1',
})

addHost('config', {
    host: 'https://config.kakamobi.com/',
    devHost: 'https://config.ttt.mucang.cn/',
    testHost: 'https://config.ttt.mucang.cn/',
    sign: '*#06#d3pycm9DSYd6lndDckVwkzyZ',
    resign: '',
})

addHost('squirrel', {
    host: 'https://squirrel.kakamobi.cn/',
    devHost: 'https://squirrel.dev.mucang.cn/',
    testHost: 'https://squirrel.ttt.mucang.cn/',
    sign: '*#06#iKVuc32KRW12cqg8QnGkdX16',
    resign: 'helloworld',
})

addHost('auth', {
    // https://auth.kakamobi.com
    // https://auth.mucang.cn
    host: 'https://auth.kakamobi.com/',
    devHost: 'https://auth.kakamobi.com/',
    testHost: 'https://auth.kakamobi.com/',
    sign: '',
    resign: '',
})

addHost('panda', {
    host: 'https://panda.kakamobi.cn/',
    devHost: 'https://panda.dev.mucang.cn/',
    testHost: 'https://panda.ttt.mucang.cn/',
    sign: '*#06#l5J2nW13l3qEfHdubKKmPJyj',
    resign: 'debug',
})

addHost('pony', {
    host: 'https://pony.kakamobi.cn/',
    devHost: 'https://pony.dev.mucang.cn/',
    testHost: 'https://pony.ttt.mucang.cn/',
    sign: '*#06#bJaWjoOnhaN9pXCCbqiSoqd2',
    resign: 'hello',
})
addHost('parrot', {
    host: 'https://parrot.kakamobi.cn/',
    devHost: 'https://parrot.dev.mucang.cn/',
    testHost: 'https://parrot.ttt.mucang.cn/',
    sign: '',
    resign: 'debug',
})