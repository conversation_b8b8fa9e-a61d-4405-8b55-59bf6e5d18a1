import Vue from 'vue'
import smsLoginComp from '../components/verifyCode.vue'
let instance
export default function(config) {
    let {callback} = config
    let SmsLoginConstructor = Vue.extend(smsLoginComp)

    instance = new SmsLoginConstructor()
    instance.$on('smsLoginSuccess', function() {
        instance.vm.visible = false
        callback()
    })
    instance.vm = instance.$mount()
    document.body.appendChild(instance.vm.$el)
    instance.vm.visible = true
}
