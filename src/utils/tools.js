import Vue from 'vue'
import {MCProtocol, MCBaseStore, MCBaseUtils} from '@simplex/simple-base'
import {stat} from '@jiakaobaodian/jiakaobaodian-utils'
import {hosts, signs, resigns} from './serverHost'
import {provide} from '../server/common'
import {getOrderStatus} from '../server/goods'
import {newGetOrderStatus} from '../server/newGoods'
import {orderUnbind} from '../server/xueche66'
import {reload, navigateTo} from './jump'
import {LOCATION} from './constant'
import mcLogin from '../libs/login'
import {once, cloneDeep} from 'lodash-es'

const ua = window.navigator.userAgent.toLowerCase()
export const isMucang = ua.indexOf('mucang') > -1
export const isWeixin = ua.indexOf('micromessenger') > -1
export const isAndroid = ua.indexOf('android') > -1
export const isTablet =
    /(?:ipad|playbook)/.test(ua) ||
    (isAndroid && !/(?:mobile)/.test(ua)) ||
    (ua.indexOf('android') > -1 && document.documentElement.clientWidth >= 480)
export const isIOS = ua.indexOf('iphone') > -1 || ua.indexOf('ipad') > -1 || ua.indexOf('ipod') > -1
export const isIpad = ua.indexOf('ipad') > -1
export const isHarmony = ua.indexOf('harmony') > -1

let search = getURLParams()
if (!search.kemu) {
    search.kemu = search.kemuStyle
}
if (search.kemu && !isNaN(search.kemu)) {
    search.kemu = 'kemu' + search.kemu
}
search.kemuNum = (search.kemu || '').slice(4)
search._product = search._product || '驾考宝典'
search._product = search._product === '驾考宝典助手' ? '驾考宝典' : search._product

export let URLParams = search
console.log(getURLParams())

export const isMajorApp =
    (URLParams._appName === 'jiakaozhushou' && isIOS) ||
    (URLParams._appName === 'jiakaobaodian' && isAndroid)

export const isXueTang = URLParams._appName === 'jiakaoxuetang' || URLParams._appName === 'jiakaoduoyuyan'

export let IOSVersion = ua.match(/cpu iphone os (.*?) like mac os/)
IOSVersion = IOSVersion && IOSVersion[1]
IOSVersion = IOSVersion && IOSVersion.split('_')
IOSVersion && (IOSVersion.length = Math.min(2, IOSVersion.length))
IOSVersion = IOSVersion && +IOSVersion.join('.')

export let androidVersion = ua.match(/android\s([0-9.]*)/)
androidVersion = androidVersion && androidVersion[1]
androidVersion = androidVersion && androidVersion.split('.')
androidVersion && (androidVersion.length = Math.min(2, androidVersion.length))
androidVersion = androidVersion && +androidVersion.join('.')

let fontSize = parseFloat(document.documentElement.style.fontSize)

MCProtocol.register('Vip.boughtByCoupon', function() {
    return {}
})

MCProtocol.register('Vip.BuyGoods', function(config) {
    return config
})

// 'lesson.luban.mucang.cn/setOrientation'
MCProtocol.register('lesson.setOrientation', function(config) {
    return config
})

MCProtocol.register('core.native.storeReview', function(config) {
    return config
})

MCProtocol.register('Vip.makeApplePayment', function(config) {
    return config
})

MCProtocol.register('Vip.show', function(config) {
    return config
})

MCProtocol.register('Listener.buyResult', function(callback) {
    return {
        callbackName: 'buyResult',
        callback: function(data) {
            callback(data.data)
        },
    }
})

MCProtocol.register('Pay.channels', function(callback) {
    return {
        callback: function(data) {
            if (typeof data === 'object') {
                callback(data)
            } else if (typeof data === 'string') {
                callback(JSON.parse(data))
            }
        },
    }
})

MCProtocol.register('Vip.getPassRate', function(config) {
    return {
        car: config.car,
        kemu: config.kemu,
        callback: config.callback,
    }
})

MCProtocol.register('Vip.getGoodsContent', function(config) {
    return config
})

MCProtocol.register('jiakao-global.web.getExamDate', function(config) {
    return config
})

MCProtocol.register('jiakao-global.web.showExamDateAlert', function(config) {
    return config
})

let androidBizVersionMap = {
    1: '8.4.2',
    2: '8.4.3',
    3: '8.4.4',
    4: '8.4.5',
}
let iosBizVersionMap = {
    1: '8.4.8',
    2: '8.4.9',
    3: '8.5.0',
    4: '8.5.1',
}

// https://mckj.feishu.cn/docs/doccnMvq1eFEUMchMoIrG68V3Ig
function getVersionFromBiz(bizVersion) {
    let currentVersion
    if (isAndroid) {
        currentVersion = androidBizVersionMap
    } else {
        currentVersion = iosBizVersionMap
    }
    let keys = Object.keys(currentVersion)
    keys.sort()
    let lastVersion = keys.pop()
    return currentVersion[bizVersion] || currentVersion[lastVersion]
}

export function compareVersionComplex(targetVersion) {
    // 通过_appName判断是否使用bizVersion参数
    if (isMajorApp) {
        return MCBaseUtils.compareVersion(search._version, targetVersion)
    } else if (search.bizVersion) {
        let currentVersion = getVersionFromBiz(+search.bizVersion)
        return MCBaseUtils.compareVersion(currentVersion, targetVersion)
    } else {
        return MCBaseUtils.compareVersion(search._version, targetVersion)
    }
}
export function goLogin(config = {}) {
    return new Promise(resolve => {
        let _login = async () => {
            const resData = await login(config)
            if (!config.refresh) {
                Vue.$EventBus.$emit('onLogin')
            }
            resolve(resData)
        }
        if (Vue.pageMode === 'liveRoom') {
            Vue.$EventBus.$emit('setOrientation', 'portrait', async () => {
                _login()
            })
        } else {
            _login()
        }
    })
}
function login(config = {}) {
    return new Promise(resolve => {
        let done = false
        const callback = () => {
            if (done) return
            done = true
            if (config.refresh) {
                reload()
            } else {
                resolve()
            }
        }
        window.loginSuccess = () => {
            callback()
        }
        if (isMucang) {
            MCProtocol.Core.User.login({
                from: 'jiakaobaodian',
                skipAuthRealName: true,
                pageType: 'quicklogin',
                callback: (data) => {
                    if (data && data.success) {
                        callback()
                        console.log('login success')
                    }
                },
            })
        } else {
            mcLogin({
                frozen: config.frozen,
                callback: () => {
                    console.log('login callback')
                    callback()

                    statInit()
                },
            })
        }
    })
}

export function listenBuySuccess(orderNumber, orderType) {
    return new Promise((resolve, reject) => {
        // 低版本没有buySuccess回调，
        // 通过监听页面可见状态 实现购买后反馈
        if ((isAndroid && compareVersionComplex('8.2.3') <= 0) || isIOS) {
            // 监听回到页面
            let isHiden = false
            let checkPagevisible
            let times = 0
            let checkFun = function() {
                console.log('checkPagevisible')
                var visibilityState = document.visibilityState
                if (visibilityState === 'visible' && isHiden) {
                    // 当页面可见时，表示已经回到页面
                    clearInterval(checkPagevisible)
                    if (orderType === 'xueche') {
                        orderUnbind({orderNumber}).then(resData => {
                            if (resData && resData.value) {
                                resolve()
                            } else {
                                reject(new Error())
                            }
                        })
                    } else if (orderType === 'vip') {
                        getOrderStatus({orderNumber}).then(resData => {
                            if (resData.status >= 1) {
                                resolve()
                            } else {
                                reject(new Error())
                            }
                        })
                    } else if (orderType === 'training') {
                        newGetOrderStatus({orderNumber}).then(resData => {
                            if (resData.status >= 1) {
                                resolve()
                            } else {
                                reject(new Error())
                            }
                        })
                    }
                } else if (visibilityState !== 'visible') {
                    // 在跳转过程中查询页面可见状态，会显示'visible'
                    // 因此判断页面从其他页面返回回来，需要2个条件
                    // 1.页面可见，2.页面经过一次由可见变为不可见
                    isHiden = true
                } else {
                    // 防止支付SDK没被调起来
                    // ios，未安装支付宝，使用支付宝网页支付，页面visibilityState不会改变，因此手动检测一次支付状态
                    if (times > 30) {
                        isHiden = true
                    }
                    times++
                }
            }
            clearInterval(checkPagevisible)
            checkPagevisible = setInterval(() => {
                checkFun()
            }, 500)
            checkFun()
        } else {
            // 缓存已注册的支付成功事件，如用户取消支付成功，
            // 把此回调再次挂载到支付成功成功事件
            let _buySuccess = window.buySuccess
            window.buyCancel = () => {
                window.buySuccess = _buySuccess
                reject(new Error())
            }
            window.buyFailed = () => {
                window.buySuccess = _buySuccess
                reject(new Error())
            }
            window.buySuccess = () => {
                resolve()
            }
        }
    })
}

export function webOpen(config) {
    let {url} = config
    let urlParams = getURLParams(null, url)
    // url上如果没有kemu或者kemuStyle,手动带上直播间的kemu
    if (!(urlParams.kemu || urlParams.kemuStyle) && search.kemuStyle) {
        config.url = getUrl(url, {kemuStyle: search.kemuNum})
    }
    if (isMucang) {
        MCProtocol.Core.Web.open(config)
    } else {
        navigateTo(url)
    }
}

function getDefaultKey() {
    const defaultKeys = [
        'carStyle',
        'kemuStyle',
        'score12',
        'sceneCode',
        'patternCode',
        'fromPage',
        'fragmentName1',
        'fromPageCode',
        'fromItemCode',
        'questionId',
        'subject',
        'courseId',
        'bizVersion',
        'fromPathCode',
    ]

    return defaultKeys.join(',')
}

/** 新开一个vip的webview */
export function openVipWebView(config) {
    if (!isMucang) {
        window.open(config.url)
        return
    }

    if (isIOS) {
        webOpen({
            url:
                'http://jiakao.nav.mucang.cn/buyWebView?url=' +
                encodeURIComponent(config.url) +
                '&keys=' +
                getDefaultKey(),
        })
    } else {
        webOpen({
            url: 'http://jiakao.nav.mucang.cn/vip/new-vip?page=' + encodeURIComponent(config.url),
        })
    }
}
export function openNewVip(config) {
    if (!isMucang) {
        window.open(config.url)
        return
    }

    webOpen({
        url: 'http://jiakao.nav.mucang.cn/vip/new-vip?page=' + encodeURIComponent(config.url),
    })
}

export function toast(message, time, callback, options = {}) {
    let {foz, frozen} = options
    if (!message) {
        return
    }

    if (document.getElementById('myToast')) {
        document.body.removeChild(document.getElementById('myToast'))
    }

    var div = document.createElement('div')
    var mask
    div.innerText = message

    div.setAttribute('id', 'myToast')

    div.style.position = 'fixed'
    div.style.left = '50%'
    div.style.top = '50%'
    div.style.transform = 'translate(-50%, -50%)'
    div.style.webkitTransform = 'translate(-50%, -50%)'
    div.style.background = 'rgba(0, 0, 0, 0.7)'
    div.style.zIndex = 9999
    div.style.padding = '0.15rem'
    div.style.borderRadius = '8px'
    div.style.textAlign = 'center'
    div.style.color = '#ffffff'
    div.style.maxWidth = '85%'
    div.style.fontSize = foz || '0.28rem'
    div.style.lineHeight = '1.5'

    time = time || 2000
    document.body.appendChild(div)
    if (frozen) {
        mask = document.createElement('div')
        mask.style.position = 'fixed'
        mask.style.left = '0'
        mask.style.top = '0'
        mask.style.width = '100%'
        mask.style.height = '100%'
        mask.style.zIndex = 9998

        document.body.appendChild(mask)
    }
    setTimeout(function() {
        div.remove()
        mask && mask.remove()
        callback && callback()
    }, time)
}

export function convertParamsStr(data) {
    let url = ''
    for (var k in data) {
        let value = data[k] !== undefined ? data[k] : ''
        url += `&${k}=${encodeURIComponent(value)}`
        // url += `&${k}=${value}`
    }
    return url ? url.substring(1) : ''
}

export function getUrl(url, data) {
    var params = getURLParams(null, url)
    var hash = url.match(/#/) ? url.split('#')[1] : ''
    params = Object.assign(params, data)
    url = url.substring(0, url.indexOf('?') > 0 ? url.indexOf('?') : url.length)
    url = url.substring(0, url.indexOf('#') > 0 ? url.indexOf('#') : url.length)
    url = (url += '?' + convertParamsStr(params))
    // 把丢失的#hash带上
    if (hash) {
        url = (url += '#' + hash)
    }
    return url
}
function paramsExtra(params) {
    return Object.assign({
        [Vue.bizIdName]: search.id,
        kemu: search.kemu,
        carStyle: search.carStyle,
        score12: search.score12,
        sceneCode: search.sceneCode,
        patternCode: search.patternCode,
    }, params)
}
let isRegisterPageLoad = false
export function trackPageShow(params) {
    if (!isRegisterPageLoad) {
        stat.trackPageLoad(paramsExtra(params))
        isRegisterPageLoad = true
    }
    stat.trackPageShow(paramsExtra(params))
}
export function trackEvent(params) {
    stat.trackEvent(paramsExtra(params))
}
export async function statInit() {
    let authToken = ''
    if (!isMucang) {
        authToken = localStorage.getItem(LOCATION.AUTHTOKEN)
    }
    stat.init({
        appName: 'jiakaobaodian',
        joinStr: '_',
        authToken,
    })
}
export function getUUID() {
    var S4 = function() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    }

    return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4()
}

export function getDetailUrl(url) {
    if (!url) return url
    let skipFromItemCode = getURLParams(null, url).skipFromItemCode
    if (
        !(
            skipFromItemCode === 'true' &&
            isAndroid &&
            MCBaseUtils.compareVersion(URLParams._version, '8.3.2') < 0
        )
    ) {
        url = getUrl(url, {
            fromItemCode: search.id,
        })
    }
    return url
}

export function isVIVO() {
    var manufacturer = (URLParams._manufacturer && URLParams._manufacturer.toLowerCase()) || ''

    if (manufacturer.indexOf('vivo') > -1) {
        return true
    }

    return false
}
export function isOppo() {
    var manufacturer = (URLParams._manufacturer && URLParams._manufacturer.toLowerCase()) || ''

    if (manufacturer.indexOf('oppo') > -1) {
        return true
    }

    return false
}
function getUserInfo() {
    return new Promise(resolve => {
        if (isMucang) {
            MCProtocol.Core.User.get(data => {
                let obj = ''
                if (data.success.toString() === 'true') {
                    obj = data.data
                }
                resolve(obj)
            })
        } else {
            let userinfo = localStorage.getItem(LOCATION.USERINFO)
            if (userinfo) {
                userinfo = JSON.parse(userinfo)
                resolve(userinfo)
            } else {
                resolve('')
            }
        }
    })
}
export function getAuthToken() {
    return new Promise(resolve => {
        if (isMucang) {
            getUserInfo().then(data => {
                resolve(data.authToken)
            })
        } else {
            resolve(search.authToken || localStorage.getItem(LOCATION.AUTHTOKEN))
        }
    })
}
export function getMucangId() {
    return new Promise(resolve => {
        getUserInfo().then(data => {
            resolve(data.mucangId)
        })
    })
}
export function checkPayChannels() {
    return new Promise(resolve => {
        try {
            if (isMucang) {
                MCProtocol.Pay.channels(data => {
                    resolve(data.data)
                })
            } else {
                resolve()
            }
        } catch {
            resolve()
        }
    })
}
export function getData(key) {
    return new Promise(resolve => {
        try {
            if (isMucang) {
                MCProtocol.Core.System.getcache({
                    key: key,
                    callback: data => {
                        resolve(JSON.parse(data || '{}'))
                    },
                })
            } else {
                resolve(JSON.parse(localStorage.getItem(key) || '{}'))
            }
        } catch (err) {
            resolve()
        }
    })
}
export function saveData(config) {
    if (isMucang) {
        MCProtocol.Core.System.setcache(config)
    } else {
        localStorage.setItem(config.key, config.value)
    }
}
export function getURLParams(filter, url) {
    var params = {}
    var filterRegx

    if (!url) {
        url = window.location.href
    }
    // 排除#hash
    url = url.match(/#/) ? url.split('#')[0] : url

    url.replace(/[#|?&]+([^=#|&]+)=([^#|&]*)/gi, function(m, key, value) {
        filterRegx = new RegExp(filter, 'gi')

        params[key] = decodeURIComponent(value)

        if (filter && !key.match(filterRegx)) {
            delete params[key]
        }
    })

    return params
}
function getRandomR(a) {
    var c = Math.abs(parseInt(new Date().getTime() * Math.random() * 10000)).toString()
    var d = 0
    var b
    var e

    for (b = 0; c.length > b; b++) {
        d += parseInt(c[b])
    }
    e = (function(f) {
        return function(g, h) {
            return h - '' + g.length <= 0 ? g : (f[h] || (f[h] = Array(h + 1).join(0))) + g
        }
    })([])

    d += c.length
    d = e(d, 3 - d.toString().length)
    return a.toString() + c + d
}
export async function httpRequest(config) {
    let from = search.from || 'unknown'
    let params = config.params || {}
    let method = config.method || 'get'
    let host = config.host || 'sirius'
    let hostName = hosts[host]
    let sign = signs[host]
    let resign = resigns[host]

    const authToken = await getAuthToken()
    if (authToken && !params.authToken) {
        params.authToken = authToken
    }

    return new Promise((resolve, reject) => {
        let showErrorInfo = config.showErrorInfo
        delete config.showErrorInfo

        let failed = data => {
            let msg = data?.statusText || data?.message
            if (showErrorInfo) {
                toast(msg)
            }
            reject(data)
        }

        params.from = from
        params.bizCode = URLParams.bizCode
        params.bizVersion = URLParams.bizVersion
        params.sceneCode = URLParams.sceneCode
        params.patternCode = URLParams.patternCode

        if (!isMucang) {
            params.resign = resign
            params._r = getRandomR(1)
            params._appUser = search._appUser || getAppUser()
        }

        /* global vConsole */
        let item = {}
        let resData = {}

        MCBaseStore.extend({
            url: hostName + config.url,
            sign: sign,
            method: method,
            type: 'online',
        })
            .create({errorToast: false})
            .request(params).then((res, data) => {
                resolve(dataWebpHelper(res))

                if (isMucang && window.vConsole && vConsole.network) {
                    vConsole.network.update(item.id, {
                        status: data.statusCode,
                        // readyState: XMLHttpRequest['readyState'],
                        // responseType: XMLHttpRequest['responseType'],
                        // requestType: 'xhr',
                        response: data.data,
                        endTime: +new Date(),
                        ...resData,
                    })
                }
            }).fail((errorCode, error) => {
                let errorData = {}
                try {
                    errorData = JSON.parse(error.text)
                } catch (error) {}
                if (errorData?.errorCode === 403) {
                    localStorage.removeItem(LOCATION.AUTHTOKEN)
                    localStorage.removeItem(LOCATION.USERINFO)
                    if (!config.unloginDoNotReload) {
                        window.location.reload()
                    }
                }
                failed(errorData)

                if (isMucang && window.vConsole && vConsole.network) {
                    vConsole.network.update(item.id, {
                        status: error.statusCode,
                        // readyState: XMLHttpRequest['readyState'],
                        // responseType: XMLHttpRequest['responseType'],
                        // requestType: 'xhr',
                        response: errorData,
                        endTime: +new Date(),
                        ...resData,
                    })
                }
            })

        if (isMucang && window.vConsole && window.vConsole.network) {
            item = window.vConsole.network.add({
                method: method,
                url: hostName + config.url,
                startTime: +new Date(),
            })
            resData = {
                [method.toLowerCase() + 'Data']: params,
            }
        }
    })
}

export function getType(obj) {
    let str = Object.prototype.toString.call(obj) // 检测基本类型值，引用类型值的类型
    let map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'unfefined',
        '[object Null]': 'null',
        '[object Object]': 'object',
    }
    return map[str]
}
export function formatDate(date, fmt) {
    date = new Date(date)
    var o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        S: date.getMilliseconds(), // 毫秒
    }
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
            )
        }
    }
    return fmt
}
export function formatPrice(price) {
    if (!price) return price
    if (price % 100 > 0) {
        if ((price % 100) % 10 > 0) {
            price = (price / 100).toFixed(2)
        } else {
            price = (price / 100).toFixed(1)
        }
    } else {
        price = (price / 100).toFixed(0)
    }
    return price - 0
}
export function dataWebpHelper(data) {
    if (isIOS && IOSVersion < 14) {
        return webpSupportRecursion(data)
    } else {
        return data
    }
}
export function webpSupportRecursion(data) {
    let dataType = getType(data)
    let result
    if (dataType === 'array') {
        result = data.map(item => {
            return webpSupportRecursion(item)
        })
    } else if (dataType === 'object') {
        result = {}
        for (let key in data) {
            result[key] = webpSupportRecursion(data[key])
        }
    } else {
        result = webpSupport(data)
    }
    return result
}
export function webpSupport(src) {
    let type = getType(src)
    if (type === 'string') {
        if (src.match(/\.webp$/) && IOSVersion >= 11 && IOSVersion < 14 && isMucang) {
            src = src.replace('https:', 'mc-ios-core-webp-image:')
        } else if (src.match(/\.(.+)!\1$/)) {
            src = src.replace(/(!.*)$/gi, '')
        }
    }
    return src
}
export function formatPriceRecursion(
    data,
    fields = [
        'price',
        'originPrice',
        'originalPrice',
        'applePrice',
        'preDiscountApplePrice',
        'preDiscountPrice',
        'priceCent',
        'salePrice',
        'channelPrice',
        'priceDiff',
    ]
) {
    let dataType = getType(data)
    let result
    if (dataType === 'array') {
        result = data.map(item => {
            return formatPriceRecursion(item, fields)
        })
    } else if (dataType === 'object') {
        result = {}
        for (let key in data) {
            let itemType = getType(data[key])
            if (itemType === 'array' || itemType === 'object') {
                result[key] = formatPriceRecursion(data[key], fields)
            } else {
                if (fields.indexOf(key) > -1) {
                    result[key] = formatPrice(data[key])
                } else {
                    result[key] = data[key]
                }
            }
        }
    } else {
        result = data
    }
    return result
}

export function formatRemainTime(ms, fmt) {
    let isShowHour = fmt.match(/h/)
    let isShowMinut = fmt.match(/m/)
    var at = ms
    var h, m, s, S
    if (at <= 0) {
        h = m = s = S = 0
    } else {
        if (isShowHour) {
            h = Math.floor(at / 3600000)
            at = at - h * 3600000
        }
        if (isShowMinut) {
            m = Math.floor(at / 60000)
            at = at - m * 60000
        }
        s = Math.floor(at / 1000)
        at = at - s * 1000
        S = Math.min(Math.floor(at / 10), 99)
    }
    var o = {
        'h+': h, // 小时
        'm+': m, // 分
        's+': s, // 秒
        'S+': S, // 毫秒
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
            )
        }
    }
    return fmt
}

// arrayEquals([1,2], [1]) => false
// arrayEquals([1,2], [1,2]) => true
// arrayEquals([1,2], [1,2,3]) => false
// arrayEquals([1,2], [2,1]) => true
export function arrayEquals(arrayA, arrayB) {
    // if the other array is a falsy value, return
    if (!arrayA || !arrayB) return false

    // compare lengths - can save a lot of time
    if (arrayA.length !== arrayB.length) return false
    arrayA.sort()
    arrayB.sort()
    for (var i = 0, l = arrayA.length; i < l; i++) {
        // Check if we have nested arrays
        if (arrayA[i] instanceof Array && arrayB[i] instanceof Array) {
            // recurse into the nested arrays
            if (!arrayEquals(arrayA[i], arrayB[i])) return false
        } else if (arrayA[i] !== arrayB[i]) {
            // Warning - two different object instances will never be equal: {x:20} != {x:20}
            return false
        }
    }
    return true
}
export function answerFn(question) {
    var answer = {
        selectList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
        optionTypes: ['判断题', '单选题', '多选题'],
        question: question,
        initQuestion: function() {
            var question = this.question
            var options = this.selectList.map((item, index) => {
                var option = {}
                if (question[`option${item}`]) {
                    option = {
                        showKey: item,
                        value: question[`option${item}`],
                        correct: !!((16 << index) & question.answer),
                    }
                }
                return option
            })
            options = options.filter(ele => {
                return ele.showKey
            })
            // 当前题目的Id
            this.qId = question.questionId

            this.starTitle = question.starTitle
            this.starImgUrl = question.starImgUrl
            // 展示题目标题
            this.title = question.question
            // 展示媒体类型
            this.mediaType = question.mediaType
            // 展示题目类型
            this.showOptionType = this.optionTypes[question.optionType]
            // 题目类型
            this.optionType = question.optionType
            // 选项列表
            this.options = options
            // 媒体数据
            this.mediaContent = question.mediaUrl
            // 正确选项列表
            this.correctOptions = this.getCorrectOptions()
            // 正确选项字符串
            this.StrCorrect = this.getStrCorrect()
            // 题目的状态[1,2,3] 1表示未做 2表示做对了 3表示做错了
            this.isCorrent = 1
            // 用户选择答案
            this.userSelect = {}
            // 是否被收藏
            this.collection = false

            this.showExplain = question.explain || '暂无'

            // vip技巧
            this.illiteracyExplain = question.illiteracyExplain || '   '

            // vip技巧解析
            this.conciseExplain = question.conciseExplain
        },
        getCorrectOptions() {
            return this.options.filter(function(item) {
                return item.correct
            })
        },
        getStrCorrect() {
            return this.getCorrectOptions()
                .map(function(item) {
                    return item.showKey
                })
                .join(',')
        },
    }
    answer.initQuestion()
    return answer
}

// let url = 'http://jiakao.nav.mucang.cn/vip/new-vip?from=201&page=%2fjkbd-vip%2findex%2findex.html%3FactivePanel%3Dpanel2&pagename=allKemu'
// let query = 'page'
// let params = {fromItemCode: 3102}
// let newUrl = addParamsForUrlQueryFun(url, query, params)
// console.log(newUrl) > 'http://jiakao.nav.mucang.cn/vip/new-vip?from=201&page=%2Fjkbd-vip%2Findex%2Findex.html%3FactivePanel%3Dpanel2%26fromItemCode%3D3102&pagename=allKemu'
export function addParamsForUrlQueryFun(url, query, params) {
    let urlQuery = getURLParams(null, url)[query]

    if (urlQuery) {
        urlQuery = getUrl(urlQuery, params)

        url = getUrl(url, {
            [query]: urlQuery,
        })
    }
    return url
}

// input自带的select()方法在苹果端无法进行选择，所以需要自己去写一个类似的方法
// 选择文本。createTextRange(setSelectionRange)是input方法
function selectText(textbox, startIndex, stopIndex) {
    if (textbox.createTextRange) {
        // ie
        var range = textbox.createTextRange()
        range.collapse(true)
        range.moveStart('character', startIndex) // 起始光标
        range.moveEnd('character', stopIndex - startIndex) // 结束光标
        range.select() // 不兼容苹果
    } else {
        // firefox/chrome
        textbox.setSelectionRange(startIndex, stopIndex)
        textbox.focus()
    }
}
export function copyText(text) {
    let ret = false
    // 数字没有 .length 不能执行selectText 需要转化成字符串
    var textString = text.toString()
    var input = document.querySelector('#copy-input')
    if (!input) {
        input = document.createElement('input')
        input.id = 'copy-input'
        input.readOnly = 'readOnly' // 防止ios聚焦触发键盘事件
        input.style.position = 'absolute'
        input.style.left = '-1000px'
        input.style.zIndex = '-1000'
        document.body.appendChild(input)
    }

    input.value = textString
    // ios必须先选中文字且不支持 input.select();
    selectText(input, 0, textString.length)
    if (document.execCommand('copy')) {
        document.execCommand('copy')
        ret = true
    } else {
        console.log('不兼容')
    }
    input.blur()

    return ret
}

export function addClass(elem, str) {
    let classNames = elem.className ? elem.className.split(' ') : []
    if (classNames.indexOf(str) === -1) {
        classNames.push(str)
        elem.className = classNames.join(' ')
    }
}

export function removeClass(elem, str) {
    let classNames = elem.className ? elem.className.split(' ') : []
    let index = classNames.indexOf(str)
    if (index !== -1) {
        classNames.splice(index, 1)
        elem.className = classNames.join(' ')
    }
}

export function convertPixelFromUI(value) {
    return ((value / 2) * fontSize) / (100 / 2)
}

const checkTime = 600 * 1000

function sendWarning(pathType, errorReason) {
    let pageName = '公共页'
    let fragmentName1 = 'h5页面'
    let actionType = '触发'
    let actionName = '新版本命中sirius'

    // 公共页_h5页面_触发新版本命中sirius
    stat.trackEvent({
        pageName,
        fragmentName1,
        actionType,
        actionName,
        pageUrl: location.href,
        _pageName: stat.getPageName(),
        pathType,
        errorReason,
        eventId: 'debug',
    })
}

// 获取请求的接口到底是squ还是sir
export const getProvide = once(async () => {
    if (URLParams.apiHost === 'squirrel' || !isMucang) {
        return true
    }

    const time = new Date().getTime()
    const hostObj = await getData('hostApi')

    if (hostObj && time - hostObj.time <= checkTime) {
        return hostObj.value
    }
    try {
        let resData = await provide()
        saveData({
            key: 'hostApi',
            value: JSON.stringify({
                time: new Date().getTime(),
                value: resData.value,
            }),
        })

        if (!resData.value && compareVersionComplex('8.29.0') >= 0) {
            sendWarning(1)
        }
        return resData.value
    } catch (error) {
        sendWarning(2, error)
        if (hostObj) {
            return hostObj.value
        } else {
            return true
        }
    }
})
export function loadScript(url) {
    let script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = url
    script.async = false
    document.head.appendChild(script)
    return new Promise(resolve => {
        script.onload = function() {
            resolve()
        }
    })
}

export function loadStyle(url) {
    let style = document.createElement('link')
    style.rel = 'stylesheet'
    style.href = url
    document.head.appendChild(style)
}

export const getMapfromArray = function(store) {
    var obj = {}
    store.forEach(item => {
        obj[item.key] = item.value
    })
    return obj
}

export const wakeup = function(url) {
    if (!url) {
        url = 'http://jiakao.nav.mucang.cn/tab?name=jiakao'
    }
    try {
        // TODO oppo
        window.location.href = `mucang-jiakaobaodian://gateway?navUrl=${encodeURIComponent(url)}`
        throw new Error()
    } catch (error) {
        setTimeout(function() {
            if (!document.hidden) {
                window.location.href =
                    'https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-jiaoliantuiguang/down1.html?channelCode=zhibo'
            }
        }, 500)
    }
}

/** 获取系统信息 */
export function getSystemInfo() {
    return new Promise(resolve => {
        if (isMucang) {
            MCProtocol.Core.System.info(baseParams => {
                if (baseParams.data) {
                    if (typeof baseParams.data === 'string') {
                        baseParams = JSON.parse(baseParams.data)
                    } else {
                        baseParams = baseParams.data
                    }
                } else if (typeof baseParams === 'string') {
                    baseParams = JSON.parse(baseParams)
                }
                resolve(baseParams)
            })
        } else {
            resolve(search)
        }
    })
}

export function secToTime(t, ellipticMode = false) {
    var at = t
    var h = Math.floor(at / 3600)
    at = at - h * 3600
    var m = Math.floor(at / 60)
    at = at - m * 60
    var s = Math.floor(at / 1)
    let text = ''
    if (h < 10) {
        h = '0' + h
    }
    if (m < 10) {
        m = '0' + m
    }
    if (s < 10) {
        s = '0' + s
    }
    if (t >= 3600 || !ellipticMode) {
        text += `${h}:`
    }
    text += `${m}:`
    text += `${s}`

    return text
}

export function toChineseNum(num) {
    let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    let unit = ['', '十', '百', '千', '万']
    num = parseInt(num)
    let getWan = temp => {
        let strArr = temp
            .toString()
            .split('')
            .reverse()
        let newNum = ''
        let newArr = []
        strArr.forEach((item, index) => {
            newArr.unshift(item === '0' ? changeNum[item] : changeNum[item] + unit[index])
        })
        let numArr = []
        newArr.forEach((m, n) => {
            if (m !== '零') numArr.push(n)
        })
        if (newArr.length > 1) {
            newArr.forEach((m, n) => {
                if (newArr[newArr.length - 1] === '零') {
                    if (n <= numArr[numArr.length - 1]) {
                        newNum += m
                    }
                } else {
                    newNum += m
                }
            })
        } else {
            newNum = newArr[0]
        }
        return newNum
    }
    let overWan = Math.floor(num / 10000)
    let noWan = num % 10000
    if (noWan.toString().length < 4) {
        noWan = '0' + noWan
    }
    return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
}

export const getOpenid = once(async () => {
    return new Promise(resolve => {
        window.wxActive.weixinAuth(data => {
            resolve(data.openid)
        })
    })
})

export const getPath = function(url, base) {
    if (!base) {
        base = location.href
    }
    return new URL(url, base).href
}

export const getAppUser = function() {
    var appUser = window.localStorage.getItem('_appUser')

    if (!appUser) {
        appUser = getUUID()
        window.localStorage.setItem('_appUser', appUser)
    }

    return appUser
}

export function getLocation() {
    return new Promise(resolve => {
        window.AMap.plugin('AMap.Geolocation', function() {
            var geolocation = new window.AMap.Geolocation({
                enableHighAccuracy: true,
                timeout: 200,
                buttonOffset: new window.AMap.Pixel(10, 20),
                zoomToAccuracy: true,
                buttonPosition: 'RB',
            })
            geolocation.getCityInfo((status, result) => {
                resolve(result)
            })
        })
    })
}

export const getCityCode = once(async () => {
    // await loadScript('https://webapi.amap.com/maps?v=2.0&key=b051b06edda886b9377fbba47a85c6f0&plugin=AMap.CitySearch');
    await loadScript(
        'https://webapi.amap.com/maps?v=1.4.6&key=5d4e2bfebc376849b0fd27a963934135&plugin=AMap.CitySearch'
    )
    return getLocation()
})

export const getCityInfo = once(async function() {
    const systemInfo = await getSystemInfo()
    const cityCode = systemInfo._userCity || systemInfo._cityCode
    const cityName = systemInfo._cityName
    return {
        cityCode,
        cityName,
    }
})

export function toAwait(promise) {
    return promise
        .then(data => {
            return [null, data]
        })
        .catch(err => [err])
}

export function setEmbeddedHeight(height, androidAdded = 0) {
    if (isAndroid) {
        height += androidAdded
    }
    MCProtocol.Vip.show({
        h5whRate: 375 / height,
        h5ContentMaxWidth: 480,
    })
}

export function maskPhoneNum(phone) {
    let reg = /(\d{3})(\d{4})(\d{4})/
    if (reg.test(phone)) {
        return phone.replace(reg, '$1****$3')
    }
}

export function memoizeAsync(func) {
    const cache = {}
    return async (params, config = {}) => {
        const authToken = await getAuthToken()
        params = cloneDeep(params || {})
        const cacheKey = JSON.stringify(Object.assign({authToken}, params))

        if (cache[cacheKey] === undefined || config.refreshCache) {
            cache[cacheKey] = func(params, config)
        }

        return cache[cacheKey]
    }
}

export function updateSessionId(id) {
    URLParams.id = id
}