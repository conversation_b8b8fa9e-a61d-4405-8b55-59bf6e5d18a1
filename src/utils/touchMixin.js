import {mapState} from 'vuex'
import {MCProtocol} from '@simplex/simple-base'
import {convertPixelFromUI, isMucang} from './tools'
export default {
    data() {
        return {
            maxHeight: 0,
            minHeight: 0,
            height: 0,
            midHeight: 0,
            isTouching: false,
            isMoveing: false,
            startY: 0,
            lastY: 0,
            currentY: 0,
            startMoveTime: 0,
            endMoveTime: 0,
            sensitivity: 300,
            zeroSpeed: 0.001,
            inertiaFrame: null,
            upFrame: null,
            downFrame: null,
            timer: null,
        }
    },
    computed: {
        ...mapState(['isShowWebHeader']),
        isMoveup() {
            return this.currentY <= this.startY
        },
        isMoveDown() {
            return this.currentY >= this.startY
        },
        isTouchLock() {
            return this.height === this.maxHeight && this.show
        },
    },
    watch: {
        show(val) {
            if (val) {
                this.height = this.minHeight
            }
        },
    },
    created() {
        setTimeout(this.resizeCout, 300)
        // this.$EventBus.$on(['orientationChange', 'resize'], () => {
        //     setTimeout(this.resizeCout, 300)
        // })
    },
    methods: {
        setFull() {
            this.height = this.maxHeight
        },
        resizeCout() {
            if (this.isShowWebHeader && isMucang) {
                MCProtocol.Core.System.env(data => {
                    let {statusBarHeight} = data.data
                    let paddingTop = 0
                    if (statusBarHeight) {
                        paddingTop = statusBarHeight
                    }
                    this.setContainerHeight(paddingTop)
                })
            } else {
                this.setContainerHeight()
            }
        },
        setContainerHeight(safeArea = 0) {
            let maxHeight = window.innerHeight - safeArea
            let minHeight = maxHeight - convertPixelFromUI(420 + 88)
            this.maxHeight = maxHeight
            this.minHeight = minHeight
            this.height = minHeight
            this.midHeight = (maxHeight - minHeight) / 2 + minHeight
        },
        touchstart(e) {
            cancelAnimationFrame(this.inertiaFrame)
            if (this.isTouchLock || this.isMoveing || this.isNoExpand) return
            this.lastY = e.changedTouches[0].pageY
        },
        touchmove(e) {
            if (this.isTouchLock || this.isMoveing || this.isNoExpand) return
            e.preventDefault()
            this.isTouching = true
            this.startMoveTime = this.endMoveTime
            this.startY = this.lastY
            this.currentY = e.changedTouches[0].pageY
            let moveY = this.currentY - this.lastY
            this.setheight(moveY)
            this.lastY = this.currentY
            this.endMoveTime = e.timeStamp // 每次触发touchmove事件的时间戳;
        },
        touchend(e) {
            if (this.isTouchLock || this.isMoveing || this.isNoExpand) return
            this.isTouching = false

            let silenceTime = e.timeStamp - this.endMoveTime
            let timeStamp = this.endMoveTime - this.startMoveTime
            timeStamp = timeStamp > 0 ? timeStamp : 8
            if (silenceTime > 100) {
                if (this.isMoveup) {
                    if (this.height > this.midHeight) {
                        this.topFrame = requestAnimationFrame(this.moveUp.bind(this))
                    } else {
                        this.downFrame = requestAnimationFrame(this.moveDown.bind(this))
                    }
                } else if (this.isMoveDown) {
                    this.downFrame = requestAnimationFrame(this.moveDown.bind(this))
                }
            } else {
                let speed = (this.lastY - this.startY) / timeStamp
                let acceleration = speed / this.sensitivity
                let frameStartTime = Date.now()
                this.inertiaFrame = requestAnimationFrame(
                    this.moveByInertia.bind(this, speed, frameStartTime, acceleration)
                )
            }
        },
        setheight(moveY) {
            let height = Math.min(Math.max(this.minHeight, this.height - moveY), this.maxHeight)
            this.height = height
        },
        moveByInertia(speed, frameStartTime, acceleration) {
            let frameEndTime = Date.now()
            let frameTime = frameEndTime - frameStartTime
            if (speed < 0) {
                speed = Math.min(speed - acceleration * frameTime, 0)
            } else if (speed > 0) {
                speed = Math.max(speed - acceleration * frameTime, 0)
            }
            if (Math.abs(speed) <= this.zeroSpeed) {
                if (this.isMoveup) {
                    if (this.height > this.midHeight) {
                        this.topFrame = requestAnimationFrame(this.moveUp.bind(this))
                    } else {
                        this.downFrame = requestAnimationFrame(this.moveDown.bind(this))
                    }
                } else if (this.isMoveDown) {
                    this.downFrame = requestAnimationFrame(this.moveDown.bind(this))
                }
                return
            }
            let moveY = (speed * frameTime) / 2
            this.setheight(moveY)

            frameStartTime = frameEndTime
            this.inertiaFrame = requestAnimationFrame(
                this.moveByInertia.bind(this, speed, frameStartTime, acceleration)
            )
        },
        moveUp() {
            this.isMoveing = true
            this.setheight(-3)
            if (this.height < this.maxHeight) {
                this.topFrame = requestAnimationFrame(this.moveUp.bind(this))
            } else {
                this.isMoveing = false
            }
        },
        moveDown() {
            this.isMoveing = true
            this.setheight(3)
            if (this.height > this.minHeight) {
                this.downFrame = requestAnimationFrame(this.moveDown.bind(this))
            } else {
                this.isMoveing = false
            }
        },
    },
}
