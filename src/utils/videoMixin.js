import {mapState} from 'vuex'
import {debounce} from 'lodash-es'
import {URLParams} from './tools'
import {
    getLiveRecord,
    getLiveRoomRecord,
} from '../server/topLesson'

export default {
    inject: ['anchorIdentity', 'liveRoomMode'],
    data() {
        return {
            playbackIndex: -1,
            playbackSources: [],
            showPor: false,
            showPorDone: false,
            pullStreamUrls: [],
            clientWidth: 0,
            clientHeight: 0,
        }
    },
    computed: {
        ...mapState(['roomDetail', 'roomStatus']),
        videoContainMode() {
            if (this.clientWidth && this.clientHeight) {
                let longSide = this.clientWidth
                let shortSide = this.clientHeight
                if (shortSide > longSide) {
                    [longSide, shortSide] = [shortSide, longSide]
                }
                return (longSide / shortSide) < 1.5
            } else {
                return false
            }
        },
    },
    watch: {
        'roomStatus.status': {
            handler() {
                this.initPlayer()
            },
            immediate: true,
        },
        'roomStatus.playbackDone': {
            handler() {
                this.initPlayer()
            },
        },
    },
    mounted() {
        this.$EventBus.$on(['orientationChange', 'resize'], () => {
            this.setClientSize()
        })
        this.setClientSize()
    },
    methods: {
        setClientSize() {
            const {clientWidth, clientHeight} = document.documentElement
            this.clientWidth = clientWidth
            this.clientHeight = clientHeight
        },
        getWarmup() {
            if (this.roomStatus.status === 2 || (this.roomStatus.status === 3 && !this.roomStatus.playbackDone)) {
                if (this.anchorIdentity === 'official') {
                    this.startWarmup()
                }
            }
        },
        async getPlayback() {
            let res
            if (this.liveRoomMode) {
                res = await getLiveRoomRecord({
                    sessionId: this.roomDetail.sessionId,
                })
            } else {
                res = await getLiveRecord({
                    id: URLParams.id,
                })
            }
            this.playbackSources = res.itemList.map(item => {
                return item.recordInfoList[0].url
            })
            this.startPlayback()
        },
        async getLiveUrl() {
            // if (res.pullStreamUrls && res.pullStreamUrls.length) {
            //     res.pullStreamUrls = res.pullStreamUrls.reverse()
            // }
            if (this.roomDetail.streamOrigin === 4 && this.roomDetail.pullStreamUrls.some(item => item.definition === 'WEBSD')) {
                this.pullStreamUrls = this.roomDetail.pullStreamUrls.filter(item => item.definition === 'WEBSD')
            } else {
                this.pullStreamUrls = this.roomDetail.pullStreamUrls
            }
            this.startLive()
        },

        initPlayer: debounce(function() {
            let isRealLive = false
            if (this.roomStatus.status === 1) {
                this.playbackIndex = -1
                this.getLiveUrl()
                this.viPor()
                if (this.roomDetail.liveMode !== 1) {
                    isRealLive = true
                }
            } else if (this.roomStatus.status === 3 && this.roomStatus.playbackDone) {
                this.playbackIndex = 0
                this.getPlayback()
                this.viPor()
            } else {
                this.playbackIndex = -1
                this.getWarmup()
            }
            this.$store.commit('updateBizConfig', {
                isRealLive,
            })
        }, 50),

        viPor() {
            if (this.roomDetail.porNo && !this.showPorDone) {
                this.showPorDone = true
                this.showPor = true
                setTimeout(() => {
                    this.showPor = false
                }, 16000)
            }
        },
    },
}
