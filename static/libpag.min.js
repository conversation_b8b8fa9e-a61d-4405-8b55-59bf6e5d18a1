!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).libpag={})}(this,(function(e){"use strict";function t(e){const t=["length","name","prototype","wasmAsyncMethods"];let r=Object.getOwnPropertyNames(e).filter((e=>-1===t.indexOf(e)));e.wasmAsyncMethods&&e.wasmAsyncMethods.length>0&&(r=r.filter((t=>-1===e.wasmAsyncMethods.indexOf(t))));let n=Object.getOwnPropertyNames(e.prototype).filter((t=>"constructor"!==t&&"function"==typeof e.prototype[t]));e.prototype.wasmAsyncMethods&&e.prototype.wasmAsyncMethods.length>0&&(n=n.filter((t=>-1===e.prototype.wasmAsyncMethods.indexOf(t))));const a=(e,t)=>{const r=e[t];e[t]=function(...e){if(null!==rt.Asyncify.currData){const t=rt.Asyncify.currData;rt.Asyncify.currData=null;const n=r.call(this,...e);return rt.Asyncify.currData=t,n}return r.call(this,...e)}};r.forEach((t=>a(e,t))),n.forEach((t=>a(e.prototype,t)))}function r(e,t,r){e.wasmAsyncMethods||(e.wasmAsyncMethods=[]),e.wasmAsyncMethods.push(t)}function n(e){let t=Object.getOwnPropertyNames(e.prototype).filter((t=>"constructor"!==t&&"function"==typeof e.prototype[t]));const r=(t,r)=>{const n=t[r];t[r]=function(...t){if(!this.isDestroyed)return n.call(this,...t);console.error(`Don't call ${r} of the ${e.name} that is destroyed.`)}};t.forEach((t=>r(e.prototype,t)))}var a,i,o,s,l,u,c,f,d,h,m,p,v,y,w,g,_,b,E,C;(i=a||(a={}))[i.None=0]="None",i[i.Stretch=1]="Stretch",i[i.LetterBox=2]="LetterBox",i[i.Zoom=3]="Zoom",(s=o||(o={})).onAnimationStart="onAnimationStart",s.onAnimationEnd="onAnimationEnd",s.onAnimationCancel="onAnimationCancel",s.onAnimationRepeat="onAnimationRepeat",s.onAnimationUpdate="onAnimationUpdate",s.onAnimationPlay="onAnimationPlay",s.onAnimationPause="onAnimationPause",s.onAnimationFlushed="onAnimationFlushed",(u=l||(l={}))[u.LeftJustify=0]="LeftJustify",u[u.CenterJustify=1]="CenterJustify",u[u.RightJustify=2]="RightJustify",u[u.FullJustifyLastLineLeft=3]="FullJustifyLastLineLeft",u[u.FullJustifyLastLineRight=4]="FullJustifyLastLineRight",u[u.FullJustifyLastLineCenter=5]="FullJustifyLastLineCenter",u[u.FullJustifyLastLineFull=6]="FullJustifyLastLineFull",(f=c||(c={}))[f.Default=0]="Default",f[f.Horizontal=1]="Horizontal",f[f.Vertical=2]="Vertical",(h=d||(d={}))[h.Unknown=0]="Unknown",h[h.Null=1]="Null",h[h.Solid=2]="Solid",h[h.Text=3]="Text",h[h.Shape=4]="Shape",h[h.Image=5]="Image",h[h.PreCompose=6]="PreCompose",(p=m||(m={}))[p.None=0]="None",p[p.Scale=1]="Scale",p[p.Repeat=2]="Repeat",p[p.RepeatInverted=3]="RepeatInverted",(y=v||(v={}))[y.a=0]="a",y[y.c=1]="c",y[y.tx=2]="tx",y[y.b=3]="b",y[y.d=4]="d",y[y.ty=5]="ty",y[y.pers0=6]="pers0",y[y.pers1=7]="pers1",y[y.pers2=8]="pers2",(g=w||(w={}))[g.Success=0]="Success",g[g.TryAgainLater=-1]="TryAgainLater",g[g.Error=-2]="Error",(b=_||(_={}))[b.Unknown=0]="Unknown",b[b.ALPHA_8=1]="ALPHA_8",b[b.RGBA_8888=2]="RGBA_8888",b[b.BGRA_8888=3]="BGRA_8888",(C=E||(E={}))[C.Unknown=0]="Unknown",C[C.Opaque=1]="Opaque",C[C.Premultiplied=2]="Premultiplied",C[C.Unpremultiplied=3]="Unpremultiplied";var x=Object.freeze({__proto__:null,get PAGScaleMode(){return a},get PAGViewListenerEvent(){return o},get ParagraphJustification(){return l},get TextDirection(){return c},get LayerType(){return d},get PAGTimeStretchMode(){return m},get MatrixIndex(){return v},get DecoderResult(){return w},get ColorType(){return _},get AlphaType(){return E}}),P=Object.defineProperty,T=Object.getOwnPropertyDescriptor;let I=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static makeAll(e,t,r,n,a,i,o=0,s=0,l=1){const u=rt._Matrix._MakeAll(e,t,r,n,a,i,o,s,l);if(!u)throw new Error("Matrix.makeAll fail, please check parameters valid!");return new I(u)}static makeScale(e,t){let r;if(r=void 0!==t?rt._Matrix._MakeScale(e,t):rt._Matrix._MakeScale(e),!r)throw new Error("Matrix.makeScale fail, please check parameters valid!");return new I(r)}static makeTrans(e,t){const r=rt._Matrix._MakeTrans(e,t);if(!r)throw new Error("Matrix.makeTrans fail, please check parameters valid!");return new I(r)}get a(){return this.wasmIns?this.wasmIns._get(v.a):0}set a(e){var t;null==(t=this.wasmIns)||t._set(v.a,e)}get b(){return this.wasmIns?this.wasmIns._get(v.b):0}set b(e){var t;null==(t=this.wasmIns)||t._set(v.b,e)}get c(){return this.wasmIns?this.wasmIns._get(v.c):0}set c(e){var t;null==(t=this.wasmIns)||t._set(v.c,e)}get d(){return this.wasmIns?this.wasmIns._get(v.d):0}set d(e){var t;null==(t=this.wasmIns)||t._set(v.d,e)}get tx(){return this.wasmIns?this.wasmIns._get(v.tx):0}set tx(e){var t;null==(t=this.wasmIns)||t._set(v.tx,e)}get ty(){return this.wasmIns?this.wasmIns._get(v.ty):0}set ty(e){var t;null==(t=this.wasmIns)||t._set(v.ty,e)}get(e){return this.wasmIns?this.wasmIns._get(e):0}set(e,t){var r;null==(r=this.wasmIns)||r._set(e,t)}setAll(e,t,r,n,a,i,o=0,s=0,l=1){var u;null==(u=this.wasmIns)||u._setAll(e,t,r,n,a,i,o,s,l)}setAffine(e,t,r,n,a,i){var o;null==(o=this.wasmIns)||o._setAffine(e,t,r,n,a,i)}reset(){var e;null==(e=this.wasmIns)||e._reset()}setTranslate(e,t){var r;null==(r=this.wasmIns)||r._setTranslate(e,t)}setScale(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._setScale(e,t,r,n)}setRotate(e,t=0,r=0){var n;null==(n=this.wasmIns)||n._setRotate(e,t,r)}setSinCos(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._setSinCos(e,t,r,n)}setSkew(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._setSkew(e,t,r,n)}setConcat(e,t){var r;null==(r=this.wasmIns)||r._setConcat(e.wasmIns,t.wasmIns)}preTranslate(e,t){var r;null==(r=this.wasmIns)||r._preTranslate(e,t)}preScale(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._preScale(e,t,r,n)}preRotate(e,t=0,r=0){var n;null==(n=this.wasmIns)||n._preRotate(e,t,r)}preSkew(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._preSkew(e,t,r,n)}preConcat(e){var t;null==(t=this.wasmIns)||t._preConcat(e.wasmIns)}postTranslate(e,t){var r;null==(r=this.wasmIns)||r._postTranslate(e,t)}postScale(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._postScale(e,t,r,n)}postRotate(e,t=0,r=0){var n;null==(n=this.wasmIns)||n._postRotate(e,t,r)}postSkew(e,t,r=0,n=0){var a;null==(a=this.wasmIns)||a._postSkew(e,t,r,n)}postConcat(e){var t;null==(t=this.wasmIns)||t._postConcat(e.wasmIns)}destroy(){this.wasmIns.delete()}};I=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?T(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&P(t,r,i),i})([n,t],I);const A=(e,t,...r)=>{if(null!==rt.Asyncify.currData){const n=rt.Asyncify.currData;rt.Asyncify.currData=null;const a=e.call(t,...r);return rt.Asyncify.currData=n,a}return e.call(t,...r)},k=(e,t)=>new Proxy(e,{get(e,r,n){switch(r){case"get":return r=>{const n=A(e.get,e,r);return t(n)};case"push_back":return t=>{A(e.push_back,e,t.wasmIns||t)};case"size":return()=>A(e.size,e);default:return Reflect.get(e,r,n)}}}),S=e=>window.OffscreenCanvas&&e instanceof OffscreenCanvas,L=e=>{switch(e._layerType()){case d.Solid:return new rt.PAGSolidLayer(e);case d.Text:return new rt.PAGTextLayer(e);case d.Image:return new rt.PAGImageLayer(e);default:return new rt.PAGLayer(e)}},D=e=>(null==e?void 0:e.wasmIns)?e.wasmIns:e;var F=Object.defineProperty,M=Object.getOwnPropertyDescriptor;let B=class{constructor(e){this.wasmIns=e}uniqueID(){return this.wasmIns._uniqueID()}layerType(){return this.wasmIns._layerType()}layerName(){return this.wasmIns._layerName()}matrix(){const e=this.wasmIns._matrix();if(!e)throw new Error("Get matrix fail!");return new I(e)}setMatrix(e){this.wasmIns._setMatrix(e.wasmIns)}resetMatrix(){this.wasmIns._resetMatrix()}getTotalMatrix(){if(!this.wasmIns._getTotalMatrix())throw new Error("Get total matrix fail!");return new I(this.wasmIns._getTotalMatrix())}alpha(){return this.wasmIns._alpha()}setAlpha(e){this.wasmIns._setAlpha(e)}visible(){return this.wasmIns._visible()}setVisible(e){this.wasmIns._setVisible(e)}editableIndex(){return this.wasmIns._editableIndex()}parent(){const e=this.wasmIns._parent();if(!e)throw new Error("Get total matrix fail!");return new O(e)}markers(){const e=this.wasmIns._markers();if(!e)throw new Error("Get markers fail!");return k(e,(e=>e))}localTimeToGlobal(e){return this.wasmIns._localTimeToGlobal(e)}globalToLocalTime(e){return this.wasmIns._globalToLocalTime(e)}duration(){return this.wasmIns._duration()}frameRate(){return this.wasmIns._frameRate()}startTime(){return this.wasmIns._startTime()}setStartTime(e){this.wasmIns._setStartTime(e)}currentTime(){return this.wasmIns._currentTime()}setCurrentTime(e){this.wasmIns._setCurrentTime(e)}getProgress(){return this.wasmIns._getProgress()}setProgress(e){this.wasmIns._setProgress(e)}preFrame(){this.wasmIns._preFrame()}nextFrame(){this.wasmIns._nextFrame()}getBounds(){return this.wasmIns._getBounds()}trackMatteLayer(){const e=this.wasmIns._trackMatteLayer();if(!e)throw new Error("Get track matte layer fail!");return L(e)}excludedFromTimeline(){return this.wasmIns._excludedFromTimeline()}setExcludedFromTimeline(e){this.wasmIns._setExcludedFromTimeline(e)}isPAGFile(){return this.wasmIns._isPAGFile()}asTypeLayer(){return L(this)}isDelete(){return this.wasmIns.isDelete()}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};B=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?M(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&F(t,r,i),i})([n,t],B);var R=Object.defineProperty,G=Object.getOwnPropertyDescriptor;let O=class extends B{static make(e,t){const r=rt._PAGComposition._Make(e,t);if(!r)throw new Error("Make PAGComposition fail!");return new O(r)}width(){return this.wasmIns._width()}height(){return this.wasmIns._height()}setContentSize(e,t){this.wasmIns._setContentSize(e,t)}numChildren(){return this.wasmIns._numChildren()}getLayerAt(e){const t=this.wasmIns._getLayerAt(e);if(!t)throw new Error(`Get layer at ${e} fail!`);return L(t)}getLayerIndex(e){return this.wasmIns._getLayerIndex(e.wasmIns)}setLayerIndex(e,t){return this.wasmIns._setLayerIndex(e.wasmIns,t)}addLayer(e){return this.wasmIns._addLayer(e.wasmIns)}addLayerAt(e,t){return this.wasmIns._addLayerAt(e.wasmIns,t)}contains(e){return this.wasmIns._contains(e.wasmIns)}removeLayer(e){const t=this.wasmIns._removeLayer(e.wasmIns);if(!t)throw new Error("Remove layer fail!");return L(t)}removeLayerAt(e){const t=this.wasmIns._removeLayerAt(e);if(!t)throw new Error(`Remove layer at ${e} fail!`);return L(t)}removeAllLayers(){this.wasmIns._removeAllLayers()}swapLayer(e,t){this.wasmIns._swapLayer(e.wasmIns,t.wasmIns)}swapLayerAt(e,t){this.wasmIns._swapLayerAt(e,t)}audioBytes(){return this.wasmIns._audioBytes()}audioMarkers(){const e=this.wasmIns._audioMarkers();if(!e)throw new Error("Get audioMarkers fail!");return k(e,(e=>e))}audioStartTime(){return this.wasmIns._audioStartTime()}getLayersByName(e){const t=this.wasmIns._getLayersByName(e);if(!t)throw new Error(`Get layers by ${e} fail!`);return k(t,L)}getLayersUnderPoint(e,t){const r=this.wasmIns._getLayersUnderPoint(e,t);if(!r)throw new Error(`Get layers under point ${e},${t} fail!`);return k(r,L)}};O=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?G(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&R(t,r,i),i})([n,t],O);const j=e=>new Promise((t=>{const r=new FileReader;r.onload=()=>{t(r.result)},r.onerror=()=>{console.error(r.error.message)},r.readAsArrayBuffer(e)}));var $=Object.defineProperty,N=Object.getOwnPropertyDescriptor,U=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?N(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&$(t,r,i),i};let H=class extends O{static async load(e){let t=null;if(e instanceof File?t=await j(e):e instanceof Blob?t=await j(new File([e],"")):e instanceof ArrayBuffer&&(t=e),!t)throw new Error("Initialize PAGFile data type error, please put check data type must to be File ｜ Blob | ArrayBuffer!");return H.loadFromBuffer(t)}static loadFromBuffer(e){if(!(e&&e.byteLength>0))throw new Error("Initialize PAGFile data not be empty!");const t=new Uint8Array(e),r=t.byteLength,n=rt._malloc(r),a=new Uint8Array(rt.HEAPU8.buffer,n,r);a.set(t);const i=rt._PAGFile._Load(a.byteOffset,a.length);if(!i)throw new Error("Load PAGFile fail!");const o=new H(i);return rt._free(n),o}static maxSupportedTagLevel(){return rt._PAGFile._MaxSupportedTagLevel()}tagLevel(){return this.wasmIns._tagLevel()}numTexts(){return this.wasmIns._numTexts()}numImages(){return this.wasmIns._numImages()}numVideos(){return this.wasmIns._numVideos()}getTextData(e){return this.wasmIns._getTextData(e)}replaceText(e,t){this.wasmIns._replaceText(e,t)}replaceImage(e,t){this.wasmIns._replaceImage(e,t.wasmIns)}getLayersByEditableIndex(e,t){const r=this.wasmIns._getLayersByEditableIndex(e,t);if(!r)throw new Error(`Get ${(e=>{switch(e){case d.Solid:return"Solid";case d.Text:return"Text";case d.Shape:return"Shape";case d.Image:return"Image";case d.PreCompose:return"PreCompose";default:return"Unknown"}})(t)} layers by ${e} fail!`);return k(r,L)}getEditableIndices(e){return this.wasmIns._getEditableIndices(e)}timeStretchMode(){return this.wasmIns._timeStretchMode()}setTimeStretchMode(e){this.wasmIns._setTimeStretchMode(e)}setDuration(e){this.wasmIns._setDuration(e)}copyOriginal(){const e=this.wasmIns._copyOriginal();if(!e)throw new Error("Copy original fail!");return new H(e)}};U([r],H,"load",1),H=U([n,t],H);var V=Object.defineProperty,W=Object.getOwnPropertyDescriptor;let z=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static fromCanvas(e){const t=rt._PAGSurface._FromCanvas(e);if(!t)throw new Error(`Make PAGSurface from canvas ${e} fail!`);return new z(t)}static fromTexture(e,t,r,n){const a=rt._PAGSurface._FromTexture(e,t,r,n);if(!a)throw new Error(`Make PAGSurface from texture ${e} fail!`);return new z(a)}static fromRenderTarget(e,t,r,n){const a=rt._PAGSurface._FromRenderTarget(e,t,r,n);if(!a)throw new Error(`Make PAGSurface from frameBuffer ${e} fail!`);return new z(a)}width(){return this.wasmIns._width()}height(){return this.wasmIns._height()}updateSize(){this.wasmIns._updateSize()}clearAll(){return this.wasmIns._clearAll()}freeCache(){this.wasmIns._freeCache()}readPixels(e,t){if(e===_.Unknown)return null;const r=this.width()*(e===_.ALPHA_8?1:4),n=r*this.height(),a=new Uint8Array(n),i=rt._malloc(a.byteLength),o=new Uint8Array(rt.HEAPU8.buffer,i,a.byteLength),s=this.wasmIns._readPixels(e,t,i,r);return a.set(o),rt._free(i),s?a:null}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};z=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?W(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&V(t,r,i),i})([n,t],z);var q=Object.defineProperty,X=Object.getOwnPropertyDescriptor,J=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?X(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&q(t,r,i),i};let K=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static create(){const e=new rt._PAGPlayer;if(!e)throw new Error("Create PAGPlayer fail!");return new K(e)}setProgress(e){this.wasmIns._setProgress(e)}async flush(){return await rt.webAssemblyQueue.exec(this.wasmIns._flush,this.wasmIns)}async flushInternal(e){return await rt.webAssemblyQueue.exec((async()=>{const t=await this.wasmIns._flush();return e(t),t}),this.wasmIns)}duration(){return this.wasmIns._duration()}getProgress(){return this.wasmIns._getProgress()}videoEnabled(){return this.wasmIns._videoEnabled()}setVideoEnabled(e){this.wasmIns._setVideoEnabled(e)}cacheEnabled(){return this.wasmIns._cacheEnabled()}setCacheEnabled(e){this.wasmIns._setCacheEnabled(e)}cacheScale(){return this.wasmIns._cacheScale()}setCacheScale(e){this.wasmIns._setCacheScale(e)}maxFrameRate(){return this.wasmIns._maxFrameRate()}setMaxFrameRate(e){this.wasmIns._setMaxFrameRate(e)}scaleMode(){return this.wasmIns._scaleMode()}setScaleMode(e){this.wasmIns._setScaleMode(e)}setSurface(e){this.wasmIns._setSurface(D(e))}getComposition(){const e=this.wasmIns._getComposition();if(!e)throw new Error("Get composition fail!");return e._isPAGFile()?new H(e):new O(e)}setComposition(e){this.wasmIns._setComposition(D(e))}getSurface(){const e=this.wasmIns._getSurface();if(!e)throw new Error("Get surface fail!");return new z(e)}matrix(){const e=this.wasmIns._matrix();if(!e)throw new Error("Get matrix fail!");return new I(e)}setMatrix(e){this.wasmIns._setMatrix(e.wasmIns)}nextFrame(){this.wasmIns._nextFrame()}preFrame(){this.wasmIns._preFrame()}autoClear(){return this.wasmIns._autoClear()}setAutoClear(e){this.wasmIns._setAutoClear(e)}getBounds(e){return this.wasmIns._getBounds(e.wasmIns)}getLayersUnderPoint(e,t){const r=this.wasmIns._getLayersUnderPoint(e,t);if(!r)throw new Error(`Get layers under point, x: ${e} y:${t} fail!`);return k(r,L)}hitTestPoint(e,t,r,n=!1){return this.wasmIns._hitTestPoint(e.wasmIns,t,r,n)}renderingTime(){return this.wasmIns._renderingTime()}imageDecodingTime(){return this.wasmIns._imageDecodingTime()}presentingTime(){return this.wasmIns._presentingTime()}graphicsMemory(){return this.wasmIns._graphicsMemory()}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};J([r],K.prototype,"flush",1),J([r],K.prototype,"flushInternal",1),K=J([n,t],K);class Q{static async createFromBytes(e){const t=new Blob([e],{type:"image/*"});return new Promise((e=>{const r=new Image;r.onload=function(){e(new Q(r))},r.onerror=function(){console.error("image create from bytes error."),e(null)},r.src=URL.createObjectURL(t)}))}static async createFromPath(e){return new Promise((t=>{const r=new Image;r.onload=function(){t(new Q(r))},r.onerror=function(){console.error(`image create from path error: ${e}`),t(null)},r.src=e}))}constructor(e){this.source=e}width(){return this.source instanceof HTMLVideoElement?this.source.videoWidth:this.source.width}height(){return this.source instanceof HTMLVideoElement?this.source.videoHeight:this.source.height}upload(e){var t;const r=null==(t=e.currentContext)?void 0:t.GLctx;r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,this.source)}}var Y=Object.defineProperty,Z=Object.getOwnPropertyDescriptor,ee=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?Z(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&Y(t,r,i),i};let te=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e}static async fromFile(e){return new Promise(((t,r)=>{const n=new Image;n.onload=async()=>{t(te.fromSource(n))},n.onerror=e=>{r(e)},n.src=URL.createObjectURL(e)}))}static fromSource(e){const t=new Q(e),r=rt._PAGImage._FromNativeImage(t);if(!r)throw new Error("Make PAGImage from source fail!");return new te(r)}static fromPixels(e,t,r,n,a){const i=t*(n===_.ALPHA_8?1:4),o=rt._malloc(e.byteLength);new Uint8Array(rt.HEAPU8.buffer,o,e.byteLength).set(e);const s=rt._PAGImage._FromPixels(o,t,r,i,n,a);if(!s)throw new Error("Make PAGImage from pixels fail!");return new te(s)}static fromTexture(e,t,r,n){const a=rt._PAGImage._FromTexture(e,t,r,n);if(!a)throw new Error("Make PAGImage from texture fail!");return new te(a)}width(){return this.wasmIns._width()}height(){return this.wasmIns._height()}scaleMode(){return this.wasmIns._scaleMode()}setScaleMode(e){this.wasmIns._setScaleMode(e)}matrix(){const e=this.wasmIns._matrix();if(!e)throw new Error("Get matrix fail!");return new I(e)}setMatrix(e){this.wasmIns._setMatrix(e.wasmIns)}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};ee([r],te,"fromFile",1),te=ee([n,t],te);class re{constructor(){this.listenersMap={}}on(e,t){void 0===this.listenersMap[e]&&(this.listenersMap[e]=[]),this.listenersMap[e].push(t)}off(e,t){const r=this.listenersMap[e];if(void 0===r)return;if(void 0===t)return void delete this.listenersMap[e];const n=r.findIndex((e=>e===t));r.splice(n,1)}emit(e,...t){const r=this.listenersMap[e];if(void 0===r||r.length<1)return!1;for(const e of r)e(...t);return!0}}const ne=2560,ae={depth:!1,stencil:!1,antialias:!1};var ie=Object.defineProperty,oe=Object.getOwnPropertySymbols,se=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable,ue=(e,t,r)=>t in e?ie(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;class ce{constructor(e,t=!1){this.isDestroyed=!1,this.oldHandle=0,this.handle=e,this.adopted=t}static from(e){if(e instanceof WebGLRenderingContext||window.WebGL2RenderingContext&&e instanceof WebGL2RenderingContext){const t=e instanceof WebGLRenderingContext?1:2,{GL:r}=rt;let n=0;return r.contexts.length>0&&(n=r.contexts.findIndex((t=>(null==t?void 0:t.GLctx)===e))),n<1?(n=r.registerContext(e,((e,t)=>{for(var r in t||(t={}))se.call(t,r)&&ue(e,r,t[r]);if(oe)for(var r of oe(t))le.call(t,r)&&ue(e,r,t[r]);return e})({majorVersion:t,minorVersion:0},ae)),new ce(n)):new ce(n,!0)}if(e instanceof ce)return new ce(e.handle,!0);throw new Error("Parameter error!")}getContext(){return rt.GL.getContext(this.handle).GLctx}makeCurrent(){var e;return!this.isDestroyed&&(this.oldHandle=(null==(e=rt.GL.currentContext)?void 0:e.handle)||0,this.oldHandle===this.handle||rt.GL.makeContextCurrent(this.handle))}clearCurrent(){this.isDestroyed||this.oldHandle!==this.handle&&(rt.GL.makeContextCurrent(0),this.oldHandle&&rt.GL.makeContextCurrent(this.oldHandle))}registerTexture(e){return this.register(rt.GL.textures,e)}getTexture(e){return rt.GL.textures[e]}unregisterTexture(e){rt.GL.textures[e]=null}registerRenderTarget(e){return this.register(rt.GL.framebuffers,e)}getRenderTarget(e){return rt.GL.framebuffers[e]}unregisterRenderTarget(e){rt.GL.framebuffers[e]=null}destroy(){this.adopted||rt.GL.deleteContext(this.handle)}register(e,t){const r=rt.GL.getNewId(e);return e[r]=t,r}}var fe=Object.defineProperty,de=Object.getOwnPropertyDescriptor,he=Object.getOwnPropertySymbols,me=Object.prototype.hasOwnProperty,pe=Object.prototype.propertyIsEnumerable,ve=(e,t,r)=>t in e?fe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ye=(e,t)=>{for(var r in t||(t={}))me.call(t,r)&&ve(e,r,t[r]);if(he)for(var r of he(t))pe.call(t,r)&&ve(e,r,t[r]);return e};let we=class{constructor(e,t){this.repeatCount=0,this.isPlaying=!1,this.isDestroyed=!1,this.startTime=0,this.playTime=0,this.timer=null,this.pagSurface=null,this.repeatedTimes=0,this.eventManager=new re,this.pagGlContext=null,this.rawWidth=0,this.rawHeight=0,this.currentFrame=0,this.frameRate=0,this.pagViewOptions={useScale:!0,useCanvas2D:!1,firstFrame:!0},this.player=e,this.canvasElement=t}static async init(e,t,r={}){let n=null;if("string"==typeof t?n=document.getElementById(t.substr(1)):(t instanceof HTMLCanvasElement||S(t))&&(n=t),!n)throw new Error("Canvas is not found!");const a=rt.PAGPlayer.create(),i=new we(a,n);if(i.pagViewOptions=ye(ye({},i.pagViewOptions),r),i.pagViewOptions.useCanvas2D){if(rt.globalCanvas.retain(),!rt.globalCanvas.glContext)throw new Error("GlobalCanvas context is not WebGL!");i.pagGlContext=ce.from(rt.globalCanvas.glContext)}else{const e=n.getContext("webgl",ae);if(!e)throw new Error("Canvas context is not WebGL!");i.pagGlContext=ce.from(e)}return i.resetSize(i.pagViewOptions.useScale),i.frameRate=e.frameRate(),i.pagSurface=this.makePAGSurface(i.pagGlContext,i.rawWidth,i.rawHeight),i.player.setSurface(i.pagSurface),i.player.setComposition(e),i.setProgress(0),i.pagViewOptions.firstFrame&&await i.flush(),i}static makePAGSurface(e,t,r){if(!e.makeCurrent())throw new Error("Make context current fail!");const n=z.fromRenderTarget(0,t,r,!0);return e.clearCurrent(),n}duration(){return this.player.duration()}addListener(e,t){return this.eventManager.on(e,t)}removeListener(e,t){return this.eventManager.off(e,t)}async play(){this.isPlaying||(0===this.playTime&&this.eventManager.emit(o.onAnimationStart,this),this.eventManager.emit(o.onAnimationPlay,this),this.isPlaying=!0,this.startTime=1e3*performance.now()-this.playTime,await this.flushLoop())}pause(){this.isPlaying&&(this.clearTimer(),this.isPlaying=!1,this.eventManager.emit(o.onAnimationPause,this))}async stop(e=!0){this.clearTimer(),this.playTime=0,this.player.setProgress(0),await this.flush(),this.isPlaying=!1,e&&this.eventManager.emit(o.onAnimationCancel,this)}setRepeatCount(e){this.repeatCount=e<0?0:e-1}getProgress(){return this.player.getProgress()}setProgress(e){return this.playTime=e*this.duration(),this.startTime=1e3*performance.now()-this.playTime,this.isPlaying||this.player.setProgress(e),e}videoEnabled(){return this.player.videoEnabled()}setVideoEnabled(e){this.player.setVideoEnabled(e)}cacheEnabled(){return this.player.cacheEnabled()}setCacheEnabled(e){this.player.setCacheEnabled(e)}cacheScale(){return this.player.cacheScale()}setCacheScale(e){this.player.setCacheScale(e)}maxFrameRate(){return this.player.maxFrameRate()}setMaxFrameRate(e){this.player.setMaxFrameRate(e)}scaleMode(){return this.player.scaleMode()}setScaleMode(e){this.player.setScaleMode(e)}async flush(){const e=await this.player.flushInternal((e=>{var t,r;if(this.pagViewOptions.useCanvas2D&&e&&rt.globalCanvas.canvas){this.canvasContext||(this.canvasContext=null==(t=this.canvasElement)?void 0:t.getContext("2d"));const e=this.canvasContext.globalCompositeOperation;this.canvasContext.globalCompositeOperation="copy",null==(r=this.canvasContext)||r.drawImage(rt.globalCanvas.canvas,0,rt.globalCanvas.canvas.height-this.rawHeight,this.rawWidth,this.rawHeight,0,0,this.canvasContext.canvas.width,this.canvasContext.canvas.height),this.canvasContext.globalCompositeOperation=e}}));return e&&this.eventManager.emit(o.onAnimationFlushed,this),e}freeCache(){var e;null==(e=this.pagSurface)||e.freeCache()}getComposition(){return this.player.getComposition()}setComposition(e){this.player.setComposition(e)}matrix(){return this.player.matrix()}setMatrix(e){this.player.setMatrix(e)}getLayersUnderPoint(e,t){return this.player.getLayersUnderPoint(e,t)}updateSize(){var e;if(!this.canvasElement)throw new Error("Canvas element is not found!");if(this.rawWidth=this.canvasElement.width,this.rawHeight=this.canvasElement.height,!this.pagGlContext)return null;const t=we.makePAGSurface(this.pagGlContext,this.rawWidth,this.rawHeight);this.player.setSurface(t),null==(e=this.pagSurface)||e.destroy(),this.pagSurface=t}destroy(){var e,t;this.clearTimer(),this.player.destroy(),null==(e=this.pagSurface)||e.destroy(),this.pagViewOptions.useCanvas2D&&rt.globalCanvas.release(),null==(t=this.pagGlContext)||t.destroy(),this.pagGlContext=null,this.canvasContext=null,this.canvasElement=null,this.isDestroyed=!0}async flushLoop(){this.isPlaying&&(this.timer=window.requestAnimationFrame((async()=>{await this.flushLoop()})),await this.flushNextFrame())}async flushNextFrame(){const e=this.duration();this.playTime=1e3*performance.now()-this.startTime;const t=Math.floor(this.playTime/1e6*this.frameRate),r=Math.floor(this.playTime/e);if(this.repeatCount>=0&&r>this.repeatCount)return this.clearTimer(),this.playTime=0,this.isPlaying=!1,this.eventManager.emit(o.onAnimationEnd,this),void(this.repeatedTimes=0);this.repeatedTimes===r&&this.currentFrame===t||(this.repeatedTimes<r&&this.eventManager.emit(o.onAnimationRepeat,this),this.player.setProgress(this.playTime%e/e),await this.flush(),this.currentFrame=t,this.repeatedTimes=r,this.eventManager.emit(o.onAnimationUpdate,this))}clearTimer(){this.timer&&(window.cancelAnimationFrame(this.timer),this.timer=null)}resetSize(e=!0){if(!this.canvasElement)throw new Error("Canvas element is not found!");if(!e||S(this.canvasElement))return this.rawWidth=this.canvasElement.width,void(this.rawHeight=this.canvasElement.height);let t;const r=this.canvasElement,n=window.getComputedStyle(r,null),a={width:Number(n.width.replace("px","")),height:Number(n.height.replace("px",""))};if(a.width>0&&a.height>0)t=a;else{const e={width:Number(r.style.width.replace("px","")),height:Number(r.style.height.replace("px",""))};t=e.width>0&&e.height>0?e:{width:r.width,height:r.height}}r.style.width=`${t.width}px`,r.style.height=`${t.height}px`,this.rawWidth=t.width*window.devicePixelRatio,this.rawHeight=t.height*window.devicePixelRatio,r.width=this.rawWidth,r.height=this.rawHeight}};we=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?de(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&fe(t,r,i),i})([n],we);const ge=["emoji"].concat(...["Arial","Courier New","Georgia","Times New Roman","Trebuchet MS","Verdana"]);var _e=Object.defineProperty,be=Object.getOwnPropertyDescriptor,Ee=(e,t,r,n)=>{for(var a,i=n>1?void 0:n?be(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&_e(t,r,i),i};let Ce=class{constructor(e){this.isDestroyed=!1,this.wasmIns=e,this.fontFamily=this.wasmIns.fontFamily,this.fontStyle=this.wasmIns.fontStyle}static create(e,t){const r=rt._PAGFont._create(e,t);if(!r)throw new Error("Create PAGFont fail!");return new Ce(r)}static async registerFont(e,t){const r=await j(t);if(!(r&&r.byteLength>0))throw new Error("Initialize PAGFont data not be empty!");const n=new Uint8Array(r),a=new FontFace(e,n);document.fonts.add(a),await a.load()}static registerFallbackFontNames(e=[]){const t=new rt.VectorString,r=e.concat(ge);for(const e of r)t.push_back(e);rt._PAGFont._SetFallbackFontNames(t),t.delete()}destroy(){this.wasmIns.delete(),this.isDestroyed=!0}};Ee([r],Ce,"registerFont",1),Ce=Ee([n,t],Ce);let xe={};const Pe=(e,t,r,n=!1)=>{var a;t in xe||(xe[t]=[]),null==(a=xe[t])||a.push({node:e,handler:r,capture:n}),e.addEventListener(t,r,n)},Te=(e,t,r)=>{var n;t in xe&&(null==(n=xe[t])||n.filter((({node:t,handler:n})=>t===e&&n===r)).forEach((({node:e,handler:r,capture:n})=>e.removeEventListener(t,r,n))))},Ie=(e,t)=>{var r,n;t in xe&&(null==(r=xe[t])||r.filter((({node:t})=>t===e)).forEach((({node:e,handler:r,capture:n})=>e.removeEventListener(t,r,n))),xe[t]=null==(n=xe[t])?void 0:n.filter((({node:t})=>t!==e)))},Ae=navigator.userAgent,ke=/android|adr/i.test(Ae),Se=/(mobile)/i.test(Ae)&&ke;!/(mobile)/i.test(Ae)&&!Se&&/Mac OS X/i.test(Ae),/(iphone|ipad|ipod)/i.test(Ae);const Le=/MicroMessenger/i.test(Ae),De=async e=>{Le&&window.WeixinJSBridge&&await new Promise((e=>{window.WeixinJSBridge.invoke("getNetworkType",{},(()=>{e()}),(()=>{e()}))}));try{await e.play()}catch(e){throw console.error(e.message),new Error("Failed to decode video, please play PAG after user gesture. Or your can load a software decoder to decode the video.")}};class Fe{constructor(e,t,r){this.hadPlay=!1,this.videoEl=document.createElement("video"),this.videoEl.style.display="none",this.videoEl.muted=!0,this.videoEl.playsInline=!0,this.videoEl.load(),Pe(this.videoEl,"timeupdate",this.onTimeupdate.bind(this)),this.frameRate=t,this.lastFlush=-1;const n=new Blob([e],{type:"video/mp4"});this.videoEl.src=URL.createObjectURL(n),this.staticTimeRanges=new Me(r)}async prepare(e){if(!this.videoEl)return console.error("Video element is null!"),!1;const{currentTime:t}=this.videoEl,r=e/this.frameRate;return this.lastFlush=r,0===t&&0===r?(this.hadPlay||(await De(this.videoEl),await new Promise((e=>{window.requestAnimationFrame((()=>{this.videoEl?(this.videoEl.pause(),this.hadPlay=!0):console.error("Video Element is null!"),e()}))}))),!0):Math.round(r*this.frameRate)===Math.round(t*this.frameRate)||(this.staticTimeRanges.contains(e)?await this.seek(r,!1):Math.abs(t-r)<1/this.frameRate*3?(this.videoEl.paused&&await De(this.videoEl),!0):await this.seek(r))}renderToTexture(e,t){var r;if(!this.videoEl||this.videoEl.readyState<2)return;const n=null==(r=e.currentContext)?void 0:r.GLctx;n.bindTexture(n.TEXTURE_2D,e.textures[t]),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,this.videoEl)}onDestroy(){if(!this.videoEl)throw new Error("Video element is null!");Ie(this.videoEl,"playing"),Ie(this.videoEl,"timeupdate"),this.videoEl=null}onTimeupdate(){if(!this.videoEl||this.lastFlush<0)return;const{currentTime:e}=this.videoEl;e-this.lastFlush>=1/this.frameRate*3&&!this.videoEl.paused&&this.videoEl.pause()}seek(e,t=!0){return new Promise((r=>{let n=!1,a=null;const i=async()=>{if(!this.videoEl)return console.error("Video element is null!"),void r(!1);Te(this.videoEl,"seeked",i),t&&this.videoEl.paused?await De(this.videoEl):t||this.videoEl.paused||this.videoEl.pause(),n=!0,clearTimeout(a),a=null,r(!0)};if(!this.videoEl)return console.error("Video element is null!"),void r(!1);Pe(this.videoEl,"seeked",i),this.videoEl.currentTime=e,a=setTimeout((()=>{if(!n){if(!this.videoEl)return console.error("Video element is null!"),void r(!1);Te(this.videoEl,"seeked",i),r(!1)}}),1e3/this.frameRate*3)}))}}class Me{constructor(e){this.timeRanges=e}contains(e){if(0===this.timeRanges.length)return!1;for(let t of this.timeRanges)if(t.start<=e&&e<t.end)return!0;return!1}}const Be=e=>{const t=new Int32Array(e.data.buffer);return{left:Re(t,e.width,e.height),top:Ge(t,e.width,e.height),right:Oe(t,e.width,e.height),bottom:je(t,e.width,e.height)}},Re=(e,t,r)=>{const n=e.length/t,a=e.length/r;for(let r=0;r<a;r++)for(let a=0;a<n;a++)if(0!==e[r+a*t])return r;return a},Ge=(e,t,r)=>{const n=e.length/t,a=e.length/r;for(let r=0;r<n;r++)for(let n=0;n<a;n++)if(0!==e[r*t+n])return r;return n},Oe=(e,t,r)=>{const n=e.length/t;for(let a=e.length/r-1;a>0;a--)for(let r=n-1;r>0;r--)if(0!==e[a+t*r])return a;return 0},je=(e,t,r)=>{const n=e.length/t,a=e.length/r;for(let r=n-1;r>0;r--)for(let n=a-1;n>0;n--)if(0!==e[r*t+n])return r;return 0},$e=(()=>{try{const e=new OffscreenCanvas(0,0),t=e.getContext("2d");return(null==t?void 0:t.measureText)?e:document.createElement("canvas")}catch(e){return document.createElement("canvas")}})();$e.width=10,$e.height=10;const Ne=document.createElement("canvas");Ne.width=1,Ne.height=1;const Ue=Ne.getContext("2d");Ue.textBaseline="top",Ue.font="100px -no-font-family-here-",Ue.scale(.01,.01),Ue.fillStyle="#000",Ue.globalCompositeOperation="copy";const He=class{constructor(e,t,r,n=!1,a=!1){this.fontBoundingBoxMap=[],this.fontName=e,this.fontStyle=t,this.size=r,this.fauxBold=n,this.fauxItalic=a}static isEmoji(e){Ue.fillText(e,0,0);return!Ue.getImageData(0,0,1,1).data.toString().includes("0,0,0,")}fontString(){const e=[];this.fauxItalic&&e.push("italic"),this.fauxBold&&e.push("bold"),e.push(`${this.size}px`);const t=ge.concat();return t.unshift(...((e,t="")=>{if(!e)return[];const r=e.split(" ");let n=[];1===r.length?n.push(e):(n.push(r.join("")),n.push(r.join(" ")));const a=n.reduce(((e,r)=>(t?(e.push(`"${r} ${t}"`),e.push(`"${r}-${t}"`)):e.push(`"${r}"`),e)),[]);return""!==t&&a.push(`"${e}"`),a})(this.fontName,this.fontStyle)),e.push(`${t.join(",")}`),e.join(" ")}getTextAdvance(e){const{context:t}=He;return t.font=this.fontString(),t.measureText(e).width}getTextBounds(e){const{context:t}=He;t.font=this.fontString();const r=this.measureText(t,e);return{left:Math.floor(-r.actualBoundingBoxLeft),top:Math.floor(-r.actualBoundingBoxAscent),right:Math.ceil(r.actualBoundingBoxRight),bottom:Math.ceil(r.actualBoundingBoxDescent)}}generateFontMetrics(){const{context:e}=He;e.font=this.fontString();const t=this.measureText(e,"中"),r=t.actualBoundingBoxAscent,n=this.measureText(e,"x").actualBoundingBoxAscent;return{ascent:-t.fontBoundingBoxAscent,descent:t.fontBoundingBoxDescent,xHeight:n,capHeight:r}}generateImage(e,t){const r=document.createElement("canvas");r.width=t.right-t.left,r.height=t.bottom-t.top;const n=r.getContext("2d");return n.font=this.fontString(),n.fillText(e,-t.left,-t.top),new Q(r)}measureText(e,t){const r=e.measureText(t);if(null==r?void 0:r.actualBoundingBoxAscent)return r;e.canvas.width=1.5*this.size,e.canvas.height=1.5*this.size;const n=[0,this.size];e.clearRect(0,0,e.canvas.width,e.canvas.height),e.font=this.fontString(),e.fillText(t,n[0],n[1]);const a=e.getImageData(0,0,e.canvas.width,e.canvas.height),{left:i,top:o,right:s,bottom:l}=Be(a);let u;e.clearRect(0,0,e.canvas.width,e.canvas.height);const c=this.fontBoundingBoxMap.find((e=>e.key===this.fontName));if(c)u=c.value;else{e.font=this.fontString(),e.fillText("测",n[0],n[1]);const t=e.getImageData(0,0,e.canvas.width,e.canvas.height);u=Be(t),this.fontBoundingBoxMap.push({key:this.fontName,value:u}),e.clearRect(0,0,e.canvas.width,e.canvas.height)}return{actualBoundingBoxAscent:n[1]-o,actualBoundingBoxRight:s-n[0],actualBoundingBoxDescent:l-n[1],actualBoundingBoxLeft:n[0]-i,fontBoundingBoxAscent:u.bottom-u.top,fontBoundingBoxDescent:0}}};let Ve=He;Ve.canvas=$e,Ve.context=$e.getContext("2d");class We{static getLineCap(e){switch(e){case rt.TGFXLineCap.Round:return"round";case rt.TGFXLineCap.Square:return"square";default:return"butt"}}static getLineJoin(e){switch(e){case rt.TGFXLineJoin.Round:return"round";case rt.TGFXLineJoin.Bevel:return"bevel";default:return"miter"}}constructor(e,t){this.canvas=document.createElement("canvas"),this.canvas.width=e,this.canvas.height=t}fillPath(e,t){const r=this.canvas.getContext("2d");r.setTransform(1,0,0,1,0,0),t===rt.TGFXPathFillType.InverseWinding||t===rt.TGFXPathFillType.InverseEvenOdd?(r.clip(e,t===rt.TGFXPathFillType.InverseEvenOdd?"evenodd":"nonzero"),r.fillRect(0,0,this.canvas.width,this.canvas.height)):r.fill(e,t===rt.TGFXPathFillType.EvenOdd?"evenodd":"nonzero")}fillText(e,t,r,n){const a=new Ve(e.name,e.style,e.size,e.bold,e.italic),i=this.canvas.getContext("2d"),o=new I(n);i.setTransform(o.a,o.b,o.c,o.d,o.tx,o.ty),i.font=a.fontString();for(let e=0;e<t.size();e++){const n=r.get(e);i.fillText(t.get(e),n.x,n.y)}}strokeText(e,t,r,n,a){if(t.width<.5)return;const i=new Ve(e.name,e.style,e.size,e.bold,e.italic),o=this.canvas.getContext("2d"),s=new I(a);o.setTransform(s.a,s.b,s.c,s.d,s.tx,s.ty),o.font=i.fontString(),o.lineJoin=We.getLineJoin(t.join),o.miterLimit=t.miterLimit,o.lineCap=We.getLineCap(t.cap),o.lineWidth=t.width;for(let e=0;e<r.size();e++){const t=n.get(e);o.strokeText(r.get(e),t.x,t.y)}}clear(){this.canvas.getContext("2d").clearRect(0,0,this.canvas.width,this.canvas.height)}update(e){var t;const r=null==(t=e.currentContext)?void 0:t.GLctx;r.texImage2D(r.TEXTURE_2D,0,r.ALPHA,r.ALPHA,r.UNSIGNED_BYTE,this.canvas)}}class ze{constructor(){this._canvas=null,this._glContext=null,this.width=ne,this.height=ne,this.retainCount=0}retain(){if(0===this.retainCount){try{this._canvas=new OffscreenCanvas(0,0)}catch(e){this._canvas=document.createElement("canvas")}this._canvas.width=this.width,this._canvas.height=this.height;const e=this._canvas.getContext("webgl",ae);this._glContext=ce.from(e)}this.retainCount+=1}release(){if(this.retainCount-=1,0===this.retainCount){if(!this._glContext)return;this._glContext.destroy(),this._glContext=null,this._canvas=null}}get canvas(){return this._canvas}get glContext(){return this._glContext}setCanvasSize(e=2560,t=2560){this.width=e,this.height=t,this._glContext&&this._canvas&&(this._canvas.width=e,this._canvas.height=t)}}var qe=Object.defineProperty,Xe=Object.getOwnPropertyDescriptor;let Je=class extends B{static make(e,t,r=0,n="",a=""){return new Je("string"==typeof t?rt._PAGTextLayer._Make(e,t,r,n,a):rt._PAGTextLayer._Make(e,t))}fillColor(){return this.wasmIns._fillColor()}setFillColor(e){this.wasmIns._setFillColor(e)}font(){return new Ce(this.wasmIns._font())}setFont(e){this.wasmIns._setFont(e.wasmIns)}fontSize(){return this.wasmIns._fontSize()}setFontSize(e){this.wasmIns._setFontSize(e)}strokeColor(){return this.wasmIns._strokeColor()}setStrokeColor(e){this.wasmIns._setStrokeColor(e)}text(){return this.wasmIns._text()}setText(e){this.wasmIns._setText(e)}reset(){this.wasmIns._reset()}};Je=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?Xe(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&qe(t,r,i),i})([n,t],Je);var Ke=Object.defineProperty,Qe=Object.getOwnPropertyDescriptor;let Ye=class extends B{static make(e,t,r){const n=rt._PAGImageLayer._Make(e,t,r);if(!n)throw new Error("Make PAGImageLayer fail!");return new Ye(n)}contentDuration(){return this.wasmIns._contentDuration()}getVideoRanges(){const e=this.wasmIns._getVideoRanges();if(!e)throw new Error("Get video ranges fail!");return k(e,(e=>e))}replaceImage(e){this.wasmIns._replaceImage(e.wasmIns)}setImage(e){this.wasmIns._setImage(e.wasmIns)}layerTimeToContent(e){return this.wasmIns._layerTimeToContent(e)}contentTimeToLayer(e){return this.wasmIns._contentTimeToLayer(e)}imageBytes(){return this.wasmIns._imageBytes()}};Ye=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?Qe(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&Ke(t,r,i),i})([n,t],Ye);var Ze=Object.defineProperty,et=Object.getOwnPropertyDescriptor;let tt=class extends B{static make(e,t,r,n,a){const i=rt._PAGSolidLayer._Make(e,t,r,n,a);if(!i)throw new Error("Make PAGSolidLayer fail!");return new tt(i)}solidColor(){return this.wasmIns._solidColor()}setSolidColor(e){this.wasmIns._setSolidColor(e)}};tt=((e,t,r,n)=>{for(var a,i=n>1?void 0:n?et(t,r):t,o=e.length-1;o>=0;o--)(a=e[o])&&(i=(n?a(t,r,i):a(i))||i);return n&&i&&Ze(t,r,i),i})([n,t],tt);let rt;var nt,at=(nt="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(e){var t,r,n=void 0!==(e=e||{})?e:{},a=Object.assign;n.ready=new Promise((function(e,n){t=e,r=n}));var i,o,s=a({},n),l=(e,t)=>{throw t},u="";"undefined"!=typeof document&&document.currentScript&&(u=document.currentScript.src),nt&&(u=nt),u=0!==u.indexOf("blob:")?u.substr(0,u.replace(/[?#].*/,"").lastIndexOf("/")+1):"",i=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},o=(e,t,r)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)};var c,f,d=n.print||console.log.bind(console),h=n.printErr||console.warn.bind(console);a(n,s),s=null,n.arguments&&n.arguments,n.thisProgram&&n.thisProgram,n.quit&&(l=n.quit),n.wasmBinary&&(c=n.wasmBinary),n.noExitRuntime,"object"!=typeof WebAssembly&&Q("no native wasm support detected");var m,p=!1;function v(e,t){e||Q(t)}var y="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function w(e,t,r){for(var n=t+r,a=t;e[a]&&!(a>=n);)++a;if(a-t>16&&e.subarray&&y)return y.decode(e.subarray(t,a));for(var i="";t<a;){var o=e[t++];if(128&o){var s=63&e[t++];if(192!=(224&o)){var l=63&e[t++];if((o=224==(240&o)?(15&o)<<12|s<<6|l:(7&o)<<18|s<<12|l<<6|63&e[t++])<65536)i+=String.fromCharCode(o);else{var u=o-65536;i+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else i+=String.fromCharCode((31&o)<<6|s)}else i+=String.fromCharCode(o)}return i}function g(e,t){return e?w(P,e,t):""}function _(e,t,r,n){if(!(n>0))return 0;for(var a=r,i=r+n-1,o=0;o<e.length;++o){var s=e.charCodeAt(o);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++o)),s<=127){if(r>=i)break;t[r++]=s}else if(s<=2047){if(r+1>=i)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=i)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-a}function b(e,t,r){return _(e,P,t,r)}function E(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++r)),n<=127?++t:t+=n<=2047?2:n<=65535?3:4}return t}var C,x,P,T,I,A,k,S,L,D="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function F(e,t){for(var r=e,n=r>>1,a=n+t/2;!(n>=a)&&I[n];)++n;if((r=n<<1)-e>32&&D)return D.decode(P.subarray(e,r));for(var i="",o=0;!(o>=t/2);++o){var s=T[e+2*o>>1];if(0==s)break;i+=String.fromCharCode(s)}return i}function M(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,a=(r-=2)<2*e.length?r/2:e.length,i=0;i<a;++i){var o=e.charCodeAt(i);T[t>>1]=o,t+=2}return T[t>>1]=0,t-n}function B(e){return 2*e.length}function R(e,t){for(var r=0,n="";!(r>=t/4);){var a=A[e+4*r>>2];if(0==a)break;if(++r,a>=65536){var i=a-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(a)}return n}function G(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,a=n+r-4,i=0;i<e.length;++i){var o=e.charCodeAt(i);if(o>=55296&&o<=57343&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++i)),A[t>>2]=o,(t+=4)+4>a)break}return A[t>>2]=0,t-n}function O(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t}function j(e){C=e,n.HEAP8=x=new Int8Array(e),n.HEAP16=T=new Int16Array(e),n.HEAP32=A=new Int32Array(e),n.HEAPU8=P=new Uint8Array(e),n.HEAPU16=I=new Uint16Array(e),n.HEAPU32=k=new Uint32Array(e),n.HEAPF32=S=new Float32Array(e),n.HEAPF64=L=new Float64Array(e)}n.INITIAL_MEMORY;var $,N,U,H,V=[],W=[],z=[],q=0,X=null;function J(e){q++,n.monitorRunDependencies&&n.monitorRunDependencies(q)}function K(e){if(q--,n.monitorRunDependencies&&n.monitorRunDependencies(q),0==q&&X){var t=X;X=null,t()}}function Q(e){n.onAbort&&n.onAbort(e),h(e="Aborted("+e+")"),p=!0,m=1,e+=". Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw r(t),t}function Y(e){return e.startsWith("data:application/octet-stream;base64,")}function Z(e){try{if(e==$&&c)return new Uint8Array(c);throw"both async and sync fetching of the wasm failed"}catch(e){Q(e)}}function ee(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var r=t.func;"number"==typeof r?void 0===t.arg?wr.call(null,r):(a=t.arg,vr.apply(null,[r,a])):r(void 0===t.arg?null:t.arg)}else t(n)}var a}function te(e){this.excPtr=e,this.ptr=e-16,this.set_type=function(e){A[this.ptr+4>>2]=e},this.get_type=function(){return A[this.ptr+4>>2]},this.set_destructor=function(e){A[this.ptr+8>>2]=e},this.get_destructor=function(){return A[this.ptr+8>>2]},this.set_refcount=function(e){A[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,x[this.ptr+12>>0]=e},this.get_caught=function(){return 0!=x[this.ptr+12>>0]},this.set_rethrown=function(e){e=e?1:0,x[this.ptr+13>>0]=e},this.get_rethrown=function(){return 0!=x[this.ptr+13>>0]},this.init=function(e,t){this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=A[this.ptr>>2];A[this.ptr>>2]=e+1},this.release_ref=function(){var e=A[this.ptr>>2];return A[this.ptr>>2]=e-1,1===e}}function re(e){return A[pr()>>2]=e,e}n.preloadedImages={},n.preloadedAudios={},Y($="libpag.wasm")||(N=$,$=n.locateFile?n.locateFile(N,u):u+N);var ne={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,t){for(var r=0,n=e.length-1;n>=0;n--){var a=e[n];"."===a?e.splice(n,1):".."===a?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:function(e){var t="/"===e.charAt(0),r="/"===e.substr(-1);return(e=ne.normalizeArray(e.split("/").filter((function(e){return!!e})),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:function(e){var t=ne.splitPath(e),r=t[0],n=t[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:function(e){if("/"===e)return"/";var t=(e=(e=ne.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},extname:function(e){return ne.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return ne.normalize(e.join("/"))},join2:function(e,t){return ne.normalize(e+"/"+t)}},ae={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var n=r>=0?arguments[r]:se.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t="/"===n.charAt(0)}return(t?"/":"")+(e=ne.normalizeArray(e.split("/").filter((function(e){return!!e})),!t).join("/"))||"."},relative:function(e,t){function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=ae.resolve(e).substr(1),t=ae.resolve(t).substr(1);for(var n=r(e.split("/")),a=r(t.split("/")),i=Math.min(n.length,a.length),o=i,s=0;s<i;s++)if(n[s]!==a[s]){o=s;break}var l=[];for(s=o;s<n.length;s++)l.push("..");return(l=l.concat(a.slice(o))).join("/")}},ie={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){ie.ttys[e]={input:[],output:[],ops:t},se.registerDevice(e,ie.stream_ops)},stream_ops:{open:function(e){var t=ie.ttys[e.node.rdev];if(!t)throw new se.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,n,a){if(!e.tty||!e.tty.ops.get_char)throw new se.ErrnoError(60);for(var i=0,o=0;o<n;o++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new se.ErrnoError(29)}if(void 0===s&&0===i)throw new se.ErrnoError(6);if(null==s)break;i++,t[r+o]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,t,r,n,a){if(!e.tty||!e.tty.ops.put_char)throw new se.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,t[r+i])}catch(e){throw new se.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n"),!t)return null;e.input=cr(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(d(w(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(d(w(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(h(w(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(h(w(e.output,0)),e.output=[])}}},oe={ops_table:null,mount:function(e){return oe.createNode(null,"/",16895,0)},createNode:function(e,t,r,n){if(se.isBlkdev(r)||se.isFIFO(r))throw new se.ErrnoError(63);oe.ops_table||(oe.ops_table={dir:{node:{getattr:oe.node_ops.getattr,setattr:oe.node_ops.setattr,lookup:oe.node_ops.lookup,mknod:oe.node_ops.mknod,rename:oe.node_ops.rename,unlink:oe.node_ops.unlink,rmdir:oe.node_ops.rmdir,readdir:oe.node_ops.readdir,symlink:oe.node_ops.symlink},stream:{llseek:oe.stream_ops.llseek}},file:{node:{getattr:oe.node_ops.getattr,setattr:oe.node_ops.setattr},stream:{llseek:oe.stream_ops.llseek,read:oe.stream_ops.read,write:oe.stream_ops.write,allocate:oe.stream_ops.allocate,mmap:oe.stream_ops.mmap,msync:oe.stream_ops.msync}},link:{node:{getattr:oe.node_ops.getattr,setattr:oe.node_ops.setattr,readlink:oe.node_ops.readlink},stream:{}},chrdev:{node:{getattr:oe.node_ops.getattr,setattr:oe.node_ops.setattr},stream:se.chrdev_stream_ops}});var a=se.createNode(e,t,r,n);return se.isDir(a.mode)?(a.node_ops=oe.ops_table.dir.node,a.stream_ops=oe.ops_table.dir.stream,a.contents={}):se.isFile(a.mode)?(a.node_ops=oe.ops_table.file.node,a.stream_ops=oe.ops_table.file.stream,a.usedBytes=0,a.contents=null):se.isLink(a.mode)?(a.node_ops=oe.ops_table.link.node,a.stream_ops=oe.ops_table.link.stream):se.isChrdev(a.mode)&&(a.node_ops=oe.ops_table.chrdev.node,a.stream_ops=oe.ops_table.chrdev.stream),a.timestamp=Date.now(),e&&(e.contents[t]=a,e.timestamp=a.timestamp),a},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var n=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=se.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,se.isDir(e.mode)?t.size=4096:se.isFile(e.mode)?t.size=e.usedBytes:se.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&oe.resizeFileStorage(e,t.size)},lookup:function(e,t){throw se.genericErrors[44]},mknod:function(e,t,r,n){return oe.createNode(e,t,r,n)},rename:function(e,t,r){if(se.isDir(e.mode)){var n;try{n=se.lookupNode(t,r)}catch(e){}if(n)for(var a in n.contents)throw new se.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=se.lookupNode(e,t);for(var n in r.contents)throw new se.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var n=oe.createNode(e,t,41471,0);return n.link=r,n},readlink:function(e){if(!se.isLink(e.mode))throw new se.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,n,a){var i=e.node.contents;if(a>=e.node.usedBytes)return 0;var o=Math.min(e.node.usedBytes-a,n);if(o>8&&i.subarray)t.set(i.subarray(a,a+o),r);else for(var s=0;s<o;s++)t[r+s]=i[a+s];return o},write:function(e,t,r,n,a,i){if(t.buffer===x.buffer&&(i=!1),!n)return 0;var o=e.node;if(o.timestamp=Date.now(),t.subarray&&(!o.contents||o.contents.subarray)){if(i)return o.contents=t.subarray(r,r+n),o.usedBytes=n,n;if(0===o.usedBytes&&0===a)return o.contents=t.slice(r,r+n),o.usedBytes=n,n;if(a+n<=o.usedBytes)return o.contents.set(t.subarray(r,r+n),a),n}if(oe.expandFileStorage(o,a+n),o.contents.subarray&&t.subarray)o.contents.set(t.subarray(r,r+n),a);else for(var s=0;s<n;s++)o.contents[a+s]=t[r+s];return o.usedBytes=Math.max(o.usedBytes,a+n),n},llseek:function(e,t,r){var n=t;if(1===r?n+=e.position:2===r&&se.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new se.ErrnoError(28);return n},allocate:function(e,t,r){oe.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,n,a,i){if(0!==t)throw new se.ErrnoError(28);if(!se.isFile(e.node.mode))throw new se.ErrnoError(43);var o,s,l=e.node.contents;if(2&i||l.buffer!==C){if((n>0||n+r<l.length)&&(l=l.subarray?l.subarray(n,n+r):Array.prototype.slice.call(l,n,n+r)),s=!0,!(o=void Q()))throw new se.ErrnoError(48);x.set(l,o)}else s=!1,o=l.byteOffset;return{ptr:o,allocated:s}},msync:function(e,t,r,n,a){if(!se.isFile(e.node.mode))throw new se.ErrnoError(43);return 2&a||oe.stream_ops.write(e,t,0,n,r,!1),0}}},se={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(e,t={})=>{if(!(e=ae.resolve(se.cwd(),e)))return{path:"",node:null};var r={follow_mount:!0,recurse_count:0};for(var n in r)void 0===t[n]&&(t[n]=r[n]);if(t.recurse_count>8)throw new se.ErrnoError(32);for(var a=ne.normalizeArray(e.split("/").filter((e=>!!e)),!1),i=se.root,o="/",s=0;s<a.length;s++){var l=s===a.length-1;if(l&&t.parent)break;if(i=se.lookupNode(i,a[s]),o=ne.join2(o,a[s]),se.isMountpoint(i)&&(!l||l&&t.follow_mount)&&(i=i.mounted.root),!l||t.follow)for(var u=0;se.isLink(i.mode);){var c=se.readlink(o);if(o=ae.resolve(ne.dirname(o),c),i=se.lookupPath(o,{recurse_count:t.recurse_count}).node,u++>40)throw new se.ErrnoError(32)}}return{path:o,node:i}},getPath:e=>{for(var t;;){if(se.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%se.nameTable.length},hashAddNode:e=>{var t=se.hashName(e.parent.id,e.name);e.name_next=se.nameTable[t],se.nameTable[t]=e},hashRemoveNode:e=>{var t=se.hashName(e.parent.id,e.name);if(se.nameTable[t]===e)se.nameTable[t]=e.name_next;else for(var r=se.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=se.mayLookup(e);if(r)throw new se.ErrnoError(r,e);for(var n=se.hashName(e.id,t),a=se.nameTable[n];a;a=a.name_next){var i=a.name;if(a.parent.id===e.id&&i===t)return a}return se.lookup(e,t)},createNode:(e,t,r,n)=>{var a=new se.FSNode(e,t,r,n);return se.hashAddNode(a),a},destroyNode:e=>{se.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=se.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>se.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=se.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return se.lookupNode(e,t),20}catch(e){}return se.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var n;try{n=se.lookupNode(e,t)}catch(e){return e.errno}var a=se.nodePermissions(e,"wx");if(a)return a;if(r){if(!se.isDir(n.mode))return 54;if(se.isRoot(n)||se.getPath(n)===se.cwd())return 10}else if(se.isDir(n.mode))return 31;return 0},mayOpen:(e,t)=>e?se.isLink(e.mode)?32:se.isDir(e.mode)&&("r"!==se.flagsToPermissionString(t)||512&t)?31:se.nodePermissions(e,se.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:(e=0,t=se.MAX_OPEN_FDS)=>{for(var r=e;r<=t;r++)if(!se.streams[r])return r;throw new se.ErrnoError(33)},getStream:e=>se.streams[e],createStream:(e,t,r)=>{se.FSStream||(se.FSStream=function(){},se.FSStream.prototype={object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}});var n=new se.FSStream;for(var a in e)n[a]=e[a];e=n;var i=se.nextfd(t,r);return e.fd=i,se.streams[i]=e,e},closeStream:e=>{se.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=se.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new se.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{se.devices[e]={stream_ops:t}},getDevice:e=>se.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),se.syncFSRequests++,se.syncFSRequests>1&&h("warning: "+se.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=se.getMounts(se.root.mount),n=0;function a(e){return se.syncFSRequests--,t(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,a(e));++n>=r.length&&a(null)}r.forEach((t=>{if(!t.type.syncfs)return i(null);t.type.syncfs(t,e,i)}))},mount:(e,t,r)=>{var n,a="/"===r,i=!r;if(a&&se.root)throw new se.ErrnoError(10);if(!a&&!i){var o=se.lookupPath(r,{follow_mount:!1});if(r=o.path,n=o.node,se.isMountpoint(n))throw new se.ErrnoError(10);if(!se.isDir(n.mode))throw new se.ErrnoError(54)}var s={type:e,opts:t,mountpoint:r,mounts:[]},l=e.mount(s);return l.mount=s,s.root=l,a?se.root=l:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),l},unmount:e=>{var t=se.lookupPath(e,{follow_mount:!1});if(!se.isMountpoint(t.node))throw new se.ErrnoError(28);var r=t.node,n=r.mounted,a=se.getMounts(n);Object.keys(se.nameTable).forEach((e=>{for(var t=se.nameTable[e];t;){var r=t.name_next;a.includes(t.mount)&&se.destroyNode(t),t=r}})),r.mounted=null;var i=r.mount.mounts.indexOf(n);r.mount.mounts.splice(i,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var n=se.lookupPath(e,{parent:!0}).node,a=ne.basename(e);if(!a||"."===a||".."===a)throw new se.ErrnoError(28);var i=se.mayCreate(n,a);if(i)throw new se.ErrnoError(i);if(!n.node_ops.mknod)throw new se.ErrnoError(63);return n.node_ops.mknod(n,a,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,se.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,se.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),n="",a=0;a<r.length;++a)if(r[a]){n+="/"+r[a];try{se.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,se.mknod(e,t,r)),symlink:(e,t)=>{if(!ae.resolve(e))throw new se.ErrnoError(44);var r=se.lookupPath(t,{parent:!0}).node;if(!r)throw new se.ErrnoError(44);var n=ne.basename(t),a=se.mayCreate(r,n);if(a)throw new se.ErrnoError(a);if(!r.node_ops.symlink)throw new se.ErrnoError(63);return r.node_ops.symlink(r,n,e)},rename:(e,t)=>{var r,n,a=ne.dirname(e),i=ne.dirname(t),o=ne.basename(e),s=ne.basename(t);if(r=se.lookupPath(e,{parent:!0}).node,n=se.lookupPath(t,{parent:!0}).node,!r||!n)throw new se.ErrnoError(44);if(r.mount!==n.mount)throw new se.ErrnoError(75);var l,u=se.lookupNode(r,o),c=ae.relative(e,i);if("."!==c.charAt(0))throw new se.ErrnoError(28);if("."!==(c=ae.relative(t,a)).charAt(0))throw new se.ErrnoError(55);try{l=se.lookupNode(n,s)}catch(e){}if(u!==l){var f=se.isDir(u.mode),d=se.mayDelete(r,o,f);if(d)throw new se.ErrnoError(d);if(d=l?se.mayDelete(n,s,f):se.mayCreate(n,s))throw new se.ErrnoError(d);if(!r.node_ops.rename)throw new se.ErrnoError(63);if(se.isMountpoint(u)||l&&se.isMountpoint(l))throw new se.ErrnoError(10);if(n!==r&&(d=se.nodePermissions(r,"w")))throw new se.ErrnoError(d);se.hashRemoveNode(u);try{r.node_ops.rename(u,n,s)}catch(e){throw e}finally{se.hashAddNode(u)}}},rmdir:e=>{var t=se.lookupPath(e,{parent:!0}).node,r=ne.basename(e),n=se.lookupNode(t,r),a=se.mayDelete(t,r,!0);if(a)throw new se.ErrnoError(a);if(!t.node_ops.rmdir)throw new se.ErrnoError(63);if(se.isMountpoint(n))throw new se.ErrnoError(10);t.node_ops.rmdir(t,r),se.destroyNode(n)},readdir:e=>{var t=se.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new se.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=se.lookupPath(e,{parent:!0}).node;if(!t)throw new se.ErrnoError(44);var r=ne.basename(e),n=se.lookupNode(t,r),a=se.mayDelete(t,r,!1);if(a)throw new se.ErrnoError(a);if(!t.node_ops.unlink)throw new se.ErrnoError(63);if(se.isMountpoint(n))throw new se.ErrnoError(10);t.node_ops.unlink(t,r),se.destroyNode(n)},readlink:e=>{var t=se.lookupPath(e).node;if(!t)throw new se.ErrnoError(44);if(!t.node_ops.readlink)throw new se.ErrnoError(28);return ae.resolve(se.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=se.lookupPath(e,{follow:!t}).node;if(!r)throw new se.ErrnoError(44);if(!r.node_ops.getattr)throw new se.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>se.stat(e,!0),chmod:(e,t,r)=>{var n;if(!(n="string"==typeof e?se.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new se.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&t|-4096&n.mode,timestamp:Date.now()})},lchmod:(e,t)=>{se.chmod(e,t,!0)},fchmod:(e,t)=>{var r=se.getStream(e);if(!r)throw new se.ErrnoError(8);se.chmod(r.node,t)},chown:(e,t,r,n)=>{var a;if(!(a="string"==typeof e?se.lookupPath(e,{follow:!n}).node:e).node_ops.setattr)throw new se.ErrnoError(63);a.node_ops.setattr(a,{timestamp:Date.now()})},lchown:(e,t,r)=>{se.chown(e,t,r,!0)},fchown:(e,t,r)=>{var n=se.getStream(e);if(!n)throw new se.ErrnoError(8);se.chown(n.node,t,r)},truncate:(e,t)=>{if(t<0)throw new se.ErrnoError(28);var r;if(!(r="string"==typeof e?se.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new se.ErrnoError(63);if(se.isDir(r.mode))throw new se.ErrnoError(31);if(!se.isFile(r.mode))throw new se.ErrnoError(28);var n=se.nodePermissions(r,"w");if(n)throw new se.ErrnoError(n);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=se.getStream(e);if(!r)throw new se.ErrnoError(8);if(0==(2097155&r.flags))throw new se.ErrnoError(28);se.truncate(r.node,t)},utime:(e,t,r)=>{var n=se.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(t,r)})},open:(e,t,r,a,i)=>{if(""===e)throw new se.ErrnoError(44);var o;if(r=void 0===r?438:r,r=64&(t="string"==typeof t?se.modeStringToFlags(t):t)?4095&r|32768:0,"object"==typeof e)o=e;else{e=ne.normalize(e);try{o=se.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var s=!1;if(64&t)if(o){if(128&t)throw new se.ErrnoError(20)}else o=se.mknod(e,r,0),s=!0;if(!o)throw new se.ErrnoError(44);if(se.isChrdev(o.mode)&&(t&=-513),65536&t&&!se.isDir(o.mode))throw new se.ErrnoError(54);if(!s){var l=se.mayOpen(o,t);if(l)throw new se.ErrnoError(l)}512&t&&se.truncate(o,0),t&=-131713;var u=se.createStream({node:o,path:se.getPath(o),flags:t,seekable:!0,position:0,stream_ops:o.stream_ops,ungotten:[],error:!1},a,i);return u.stream_ops.open&&u.stream_ops.open(u),!n.logReadFiles||1&t||(se.readFiles||(se.readFiles={}),e in se.readFiles||(se.readFiles[e]=1)),u},close:e=>{if(se.isClosed(e))throw new se.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{se.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(se.isClosed(e))throw new se.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new se.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new se.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,n,a)=>{if(n<0||a<0)throw new se.ErrnoError(28);if(se.isClosed(e))throw new se.ErrnoError(8);if(1==(2097155&e.flags))throw new se.ErrnoError(8);if(se.isDir(e.node.mode))throw new se.ErrnoError(31);if(!e.stream_ops.read)throw new se.ErrnoError(28);var i=void 0!==a;if(i){if(!e.seekable)throw new se.ErrnoError(70)}else a=e.position;var o=e.stream_ops.read(e,t,r,n,a);return i||(e.position+=o),o},write:(e,t,r,n,a,i)=>{if(n<0||a<0)throw new se.ErrnoError(28);if(se.isClosed(e))throw new se.ErrnoError(8);if(0==(2097155&e.flags))throw new se.ErrnoError(8);if(se.isDir(e.node.mode))throw new se.ErrnoError(31);if(!e.stream_ops.write)throw new se.ErrnoError(28);e.seekable&&1024&e.flags&&se.llseek(e,0,2);var o=void 0!==a;if(o){if(!e.seekable)throw new se.ErrnoError(70)}else a=e.position;var s=e.stream_ops.write(e,t,r,n,a,i);return o||(e.position+=s),s},allocate:(e,t,r)=>{if(se.isClosed(e))throw new se.ErrnoError(8);if(t<0||r<=0)throw new se.ErrnoError(28);if(0==(2097155&e.flags))throw new se.ErrnoError(8);if(!se.isFile(e.node.mode)&&!se.isDir(e.node.mode))throw new se.ErrnoError(43);if(!e.stream_ops.allocate)throw new se.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,n,a,i)=>{if(0!=(2&a)&&0==(2&i)&&2!=(2097155&e.flags))throw new se.ErrnoError(2);if(1==(2097155&e.flags))throw new se.ErrnoError(2);if(!e.stream_ops.mmap)throw new se.ErrnoError(43);return e.stream_ops.mmap(e,t,r,n,a,i)},msync:(e,t,r,n,a)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,a):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new se.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:(e,t={})=>{if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,n=se.open(e,t.flags),a=se.stat(e).size,i=new Uint8Array(a);return se.read(n,i,0,a,0),"utf8"===t.encoding?r=w(i,0):"binary"===t.encoding&&(r=i),se.close(n),r},writeFile:(e,t,r={})=>{r.flags=r.flags||577;var n=se.open(e,r.flags,r.mode);if("string"==typeof t){var a=new Uint8Array(E(t)+1),i=_(t,a,0,a.length);se.write(n,a,0,i,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");se.write(n,t,0,t.byteLength,void 0,r.canOwn)}se.close(n)},cwd:()=>se.currentPath,chdir:e=>{var t=se.lookupPath(e,{follow:!0});if(null===t.node)throw new se.ErrnoError(44);if(!se.isDir(t.node.mode))throw new se.ErrnoError(54);var r=se.nodePermissions(t.node,"x");if(r)throw new se.ErrnoError(r);se.currentPath=t.path},createDefaultDirectories:()=>{se.mkdir("/tmp"),se.mkdir("/home"),se.mkdir("/home/<USER>")},createDefaultDevices:()=>{se.mkdir("/dev"),se.registerDevice(se.makedev(1,3),{read:()=>0,write:(e,t,r,n,a)=>n}),se.mkdev("/dev/null",se.makedev(1,3)),ie.register(se.makedev(5,0),ie.default_tty_ops),ie.register(se.makedev(6,0),ie.default_tty1_ops),se.mkdev("/dev/tty",se.makedev(5,0)),se.mkdev("/dev/tty1",se.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}return function(){Q("randomDevice")}}();se.createDevice("/dev","random",e),se.createDevice("/dev","urandom",e),se.mkdir("/dev/shm"),se.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{se.mkdir("/proc");var e=se.mkdir("/proc/self");se.mkdir("/proc/self/fd"),se.mount({mount:()=>{var t=se.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,n=se.getStream(r);if(!n)throw new se.ErrnoError(8);var a={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>n.path}};return a.parent=a,a}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{n.stdin?se.createDevice("/dev","stdin",n.stdin):se.symlink("/dev/tty","/dev/stdin"),n.stdout?se.createDevice("/dev","stdout",null,n.stdout):se.symlink("/dev/tty","/dev/stdout"),n.stderr?se.createDevice("/dev","stderr",null,n.stderr):se.symlink("/dev/tty1","/dev/stderr"),se.open("/dev/stdin",0),se.open("/dev/stdout",1),se.open("/dev/stderr",1)},ensureErrnoError:()=>{se.ErrnoError||(se.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},se.ErrnoError.prototype=new Error,se.ErrnoError.prototype.constructor=se.ErrnoError,[44].forEach((e=>{se.genericErrors[e]=new se.ErrnoError(e),se.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{se.ensureErrnoError(),se.nameTable=new Array(4096),se.mount(oe,{},"/"),se.createDefaultDirectories(),se.createDefaultDevices(),se.createSpecialDirectories(),se.filesystems={MEMFS:oe}},init:(e,t,r)=>{se.init.initialized=!0,se.ensureErrnoError(),n.stdin=e||n.stdin,n.stdout=t||n.stdout,n.stderr=r||n.stderr,se.createStandardStreams()},quit:()=>{se.init.initialized=!1;for(var e=0;e<se.streams.length;e++){var t=se.streams[e];t&&se.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=se.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(n=se.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=se.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=ne.basename(e),n=se.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,n)=>{e="string"==typeof e?e:se.getPath(e);for(var a=t.split("/").reverse();a.length;){var i=a.pop();if(i){var o=ne.join2(e,i);try{se.mkdir(o)}catch(e){}e=o}}return o},createFile:(e,t,r,n,a)=>{var i=ne.join2("string"==typeof e?e:se.getPath(e),t),o=se.getMode(n,a);return se.create(i,o)},createDataFile:(e,t,r,n,a,i)=>{var o=t;e&&(e="string"==typeof e?e:se.getPath(e),o=t?ne.join2(e,t):e);var s=se.getMode(n,a),l=se.create(o,s);if(r){if("string"==typeof r){for(var u=new Array(r.length),c=0,f=r.length;c<f;++c)u[c]=r.charCodeAt(c);r=u}se.chmod(l,146|s);var d=se.open(l,577);se.write(d,r,0,r.length,0,i),se.close(d),se.chmod(l,s)}return l},createDevice:(e,t,r,n)=>{var a=ne.join2("string"==typeof e?e:se.getPath(e),t),i=se.getMode(!!r,!!n);se.createDevice.major||(se.createDevice.major=64);var o=se.makedev(se.createDevice.major++,0);return se.registerDevice(o,{open:e=>{e.seekable=!1},close:e=>{n&&n.buffer&&n.buffer.length&&n(10)},read:(e,t,n,a,i)=>{for(var o=0,s=0;s<a;s++){var l;try{l=r()}catch(e){throw new se.ErrnoError(29)}if(void 0===l&&0===o)throw new se.ErrnoError(6);if(null==l)break;o++,t[n+s]=l}return o&&(e.node.timestamp=Date.now()),o},write:(e,t,r,a,i)=>{for(var o=0;o<a;o++)try{n(t[r+o])}catch(e){throw new se.ErrnoError(29)}return a&&(e.node.timestamp=Date.now()),o}}),se.mkdev(a,i,o)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!i)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=cr(i(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new se.ErrnoError(29)}},createLazyFile:(e,t,r,n,a)=>{function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),a=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,i=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,o=1048576;a||(o=n);var s=this;s.setDataGetter((e=>{var t=e*o,a=(e+1)*o-1;if(a=Math.min(a,n-1),void 0===s.chunks[e]&&(s.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>n-1)throw new Error("only "+n+" bytes available! programmer error!");var a=new XMLHttpRequest;if(a.open("GET",r,!1),n!==o&&a.setRequestHeader("Range","bytes="+e+"-"+t),"undefined"!=typeof Uint8Array&&(a.responseType="arraybuffer"),a.overrideMimeType&&a.overrideMimeType("text/plain; charset=x-user-defined"),a.send(null),!(a.status>=200&&a.status<300||304===a.status))throw new Error("Couldn't load "+r+". Status: "+a.status);return void 0!==a.response?new Uint8Array(a.response||[]):cr(a.responseText||"",!0)})(t,a)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!i&&n||(o=n=1,n=this.getter(0).length,o=n,d("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var o={isDevice:!1,url:r},s=se.createFile(e,t,o,n,a);o.contents?s.contents=o.contents:o.url&&(s.contents=null,s.url=o.url),Object.defineProperties(s,{usedBytes:{get:function(){return this.contents.length}}});var l={};return Object.keys(s.stream_ops).forEach((e=>{var t=s.stream_ops[e];l[e]=function(){return se.forceLoadFile(s),t.apply(null,arguments)}})),l.read=(e,t,r,n,a)=>{se.forceLoadFile(s);var i=e.node.contents;if(a>=i.length)return 0;var o=Math.min(i.length-a,n);if(i.slice)for(var l=0;l<o;l++)t[r+l]=i[a+l];else for(l=0;l<o;l++)t[r+l]=i.get(a+l);return o},s.stream_ops=l,s},createPreloadedFile:(e,t,r,n,a,i,s,l,u,c)=>{var f=t?ae.resolve(ne.join2(e,t)):e;function d(r){function o(r){c&&c(),l||se.createDataFile(e,t,r,n,a,u),i&&i(),K()}Browser.handledByPreloadPlugin(r,f,o,(()=>{s&&s(),K()}))||o(r)}J(),"string"==typeof r?function(e,t,r,n){var a=n?"":"al "+e;o(e,(function(r){v(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),a&&K()}),(function(t){if(!r)throw'Loading data file "'+e+'" failed.';r()})),a&&J()}(r,(e=>d(e)),s):d(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var n=se.indexedDB();try{var a=n.open(se.DB_NAME(),se.DB_VERSION)}catch(e){return r(e)}a.onupgradeneeded=()=>{d("creating db"),a.result.createObjectStore(se.DB_STORE_NAME)},a.onsuccess=()=>{var n=a.result.transaction([se.DB_STORE_NAME],"readwrite"),i=n.objectStore(se.DB_STORE_NAME),o=0,s=0,l=e.length;function u(){0==s?t():r()}e.forEach((e=>{var t=i.put(se.analyzePath(e).object.contents,e);t.onsuccess=()=>{++o+s==l&&u()},t.onerror=()=>{s++,o+s==l&&u()}})),n.onerror=r},a.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var n=se.indexedDB();try{var a=n.open(se.DB_NAME(),se.DB_VERSION)}catch(e){return r(e)}a.onupgradeneeded=r,a.onsuccess=()=>{var n=a.result;try{var i=n.transaction([se.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var o=i.objectStore(se.DB_STORE_NAME),s=0,l=0,u=e.length;function c(){0==l?t():r()}e.forEach((e=>{var t=o.get(e);t.onsuccess=()=>{se.analyzePath(e).exists&&se.unlink(e),se.createDataFile(ne.dirname(e),ne.basename(e),t.result,!0,!0,!0),++s+l==u&&c()},t.onerror=()=>{l++,s+l==u&&c()}})),i.onerror=r},a.onerror=r}},le={mappings:{},DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if("/"===t[0])return t;var n;if(-100===e)n=se.cwd();else{var a=se.getStream(e);if(!a)throw new se.ErrnoError(8);n=a.path}if(0==t.length){if(!r)throw new se.ErrnoError(44);return n}return ne.join2(n,t)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&ne.normalize(t)!==ne.normalize(se.getPath(e.node)))return-54;throw e}return A[r>>2]=n.dev,A[r+4>>2]=0,A[r+8>>2]=n.ino,A[r+12>>2]=n.mode,A[r+16>>2]=n.nlink,A[r+20>>2]=n.uid,A[r+24>>2]=n.gid,A[r+28>>2]=n.rdev,A[r+32>>2]=0,H=[n.size>>>0,(U=n.size,+Math.abs(U)>=1?U>0?(0|Math.min(+Math.floor(U/4294967296),4294967295))>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],A[r+40>>2]=H[0],A[r+44>>2]=H[1],A[r+48>>2]=4096,A[r+52>>2]=n.blocks,A[r+56>>2]=n.atime.getTime()/1e3|0,A[r+60>>2]=0,A[r+64>>2]=n.mtime.getTime()/1e3|0,A[r+68>>2]=0,A[r+72>>2]=n.ctime.getTime()/1e3|0,A[r+76>>2]=0,H=[n.ino>>>0,(U=n.ino,+Math.abs(U)>=1?U>0?(0|Math.min(+Math.floor(U/4294967296),4294967295))>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],A[r+80>>2]=H[0],A[r+84>>2]=H[1],0},doMsync:function(e,t,r,n,a){var i=P.slice(e,e+r);se.msync(t,i,a,r,n)},doMkdir:function(e,t){return"/"===(e=ne.normalize(e))[e.length-1]&&(e=e.substr(0,e.length-1)),se.mkdir(e,t,0),0},doMknod:function(e,t,r){switch(61440&t){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return se.mknod(e,t,r),0},doReadlink:function(e,t,r){if(r<=0)return-28;var n=se.readlink(e),a=Math.min(r,E(n)),i=x[t+a];return b(n,t,r+1),x[t+a]=i,a},doAccess:function(e,t){if(-8&t)return-28;var r=se.lookupPath(e,{follow:!0}).node;if(!r)return-44;var n="";return 4&t&&(n+="r"),2&t&&(n+="w"),1&t&&(n+="x"),n&&se.nodePermissions(r,n)?-2:0},doDup:function(e,t,r){var n=se.getStream(r);return n&&se.close(n),se.open(e,t,0,r,r).fd},doReadv:function(e,t,r,n){for(var a=0,i=0;i<r;i++){var o=A[t+8*i>>2],s=A[t+(8*i+4)>>2],l=se.read(e,x,o,s,n);if(l<0)return-1;if(a+=l,l<s)break}return a},doWritev:function(e,t,r,n){for(var a=0,i=0;i<r;i++){var o=A[t+8*i>>2],s=A[t+(8*i+4)>>2],l=se.write(e,x,o,s,n);if(l<0)return-1;a+=l}return a},varargs:void 0,get:function(){return le.varargs+=4,A[le.varargs-4>>2]},getStr:function(e){return g(e)},getStreamFromFD:function(e){var t=se.getStream(e);if(!t)throw new se.ErrnoError(8);return t},get64:function(e,t){return e}},ue={};function ce(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function fe(e){return this.fromWireType(k[e>>2])}var de={},he={},me={};function pe(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=48&&t<=57?"_"+e:e}function ve(e,t){return e=pe(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function ye(e,t){var r=ve(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var we=void 0;function ge(e){throw new we(e)}function _e(e,t,r){function n(t){var n=r(t);n.length!==e.length&&ge("Mismatched type converter count");for(var a=0;a<e.length;++a)Te(e[a],n[a])}e.forEach((function(e){me[e]=t}));var a=new Array(t.length),i=[],o=0;t.forEach((function(e,t){he.hasOwnProperty(e)?a[t]=he[e]:(i.push(e),de.hasOwnProperty(e)||(de[e]=[]),de[e].push((function(){a[t]=he[e],++o===i.length&&n(a)})))})),0===i.length&&n(a)}function be(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var Ee=void 0;function Ce(e){for(var t="",r=e;P[r];)t+=Ee[P[r++]];return t}var xe=void 0;function Pe(e){throw new xe(e)}function Te(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||Pe('type "'+n+'" must have a positive integer typeid pointer'),he.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;Pe("Cannot register type '"+n+"' twice")}if(he[e]=t,delete me[e],de.hasOwnProperty(e)){var a=de[e];delete de[e],a.forEach((function(e){e()}))}}function Ie(e){if(!(this instanceof $e))return!1;if(!(e instanceof $e))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,a=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)a=n.upcast(a),n=n.baseClass;return t===n&&r===a}function Ae(e){Pe(e.$$.ptrType.registeredClass.name+" instance already deleted")}var ke=!1;function Se(e){}function Le(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function De(e){return"undefined"==typeof FinalizationGroup?(De=e=>e,e):(ke=new FinalizationGroup((function(e){for(var t=e.next();!t.done;t=e.next()){var r=t.value;r.ptr?Le(r):console.warn("object already deleted: "+r.ptr)}})),Se=e=>{ke.unregister(e.$$)},(De=e=>(ke.register(e,e.$$,e.$$),e))(e))}function Fe(){if(this.$$.ptr||Ae(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=De(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function Me(){this.$$.ptr||Ae(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Pe("Object already scheduled for deletion"),Se(this),Le(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Be(){return!this.$$.ptr}var Re=void 0,Ge=[];function Oe(){for(;Ge.length;){var e=Ge.pop();e.$$.deleteScheduled=!1,e.delete()}}function je(){return this.$$.ptr||Ae(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Pe("Object already scheduled for deletion"),Ge.push(this),1===Ge.length&&Re&&Re(Oe),this.$$.deleteScheduled=!0,this}function $e(){}var Ne={};function Ue(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Pe("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function He(e,t,r){n.hasOwnProperty(e)?((void 0===r||void 0!==n[e].overloadTable&&void 0!==n[e].overloadTable[r])&&Pe("Cannot register public name '"+e+"' twice"),Ue(n,e,e),n.hasOwnProperty(r)&&Pe("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),n[e].overloadTable[r]=t):(n[e]=t,void 0!==r&&(n[e].numArguments=r))}function Ve(e,t,r,n,a,i,o,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=a,this.getActualType=i,this.upcast=o,this.downcast=s,this.pureVirtualFunctions=[]}function We(e,t,r){for(;t!==r;)t.upcast||Pe("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function ze(e,t){if(null===t)return this.isReference&&Pe("null is not a valid "+this.name),0;t.$$||Pe('Cannot pass "'+kt(t)+'" as a '+this.name),t.$$.ptr||Pe("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return We(t.$$.ptr,r,this.registeredClass)}function qe(e,t){var r;if(null===t)return this.isReference&&Pe("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||Pe('Cannot pass "'+kt(t)+'" as a '+this.name),t.$$.ptr||Pe("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&Pe("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass;if(r=We(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&Pe("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:Pe("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var a=t.clone();r=this.rawShare(r,Tt.toHandle((function(){a.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:Pe("Unsupporting sharing policy")}return r}function Xe(e,t){if(null===t)return this.isReference&&Pe("null is not a valid "+this.name),0;t.$$||Pe('Cannot pass "'+kt(t)+'" as a '+this.name),t.$$.ptr||Pe("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&Pe("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return We(t.$$.ptr,r,this.registeredClass)}function Je(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function Ke(e){this.rawDestructor&&this.rawDestructor(e)}function Qe(e){null!==e&&e.delete()}function Ye(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=Ye(e,t,r.baseClass);return null===n?null:r.downcast(n)}function Ze(){return Object.keys(rt).length}function et(){var e=[];for(var t in rt)rt.hasOwnProperty(t)&&e.push(rt[t]);return e}function tt(e){Re=e,Ge.length&&Re&&Re(Oe)}var rt={};function at(e,t){return t=function(e,t){for(void 0===t&&Pe("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),rt[t]}function it(e,t){return t.ptrType&&t.ptr||ge("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&ge("Both smartPtrType and smartPtr must be specified"),t.count={value:1},De(Object.create(e,{$$:{value:t}}))}function ot(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=at(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function a(){return this.isSmartPointer?it(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):it(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,o=this.registeredClass.getActualType(t),s=Ne[o];if(!s)return a.call(this);i=this.isConst?s.constPointerType:s.pointerType;var l=Ye(t,this.registeredClass,i.registeredClass);return null===l?a.call(this):this.isSmartPointer?it(i.registeredClass.instancePrototype,{ptrType:i,ptr:l,smartPtrType:this,smartPtr:e}):it(i.registeredClass.instancePrototype,{ptrType:i,ptr:l})}function st(e,t,r,n,a,i,o,s,l,u,c){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=a,this.pointeeType=i,this.sharingPolicy=o,this.rawGetPointee=s,this.rawConstructor=l,this.rawShare=u,this.rawDestructor=c,a||void 0!==t.baseClass?this.toWireType=qe:n?(this.toWireType=ze,this.destructorFunction=null):(this.toWireType=Xe,this.destructorFunction=null)}function lt(e,t,r){n.hasOwnProperty(e)||ge("Replacing nonexistant public symbol"),void 0!==n[e].overloadTable&&void 0!==r?n[e].overloadTable[r]=t:(n[e]=t,n[e].argCount=r)}function ut(e,t,r){return function(e,t,r){var a=n["dynCall_"+e];return r&&r.length?a.apply(null,[t].concat(r)):a.call(null,t)}(e,t,r)}function ct(e,t){e=Ce(e);var r,n,a,i=(r=e,n=t,a=[],function(){a.length=arguments.length;for(var e=0;e<arguments.length;e++)a[e]=arguments[e];return ut(r,n,a)});return"function"!=typeof i&&Pe("unknown function pointer with signature "+e+": "+t),i}var ft=void 0;function dt(e){var t=mr(e),r=Ce(t);return dr(t),r}function ht(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||he[t]||(me[t]?me[t].forEach(e):(r.push(t),n[t]=!0))})),new ft(e+": "+r.map(dt).join([", "]))}function mt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=ve(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,a=e.apply(n,t);return a instanceof Object?a:n}function pt(e){try{return e()}catch(e){Q(e)}}function vt(e,t){if(!p)if(t)e();else try{e()}catch(e){!function(e){if(e instanceof gr||"unwind"==e)return m;l(1,e)}(e)}}var yt={State:{Normal:0,Unwinding:1,Rewinding:2,Disabled:3},state:0,StackSize:4096,currData:null,handleSleepReturnValue:0,exportCallStack:[],callStackNameToId:{},callStackIdToName:{},callStackId:0,asyncPromiseHandlers:null,sleepCallbacks:[],getCallStackId:function(e){var t=yt.callStackNameToId[e];return void 0===t&&(t=yt.callStackId++,yt.callStackNameToId[e]=t,yt.callStackIdToName[t]=e),t},instrumentWasmExports:function(e){var t={};for(var r in e)!function(r){var n=e[r];t[r]="function"==typeof n?function(){yt.exportCallStack.push(r);try{return n.apply(null,arguments)}finally{if(!p){var e=yt.exportCallStack.pop();v(e===r),yt.maybeStopUnwind()}}}:n}(r);return t},maybeStopUnwind:function(){yt.currData&&yt.state===yt.State.Unwinding&&0===yt.exportCallStack.length&&(yt.state=yt.State.Normal,pt(n._asyncify_stop_unwind),"undefined"!=typeof Fibers&&Fibers.trampoline())},whenDone:function(){return new Promise(((e,t)=>{yt.asyncPromiseHandlers={resolve:e,reject:t}}))},allocateData:function(){var e=hr(12+yt.StackSize);return yt.setDataHeader(e,e+12,yt.StackSize),yt.setDataRewindFunc(e),e},setDataHeader:function(e,t,r){A[e>>2]=t,A[e+4>>2]=t+r},setDataRewindFunc:function(e){var t=yt.exportCallStack[0],r=yt.getCallStackId(t);A[e+8>>2]=r},getDataRewindFunc:function(e){var t=A[e+8>>2],r=yt.callStackIdToName[t];return n.asm[r]},doRewind:function(e){return yt.getDataRewindFunc(e)()},handleSleep:function(e){if(!p){if(yt.state===yt.State.Normal){var t=!1,r=!1;e((e=>{if(!p&&(yt.handleSleepReturnValue=e||0,t=!0,r)){yt.state=yt.State.Rewinding,pt((()=>n._asyncify_start_rewind(yt.currData))),"undefined"!=typeof Browser&&Browser.mainLoop.func&&Browser.mainLoop.resume();var a,i=!1;try{a=yt.doRewind(yt.currData)}catch(e){a=e,i=!0}var o=!1;if(!yt.currData){var s=yt.asyncPromiseHandlers;s&&(yt.asyncPromiseHandlers=null,(i?s.reject:s.resolve)(a),o=!0)}if(i&&!o)throw a}})),r=!0,t||(yt.state=yt.State.Unwinding,yt.currData=yt.allocateData(),pt((()=>n._asyncify_start_unwind(yt.currData))),"undefined"!=typeof Browser&&Browser.mainLoop.func&&Browser.mainLoop.pause())}else yt.state===yt.State.Rewinding?(yt.state=yt.State.Normal,pt(n._asyncify_stop_rewind),dr(yt.currData),yt.currData=null,yt.sleepCallbacks.forEach((e=>vt(e)))):Q("invalid state: "+yt.state);return yt.handleSleepReturnValue}},handleAsync:function(e){return yt.handleSleep((t=>{e().then(t)}))}};function wt(e,t,r,n,a){var i=t.length;i<2&&Pe("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var o=null!==t[1]&&null!==r,s=!1,l=1;l<t.length;++l)if(null!==t[l]&&void 0===t[l].destructorFunction){s=!0;break}var u="void"!==t[0].name,c="",f="";for(l=0;l<i-2;++l)c+=(0!==l?", ":"")+"arg"+l,f+=(0!==l?", ":"")+"arg"+l+"Wired";var d="return function "+pe(e)+"("+c+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";s&&(d+="var destructors = [];\n");var h=s?"destructors":"null",m=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],p=[Pe,n,a,ce,t[0],t[1]];for(o&&(d+="var thisWired = classParam.toWireType("+h+", this);\n"),l=0;l<i-2;++l)d+="var arg"+l+"Wired = argType"+l+".toWireType("+h+", arg"+l+"); // "+t[l+2].name+"\n",m.push("argType"+l),p.push(t[l+2]);if(o&&(f="thisWired"+(f.length>0?", ":"")+f),d+=(u?"var rv = ":"")+"invoker(fn"+(f.length>0?", ":"")+f+");\n",m.push("Asyncify"),p.push(yt),d+="function onDone("+(u?"rv":"")+") {\n",s)d+="runDestructors(destructors);\n";else for(l=o?1:2;l<t.length;++l){var v=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==t[l].destructorFunction&&(d+=v+"_dtor("+v+"); // "+t[l].name+"\n",m.push(v+"_dtor"),p.push(t[l].destructorFunction))}return u&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",d+="return Asyncify.currData ? Asyncify.whenDone().then(onDone) : onDone("+(u?"rv":"")+");\n",d+="}\n",m.push(d),mt(Function,m).apply(null,p)}function gt(e,t){for(var r=[],n=0;n<e;n++)r.push(A[(t>>2)+n]);return r}function _t(e,t,r){return e instanceof Object||Pe(r+' with invalid "this": '+e),e instanceof t.registeredClass.constructor||Pe(r+' incompatible with "this" of type '+e.constructor.name),e.$$.ptr||Pe("cannot call emscripten binding method "+r+" on deleted object"),We(e.$$.ptr,e.$$.ptrType.registeredClass,t.registeredClass)}var bt=[],Et=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Ct(e){e>4&&0==--Et[e].refcount&&(Et[e]=void 0,bt.push(e))}function xt(){for(var e=0,t=5;t<Et.length;++t)void 0!==Et[t]&&++e;return e}function Pt(){for(var e=5;e<Et.length;++e)if(void 0!==Et[e])return Et[e];return null}var Tt={toValue:function(e){return e||Pe("Cannot use deleted val. handle = "+e),Et[e].value},toHandle:function(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=bt.length?bt.pop():Et.length;return Et[t]={refcount:1,value:e},t}}};function It(e,t,r){switch(t){case 0:return function(e){var t=r?x:P;return this.fromWireType(t[e])};case 1:return function(e){var t=r?T:I;return this.fromWireType(t[e>>1])};case 2:return function(e){var t=r?A:k;return this.fromWireType(t[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function At(e,t){var r=he[e];return void 0===r&&Pe(t+" has unknown type "+dt(e)),r}function kt(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function St(e,t){switch(t){case 2:return function(e){return this.fromWireType(S[e>>2])};case 3:return function(e){return this.fromWireType(L[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Lt(e,t,r){switch(t){case 0:return r?function(e){return x[e]}:function(e){return P[e]};case 1:return r?function(e){return T[e>>1]}:function(e){return I[e>>1]};case 2:return r?function(e){return A[e>>2]}:function(e){return k[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function Dt(e,t){for(var r=new Array(e),n=0;n<e;++n)r[n]=At(A[(t>>2)+n],"parameter "+n);return r}var Ft={};function Mt(e){var t=Ft[e];return void 0===t?Ce(e):t}var Bt=[];function Rt(){return"object"==typeof globalThis?globalThis:Function("return this")()}var Gt,Ot=[],jt={};Gt=()=>performance.now();var $t={inEventHandler:0,removeAllEventListeners:function(){for(var e=$t.eventHandlers.length-1;e>=0;--e)$t._removeHandler(e);$t.eventHandlers=[],$t.deferredCalls=[]},registerRemoveEventListeners:function(){$t.removeEventListenersRegistered||($t.removeEventListenersRegistered=!0)},deferredCalls:[],deferCall:function(e,t,r){function n(e,t){if(e.length!=t.length)return!1;for(var r in e)if(e[r]!=t[r])return!1;return!0}for(var a in $t.deferredCalls){var i=$t.deferredCalls[a];if(i.targetFunction==e&&n(i.argsList,r))return}$t.deferredCalls.push({targetFunction:e,precedence:t,argsList:r}),$t.deferredCalls.sort((function(e,t){return e.precedence<t.precedence}))},removeDeferredCalls:function(e){for(var t=0;t<$t.deferredCalls.length;++t)$t.deferredCalls[t].targetFunction==e&&($t.deferredCalls.splice(t,1),--t)},canPerformEventHandlerRequests:function(){return $t.inEventHandler&&$t.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if($t.canPerformEventHandlerRequests())for(var e=0;e<$t.deferredCalls.length;++e){var t=$t.deferredCalls[e];$t.deferredCalls.splice(e,1),--e,t.targetFunction.apply(null,t.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(e,t){for(var r=0;r<$t.eventHandlers.length;++r)$t.eventHandlers[r].target!=e||t&&t!=$t.eventHandlers[r].eventTypeString||$t._removeHandler(r--)},_removeHandler:function(e){var t=$t.eventHandlers[e];t.target.removeEventListener(t.eventTypeString,t.eventListenerFunc,t.useCapture),$t.eventHandlers.splice(e,1)},registerOrRemoveHandler:function(e){var t=function(t){++$t.inEventHandler,$t.currentEventHandler=e,$t.runDeferredCalls(),e.handlerFunc(t),$t.runDeferredCalls(),--$t.inEventHandler};if(e.callbackfunc)e.eventListenerFunc=t,e.target.addEventListener(e.eventTypeString,t,e.useCapture),$t.eventHandlers.push(e),$t.registerRemoveEventListeners();else for(var r=0;r<$t.eventHandlers.length;++r)$t.eventHandlers[r].target==e.target&&$t.eventHandlers[r].eventTypeString==e.eventTypeString&&$t._removeHandler(r--)},getNodeNameForTarget:function(e){return e?e==window?"#window":e==screen?"#screen":e&&e.nodeName?e.nodeName:"":""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}},Nt=[0,document,window];function Ut(e){var t;return e=(t=e)>2?g(t):t,Nt[e]||document.querySelector(e)}function Ht(e){return Ut(e)}var Vt={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],samplers:[],transformFeedbacks:[],syncs:[],stringCache:{},stringiCache:{},unpackAlignment:4,recordError:function(e){Vt.lastError||(Vt.lastError=e)},getNewId:function(e){for(var t=Vt.counter++,r=e.length;r<t;r++)e[r]=null;return t},getSource:function(e,t,r,n){for(var a="",i=0;i<t;++i){var o=n?A[n+4*i>>2]:-1;a+=g(A[r+4*i>>2],o<0?void 0:o)}return a},createContext:function(e,t){e.getContextSafariWebGL2Fixed||(e.getContextSafariWebGL2Fixed=e.getContext,e.getContext=function(t,r){var n=e.getContextSafariWebGL2Fixed(t,r);return"webgl"==t==n instanceof WebGLRenderingContext?n:null});var r=t.majorVersion>1?e.getContext("webgl2",t):e.getContext("webgl",t);return r?Vt.registerContext(r,t):0},registerContext:function(e,t){var r=Vt.getNewId(Vt.contexts),n={handle:r,attributes:t,version:t.majorVersion,GLctx:e};return e.canvas&&(e.canvas.GLctxObject=n),Vt.contexts[r]=n,(void 0===t.enableExtensionsByDefault||t.enableExtensionsByDefault)&&Vt.initExtensions(n),r},makeContextCurrent:function(e){return Vt.currentContext=Vt.contexts[e],n.ctx=rr=Vt.currentContext&&Vt.currentContext.GLctx,!(e&&!rr)},getContext:function(e){return Vt.contexts[e]},deleteContext:function(e){Vt.currentContext===Vt.contexts[e]&&(Vt.currentContext=null),"object"==typeof $t&&$t.removeAllHandlersOnTarget(Vt.contexts[e].GLctx.canvas),Vt.contexts[e]&&Vt.contexts[e].GLctx.canvas&&(Vt.contexts[e].GLctx.canvas.GLctxObject=void 0),Vt.contexts[e]=null},initExtensions:function(e){if(e||(e=Vt.currentContext),!e.initExtensionsDone){e.initExtensionsDone=!0;var t,r=e.GLctx;!function(e){var t=e.getExtension("ANGLE_instanced_arrays");t&&(e.vertexAttribDivisor=function(e,r){t.vertexAttribDivisorANGLE(e,r)},e.drawArraysInstanced=function(e,r,n,a){t.drawArraysInstancedANGLE(e,r,n,a)},e.drawElementsInstanced=function(e,r,n,a,i){t.drawElementsInstancedANGLE(e,r,n,a,i)})}(r),function(e){var t=e.getExtension("OES_vertex_array_object");t&&(e.createVertexArray=function(){return t.createVertexArrayOES()},e.deleteVertexArray=function(e){t.deleteVertexArrayOES(e)},e.bindVertexArray=function(e){t.bindVertexArrayOES(e)},e.isVertexArray=function(e){return t.isVertexArrayOES(e)})}(r),function(e){var t=e.getExtension("WEBGL_draw_buffers");t&&(e.drawBuffers=function(e,r){t.drawBuffersWEBGL(e,r)})}(r),(t=r).dibvbi=t.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"),function(e){e.mdibvbi=e.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance")}(r),e.version>=2&&(r.disjointTimerQueryExt=r.getExtension("EXT_disjoint_timer_query_webgl2")),(e.version<2||!r.disjointTimerQueryExt)&&(r.disjointTimerQueryExt=r.getExtension("EXT_disjoint_timer_query")),function(e){e.multiDrawWebgl=e.getExtension("WEBGL_multi_draw")}(r),(r.getSupportedExtensions()||[]).forEach((function(e){e.includes("lose_context")||e.includes("debug")||r.getExtension(e)}))}}};function Wt(e,t,r,n){for(var a=0;a<e;a++){var i=rr[r](),o=i&&Vt.getNewId(n);i?(i.name=o,n[o]=i):Vt.recordError(1282),A[t+4*a>>2]=o}}function zt(e,t,r){if(t){var n,a,i=void 0;switch(e){case 36346:i=1;break;case 36344:return void(0!=r&&1!=r&&Vt.recordError(1280));case 34814:case 36345:i=0;break;case 34466:var o=rr.getParameter(34467);i=o?o.length:0;break;case 33309:if(Vt.currentContext.version<2)return void Vt.recordError(1282);i=2*(rr.getSupportedExtensions()||[]).length;break;case 33307:case 33308:if(Vt.currentContext.version<2)return void Vt.recordError(1280);i=33307==e?3:0}if(void 0===i){var s=rr.getParameter(e);switch(typeof s){case"number":i=s;break;case"boolean":i=s?1:0;break;case"string":return void Vt.recordError(1280);case"object":if(null===s)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:i=0;break;default:return void Vt.recordError(1280)}else{if(s instanceof Float32Array||s instanceof Uint32Array||s instanceof Int32Array||s instanceof Array){for(var l=0;l<s.length;++l)switch(r){case 0:A[t+4*l>>2]=s[l];break;case 2:S[t+4*l>>2]=s[l];break;case 4:x[t+l>>0]=s[l]?1:0}return}try{i=0|s.name}catch(t){return Vt.recordError(1280),void h("GL_INVALID_ENUM in glGet"+r+"v: Unknown object returned from WebGL getParameter("+e+")! (error: "+t+")")}}break;default:return Vt.recordError(1280),void h("GL_INVALID_ENUM in glGet"+r+"v: Native code calling glGet"+r+"v("+e+") and it returns "+s+" of type "+typeof s+"!")}}switch(r){case 1:a=i,k[(n=t)>>2]=a,k[n+4>>2]=(a-k[n>>2])/4294967296;break;case 0:A[t>>2]=i;break;case 2:S[t>>2]=i;break;case 4:x[t>>0]=i?1:0}}else Vt.recordError(1281)}function qt(e){var t=E(e)+1,r=hr(t);return b(e,r,t),r}function Xt(e){return"]"==e.slice(-1)&&e.lastIndexOf("[")}function Jt(e){return 0==(e-=5120)?x:1==e?P:2==e?T:4==e?A:6==e?S:5==e||28922==e||28520==e||30779==e||30782==e?k:I}function Kt(e){return 31-Math.clz32(e.BYTES_PER_ELEMENT)}function Qt(e,t,r,n,a,i){var o=Jt(e),s=Kt(o),l=1<<s,u=function(e){return{5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[e-6402]||1}(t)*l,c=function(e,t,r,n){var a;return t*(e*r+(a=n)-1&-a)}(r,n,u,Vt.unpackAlignment);return o.subarray(a>>s,a+c>>s)}function Yt(e){var t=rr.currentProgram;if(t){var r=t.uniformLocsById[e];return"number"==typeof r&&(t.uniformLocsById[e]=r=rr.getUniformLocation(t,t.uniformArrayNamesById[e]+(r>0?"["+r+"]":""))),r}Vt.recordError(1282)}var Zt=[],er=[];function tr(e){try{return f.grow(e-C.byteLength+65535>>>16),j(f.buffer),1}catch(e){}}var rr,nr=["default","low-power","high-performance"],ar=function(e,t,r,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=se.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n},ir=365,or=146;Object.defineProperties(ar.prototype,{read:{get:function(){return(this.mode&ir)===ir},set:function(e){e?this.mode|=ir:this.mode&=-366}},write:{get:function(){return(this.mode&or)===or},set:function(e){e?this.mode|=or:this.mode&=-147}},isFolder:{get:function(){return se.isDir(this.mode)}},isDevice:{get:function(){return se.isChrdev(this.mode)}}}),se.FSNode=ar,se.staticInit(),we=n.InternalError=ye(Error,"InternalError"),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Ee=e}(),xe=n.BindingError=ye(Error,"BindingError"),$e.prototype.isAliasOf=Ie,$e.prototype.clone=Fe,$e.prototype.delete=Me,$e.prototype.isDeleted=Be,$e.prototype.deleteLater=je,st.prototype.getPointee=Je,st.prototype.destructor=Ke,st.prototype.argPackAdvance=8,st.prototype.readValueFromPointer=fe,st.prototype.deleteObject=Qe,st.prototype.fromWireType=ot,n.getInheritedInstanceCount=Ze,n.getLiveInheritedInstances=et,n.flushPendingDeletes=Oe,n.setDelayFunction=tt,ft=n.UnboundTypeError=ye(Error,"UnboundTypeError"),n.count_emval_handles=xt,n.get_first_emval=Pt;for(var sr=new Float32Array(288),lr=0;lr<288;++lr)Zt[lr]=sr.subarray(0,lr+1);var ur=new Int32Array(288);for(lr=0;lr<288;++lr)er[lr]=ur.subarray(0,lr+1);function cr(e,t,r){var n=r>0?r:E(e)+1,a=new Array(n),i=_(e,a,0,a.length);return t&&(a.length=i),a}var fr={d:function(e,t,r,n){Q("Assertion failed: "+g(e)+", at: "+[t?g(t):"unknown filename",r,n?g(n):"unknown function"])},C:function(e){return hr(e+16)+16},B:function(e,t,r){throw new te(e).init(t,r),e},I:function(e,t,r){le.varargs=r;try{var n=le.getStreamFromFD(e);switch(t){case 0:return(a=le.get())<0?-28:se.open(n.path,n.flags,0,a).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var a=le.get();return n.flags|=a,0;case 5:return a=le.get(),T[a+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return re(28),-1}}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return-e.errno}},ib:function(e,t,r){le.varargs=r;try{var n=le.getStreamFromFD(e);switch(t){case 21509:case 21505:case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:case 21523:case 21524:return n.tty?0:-59;case 21519:if(!n.tty)return-59;var a=le.get();return A[a>>2]=0,0;case 21520:return n.tty?-28:-59;case 21531:return a=le.get(),se.ioctl(n,t,a);default:Q("bad ioctl syscall "+t)}}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return-e.errno}},jb:function(e,t,r){le.varargs=r;try{var n=le.getStr(e),a=r?le.get():0;return se.open(n,t,a).fd}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return-e.errno}},p:function(e){var t=ue[e];delete ue[e];var r=t.rawConstructor,n=t.rawDestructor,a=t.fields;_e([e],a.map((function(e){return e.getterReturnType})).concat(a.map((function(e){return e.setterArgumentType}))),(function(e){var i={};return a.forEach((function(t,r){var n=t.fieldName,o=e[r],s=t.getter,l=t.getterContext,u=e[r+a.length],c=t.setter,f=t.setterContext;i[n]={read:function(e){return o.fromWireType(s(l,e))},write:function(e,t){var r=[];c(f,e,u.toWireType(r,t)),ce(r)}}})),[{name:t.name,fromWireType:function(e){var t={};for(var r in i)t[r]=i[r].read(e);return n(e),t},toWireType:function(e,t){for(var a in i)if(!(a in t))throw new TypeError('Missing field:  "'+a+'"');var o=r();for(a in i)i[a].write(o,t[a]);return null!==e&&e.push(n,o),o},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:n}]}))},eb:function(e,t,r,n,a){},lb:function(e,t,r,n,a){var i=be(r);Te(e,{name:t=Ce(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:a},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=x;else if(2===r)n=T;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=A}return this.fromWireType(n[e>>i])},destructorFunction:null})},e:function(e,t,r,n,a,i,o,s,l,u,c,f,d){c=Ce(c),i=ct(a,i),s&&(s=ct(o,s)),u&&(u=ct(l,u)),d=ct(f,d);var h=pe(c);He(h,(function(){ht("Cannot construct "+c+" due to unbound types",[n])})),_e([e,t,r],n?[n]:[],(function(t){var r,a;t=t[0],a=n?(r=t.registeredClass).instancePrototype:$e.prototype;var o=ve(h,(function(){if(Object.getPrototypeOf(this)!==l)throw new xe("Use 'new' to construct "+c);if(void 0===f.constructor_body)throw new xe(c+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new xe("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),l=Object.create(a,{constructor:{value:o}});o.prototype=l;var f=new Ve(c,o,l,d,r,i,s,u),m=new st(c,f,!0,!1,!1),p=new st(c+"*",f,!1,!1,!1),v=new st(c+" const*",f,!1,!0,!1);return Ne[e]={pointerType:p,constPointerType:v},lt(h,o),[m,p,v]}))},f:function(e,t,r,n,a,i,o){var s=gt(r,n);t=Ce(t),i=ct(a,i),_e([],[e],(function(e){var n=(e=e[0]).name+"."+t;function a(){ht("Cannot call "+n+" due to unbound types",s)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]);var l=e.registeredClass.constructor;return void 0===l[t]?(a.argCount=r-1,l[t]=a):(Ue(l,t,n),l[t].overloadTable[r-1]=a),_e([],s,(function(e){var a=[e[0],null].concat(e.slice(1)),s=wt(n,a,null,i,o);return void 0===l[t].overloadTable?(s.argCount=r-1,l[t]=s):l[t].overloadTable[r-1]=s,[]})),[]}))},r:function(e,t,r,n,a,i){v(t>0);var o=gt(t,r);a=ct(n,a),_e([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new xe("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{ht("Cannot construct "+e.name+" due to unbound types",o)},_e([],o,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=wt(r,n,null,a,i),[]})),[]}))},a:function(e,t,r,n,a,i,o,s){var l=gt(r,n);t=Ce(t),i=ct(a,i),_e([],[e],(function(e){var n=(e=e[0]).name+"."+t;function a(){ht("Cannot call "+n+" due to unbound types",l)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var u=e.registeredClass.instancePrototype,c=u[t];return void 0===c||void 0===c.overloadTable&&c.className!==e.name&&c.argCount===r-2?(a.argCount=r-2,a.className=e.name,u[t]=a):(Ue(u,t,n),u[t].overloadTable[r-2]=a),_e([],l,(function(a){var s=wt(n,a,e,i,o);return void 0===u[t].overloadTable?(s.argCount=r-2,u[t]=s):u[t].overloadTable[r-2]=s,[]})),[]}))},b:function(e,t,r,n,a,i,o,s,l,u){t=Ce(t),a=ct(n,a),_e([],[e],(function(e){var n=(e=e[0]).name+"."+t,c={get:function(){ht("Cannot access "+n+" due to unbound types",[r,o])},enumerable:!0,configurable:!0};return c.set=l?()=>{ht("Cannot access "+n+" due to unbound types",[r,o])}:e=>{Pe(n+" is a read-only property")},Object.defineProperty(e.registeredClass.instancePrototype,t,c),_e([],l?[r,o]:[r],(function(r){var o=r[0],c={get:function(){var t=_t(this,e,n+" getter");return o.fromWireType(a(i,t))},enumerable:!0};if(l){l=ct(s,l);var f=r[1];c.set=function(t){var r=_t(this,e,n+" setter"),a=[];l(u,r,f.toWireType(a,t)),ce(a)}}return Object.defineProperty(e.registeredClass.instancePrototype,t,c),[]})),[]}))},kb:function(e,t){Te(e,{name:t=Ce(t),fromWireType:function(e){var t=Tt.toValue(e);return Ct(e),t},toWireType:function(e,t){return Tt.toHandle(t)},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:null})},z:function(e,t,r,n){var a=be(r);function i(){}t=Ce(t),i.values={},Te(e,{name:t,constructor:i,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,t){return t.value},argPackAdvance:8,readValueFromPointer:It(t,a,n),destructorFunction:null}),He(t,i)},y:function(e,t,r){var n=At(e,"enum");t=Ce(t);var a=n.constructor,i=Object.create(n.constructor.prototype,{value:{value:r},constructor:{value:ve(n.name+"_"+t,(function(){}))}});a.values[r]=i,a[t]=i},K:function(e,t,r){var n=be(r);Te(e,{name:t=Ce(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:St(t,n),destructorFunction:null})},E:function(e,t,r,n,a,i){var o=gt(t,r);e=Ce(e),a=ct(n,a),He(e,(function(){ht("Cannot call "+e+" due to unbound types",o)}),t-1),_e([],o,(function(r){var n=[r[0],null].concat(r.slice(1));return lt(e,wt(e,n,null,a,i),t-1),[]}))},m:function(e,t,r,n,a){t=Ce(t);var i=be(r),o=e=>e;if(0===n){var s=32-8*r;o=e=>e<<s>>>s}var l=t.includes("unsigned");Te(e,{name:t,fromWireType:o,toWireType:l?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:Lt(t,i,0!==n),destructorFunction:null})},g:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function a(e){var t=k,r=t[e>>=2],a=t[e+1];return new n(C,a,r)}Te(e,{name:r=Ce(r),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},j:function(e,t,r,n,a,i,o,s,l,u,c,f){r=Ce(r),i=ct(a,i),s=ct(o,s),u=ct(l,u),f=ct(c,f),_e([e],[t],(function(e){return e=e[0],[new st(r,e.registeredClass,!1,!1,!0,e,n,i,s,u,f)]}))},J:function(e,t){var r="std::string"===(t=Ce(t));Te(e,{name:t,fromWireType:function(e){var t,n=k[e>>2];if(r)for(var a=e+4,i=0;i<=n;++i){var o=e+4+i;if(i==n||0==P[o]){var s=g(a,o-a);void 0===t?t=s:(t+=String.fromCharCode(0),t+=s),a=o+1}}else{var l=new Array(n);for(i=0;i<n;++i)l[i]=String.fromCharCode(P[e+4+i]);t=l.join("")}return dr(e),t},toWireType:function(e,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Pe("Cannot pass non-string to std::string");var a=(r&&n?()=>E(t):()=>t.length)(),i=hr(4+a+1);if(k[i>>2]=a,r&&n)b(t,i+4,a+1);else if(n)for(var o=0;o<a;++o){var s=t.charCodeAt(o);s>255&&(dr(i),Pe("String has UTF-16 code units that do not fit in 8 bits")),P[i+4+o]=s}else for(o=0;o<a;++o)P[i+4+o]=t[o];return null!==e&&e.push(dr,i),i},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:function(e){dr(e)}})},A:function(e,t,r){var n,a,i,o,s;r=Ce(r),2===t?(n=F,a=M,o=B,i=()=>I,s=1):4===t&&(n=R,a=G,o=O,i=()=>k,s=2),Te(e,{name:r,fromWireType:function(e){for(var r,a=k[e>>2],o=i(),l=e+4,u=0;u<=a;++u){var c=e+4+u*t;if(u==a||0==o[c>>s]){var f=n(l,c-l);void 0===r?r=f:(r+=String.fromCharCode(0),r+=f),l=c+t}}return dr(e),r},toWireType:function(e,n){"string"!=typeof n&&Pe("Cannot pass non-string to C++ string type "+r);var i=o(n),l=hr(4+i+t);return k[l>>2]=i>>s,a(n,l+4,i+t),null!==e&&e.push(dr,l),l},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:function(e){dr(e)}})},q:function(e,t,r,n,a,i){ue[e]={name:Ce(t),rawConstructor:ct(r,n),rawDestructor:ct(a,i),fields:[]}},n:function(e,t,r,n,a,i,o,s,l,u){ue[e].fields.push({fieldName:Ce(t),getterReturnType:r,getter:ct(n,a),getterContext:i,setterArgumentType:o,setter:ct(s,l),setterContext:u})},mb:function(e,t){Te(e,{isVoid:!0,name:t=Ce(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},x:function(e,t,r){e=Tt.toValue(e),t=At(t,"emval::as");var n=[],a=Tt.toHandle(n);return A[r>>2]=a,t.toWireType(n,e)},Ja:function(e){return yt.handleAsync((function(){return(e=Tt.toValue(e)).then(Tt.toHandle)}))},Nb:function(e,t,r,n){e=Tt.toValue(e);for(var a=Dt(t,r),i=new Array(t),o=0;o<t;++o){var s=a[o];i[o]=s.readValueFromPointer(n),n+=s.argPackAdvance}var l=e.apply(void 0,i);return Tt.toHandle(l)},h:function(e,t,r,n,a){return(e=Bt[e])(t=Tt.toValue(t),r=Mt(r),function(e){var t=[];return A[e>>2]=Tt.toHandle(t),t}(n),a)},l:function(e,t,r,n){(e=Bt[e])(t=Tt.toValue(t),r=Mt(r),null,n)},w:Ct,ob:function(e){return 0===e?Tt.toHandle(Rt()):(e=Mt(e),Tt.toHandle(Rt()[e]))},c:function(e,t){var r=Dt(e,t),n=r[0],a=n.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",i=Ot[a];if(void 0!==i)return i;for(var o=["retType"],s=[n],l="",u=0;u<e-1;++u)l+=(0!==u?", ":"")+"arg"+u,o.push("argType"+u),s.push(r[1+u]);var c="return function "+pe("methodCaller_"+a)+"(handle, name, destructors, args) {\n",f=0;for(u=0;u<e-1;++u)c+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(f?"+"+f:"")+");\n",f+=r[u+1].argPackAdvance;for(c+="    var rv = handle[name]("+l+");\n",u=0;u<e-1;++u)r[u+1].deleteObject&&(c+="    argType"+u+".deleteObject(arg"+u+");\n");n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",o.push(c);var d,h,m=mt(Function,o).apply(null,s);return d=m,h=Bt.length,Bt.push(d),i=h,Ot[a]=i,i},$a:function(e){return e=Mt(e),Tt.toHandle(n[e])},L:function(e,t){return e=Tt.toValue(e),t=Tt.toValue(t),Tt.toHandle(e[t])},v:function(e){e>4&&(Et[e].refcount+=1)},nb:function(e,t){return(e=Tt.toValue(e))instanceof(t=Tt.toValue(t))},ab:function(e,t,r,a){e=Tt.toValue(e);var i=jt[t];return i||(i=function(e){for(var t="",r=0;r<e;++r)t+=(0!==r?", ":"")+"arg"+r;var a="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(r=0;r<e;++r)a+="var argType"+r+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+r+'], "parameter '+r+'");\nvar arg'+r+" = argType"+r+".readValueFromPointer(args);\nargs += argType"+r+"['argPackAdvance'];\n";return a+="var obj = new constructor("+t+");\nreturn valueToHandle(obj);\n}\n",new Function("requireRegisteredType","Module","valueToHandle",a)(At,n,Tt.toHandle)}(t),jt[t]=i),i(e,r,a)},cb:function(){return Tt.toHandle([])},_a:function(e){return Tt.toHandle(Mt(e))},bb:function(){return Tt.toHandle({})},W:function(e){ce(Tt.toValue(e)),Ct(e)},u:function(e,t,r){e=Tt.toValue(e),t=Tt.toValue(t),r=Tt.toValue(r),e[t]=r},k:function(e,t){var r=(e=At(e,"_emval_take_value")).readValueFromPointer(t);return Tt.toHandle(r)},t:function(){Q("")},fb:function(e,t){var r;if(0===e)r=Date.now();else{if(1!==e&&4!==e)return re(28),-1;r=Gt()}return A[t>>2]=r/1e3|0,A[t+4>>2]=r%1e3*1e3*1e3|0,0},F:function(e,t,r){var n=Ht(e);if(!n)return-4;A[t>>2]=n.width,A[r>>2]=n.height},Xa:function(e){rr.activeTexture(e)},Wa:function(e,t){rr.attachShader(Vt.programs[e],Vt.shaders[t])},Ua:function(e,t,r){rr.bindAttribLocation(Vt.programs[e],t,g(r))},Ta:function(e,t){35051==e?rr.currentPixelPackBufferBinding=t:35052==e&&(rr.currentPixelUnpackBufferBinding=t),rr.bindBuffer(e,Vt.buffers[t])},Sa:function(e,t){rr.bindFramebuffer(e,Vt.framebuffers[t])},Ra:function(e,t){rr.bindRenderbuffer(e,Vt.renderbuffers[t])},Qa:function(e,t){rr.bindTexture(e,Vt.textures[t])},zb:function(e){rr.bindVertexArray(Vt.vaos[e])},wb:function(e){rr.bindVertexArray(Vt.vaos[e])},Pa:function(e,t,r,n){rr.blendColor(e,t,r,n)},Oa:function(e){rr.blendEquation(e)},Ab:function(e,t){rr.blendEquationSeparate(e,t)},Na:function(e,t){rr.blendFunc(e,t)},rb:function(e,t,r,n,a,i,o,s,l,u){rr.blitFramebuffer(e,t,r,n,a,i,o,s,l,u)},Ma:function(e,t,r,n){Vt.currentContext.version>=2?r?rr.bufferData(e,P,n,r,t):rr.bufferData(e,t,n):rr.bufferData(e,r?P.subarray(r,r+t):t,n)},La:function(e){return rr.checkFramebufferStatus(e)},Ka:function(e){rr.clear(e)},Ia:function(e,t,r,n){rr.clearColor(e,t,r,n)},Ha:function(e){rr.clearStencil(e)},Ga:function(e,t,r,n){rr.colorMask(!!e,!!t,!!r,!!n)},Fa:function(e){rr.compileShader(Vt.shaders[e])},Ea:function(e,t,r,n,a,i,o,s){rr.copyTexSubImage2D(e,t,r,n,a,i,o,s)},Da:function(){var e=Vt.getNewId(Vt.programs),t=rr.createProgram();return t.name=e,t.maxUniformLength=t.maxAttributeLength=t.maxUniformBlockNameLength=0,t.uniformIdCounter=1,Vt.programs[e]=t,e},Ca:function(e){var t=Vt.getNewId(Vt.shaders);return Vt.shaders[t]=rr.createShader(e),t},Ba:function(e,t){for(var r=0;r<e;r++){var n=A[t+4*r>>2],a=Vt.buffers[n];a&&(rr.deleteBuffer(a),a.name=0,Vt.buffers[n]=null,n==rr.currentPixelPackBufferBinding&&(rr.currentPixelPackBufferBinding=0),n==rr.currentPixelUnpackBufferBinding&&(rr.currentPixelUnpackBufferBinding=0))}},Aa:function(e,t){for(var r=0;r<e;++r){var n=A[t+4*r>>2],a=Vt.framebuffers[n];a&&(rr.deleteFramebuffer(a),a.name=0,Vt.framebuffers[n]=null)}},za:function(e){if(e){var t=Vt.programs[e];t?(rr.deleteProgram(t),t.name=0,Vt.programs[e]=null):Vt.recordError(1281)}},ya:function(e,t){for(var r=0;r<e;r++){var n=A[t+4*r>>2],a=Vt.renderbuffers[n];a&&(rr.deleteRenderbuffer(a),a.name=0,Vt.renderbuffers[n]=null)}},xa:function(e){if(e){var t=Vt.shaders[e];t?(rr.deleteShader(t),Vt.shaders[e]=null):Vt.recordError(1281)}},sb:function(e){if(e){var t=Vt.syncs[e];t?(rr.deleteSync(t),t.name=0,Vt.syncs[e]=null):Vt.recordError(1281)}},wa:function(e,t){for(var r=0;r<e;r++){var n=A[t+4*r>>2],a=Vt.textures[n];a&&(rr.deleteTexture(a),a.name=0,Vt.textures[n]=null)}},yb:function(e,t){for(var r=0;r<e;r++){var n=A[t+4*r>>2];rr.deleteVertexArray(Vt.vaos[n]),Vt.vaos[n]=null}},vb:function(e,t){for(var r=0;r<e;r++){var n=A[t+4*r>>2];rr.deleteVertexArray(Vt.vaos[n]),Vt.vaos[n]=null}},va:function(e){rr.depthMask(!!e)},ua:function(e){rr.disable(e)},ta:function(e){rr.disableVertexAttribArray(e)},sa:function(e,t,r){rr.drawArrays(e,t,r)},ra:function(e,t,r,n){rr.drawElements(e,t,r,n)},qa:function(e){rr.enable(e)},pa:function(e){rr.enableVertexAttribArray(e)},tb:function(e,t){var r=rr.fenceSync(e,t);if(r){var n=Vt.getNewId(Vt.syncs);return r.name=n,Vt.syncs[n]=r,n}return 0},oa:function(){rr.finish()},na:function(){rr.flush()},ma:function(e,t,r,n){rr.framebufferRenderbuffer(e,t,r,Vt.renderbuffers[n])},la:function(e,t,r,n,a){rr.framebufferTexture2D(e,t,r,Vt.textures[n],a)},ka:function(e,t){Wt(e,t,"createBuffer",Vt.buffers)},ja:function(e,t){Wt(e,t,"createFramebuffer",Vt.framebuffers)},ia:function(e,t){Wt(e,t,"createRenderbuffer",Vt.renderbuffers)},ha:function(e,t){Wt(e,t,"createTexture",Vt.textures)},xb:function(e,t){Wt(e,t,"createVertexArray",Vt.vaos)},ub:function(e,t){Wt(e,t,"createVertexArray",Vt.vaos)},Bb:function(e,t){return rr.getAttribLocation(Vt.programs[e],g(t))},ga:function(e,t,r){r?A[r>>2]=rr.getBufferParameter(e,t):Vt.recordError(1281)},fa:function(){var e=rr.getError()||Vt.lastError;return Vt.lastError=0,e},ea:function(e,t,r,n){var a=rr.getFramebufferAttachmentParameter(e,t,r);(a instanceof WebGLRenderbuffer||a instanceof WebGLTexture)&&(a=0|a.name),A[n>>2]=a},da:function(e,t){zt(e,t,0)},ca:function(e,t,r,n){var a=rr.getProgramInfoLog(Vt.programs[e]);null===a&&(a="(unknown error)");var i=t>0&&n?b(a,n,t):0;r&&(A[r>>2]=i)},ba:function(e,t,r){if(r)if(e>=Vt.counter)Vt.recordError(1281);else if(e=Vt.programs[e],35716==t){var n=rr.getProgramInfoLog(e);null===n&&(n="(unknown error)"),A[r>>2]=n.length+1}else if(35719==t){if(!e.maxUniformLength)for(var a=0;a<rr.getProgramParameter(e,35718);++a)e.maxUniformLength=Math.max(e.maxUniformLength,rr.getActiveUniform(e,a).name.length+1);A[r>>2]=e.maxUniformLength}else if(35722==t){if(!e.maxAttributeLength)for(a=0;a<rr.getProgramParameter(e,35721);++a)e.maxAttributeLength=Math.max(e.maxAttributeLength,rr.getActiveAttrib(e,a).name.length+1);A[r>>2]=e.maxAttributeLength}else if(35381==t){if(!e.maxUniformBlockNameLength)for(a=0;a<rr.getProgramParameter(e,35382);++a)e.maxUniformBlockNameLength=Math.max(e.maxUniformBlockNameLength,rr.getActiveUniformBlockName(e,a).length+1);A[r>>2]=e.maxUniformBlockNameLength}else A[r>>2]=rr.getProgramParameter(e,t);else Vt.recordError(1281)},aa:function(e,t,r){r?A[r>>2]=rr.getRenderbufferParameter(e,t):Vt.recordError(1281)},$:function(e,t,r,n){var a=rr.getShaderInfoLog(Vt.shaders[e]);null===a&&(a="(unknown error)");var i=t>0&&n?b(a,n,t):0;r&&(A[r>>2]=i)},_:function(e,t,r,n){var a=rr.getShaderPrecisionFormat(e,t);A[r>>2]=a.rangeMin,A[r+4>>2]=a.rangeMax,A[n>>2]=a.precision},Z:function(e,t,r){if(r)if(35716==t){var n=rr.getShaderInfoLog(Vt.shaders[e]);null===n&&(n="(unknown error)");var a=n?n.length+1:0;A[r>>2]=a}else if(35720==t){var i=rr.getShaderSource(Vt.shaders[e]),o=i?i.length+1:0;A[r>>2]=o}else A[r>>2]=rr.getShaderParameter(Vt.shaders[e],t);else Vt.recordError(1281)},Y:function(e){var t=Vt.stringCache[e];if(!t){switch(e){case 7939:var r=rr.getSupportedExtensions()||[];t=qt((r=r.concat(r.map((function(e){return"GL_"+e})))).join(" "));break;case 7936:case 7937:case 37445:case 37446:var n=rr.getParameter(e);n||Vt.recordError(1280),t=n&&qt(n);break;case 7938:var a=rr.getParameter(7938);t=qt(a=Vt.currentContext.version>=2?"OpenGL ES 3.0 ("+a+")":"OpenGL ES 2.0 ("+a+")");break;case 35724:var i=rr.getParameter(35724),o=i.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==o&&(3==o[1].length&&(o[1]=o[1]+"0"),i="OpenGL ES GLSL ES "+o[1]+" ("+i+")"),t=qt(i);break;default:Vt.recordError(1280)}Vt.stringCache[e]=t}return t},X:function(e,t){if(Vt.currentContext.version<2)return Vt.recordError(1282),0;var r=Vt.stringiCache[e];if(r)return t<0||t>=r.length?(Vt.recordError(1281),0):r[t];if(7939===e){var n=rr.getSupportedExtensions()||[];return n=(n=n.concat(n.map((function(e){return"GL_"+e})))).map((function(e){return qt(e)})),r=Vt.stringiCache[e]=n,t<0||t>=r.length?(Vt.recordError(1281),0):r[t]}return Vt.recordError(1280),0},V:function(e,t){if(t=g(t),e=Vt.programs[e]){!function(e){var t,r,n=e.uniformLocsById,a=e.uniformSizeAndIdsByName;if(!n)for(e.uniformLocsById=n={},e.uniformArrayNamesById={},t=0;t<rr.getProgramParameter(e,35718);++t){var i=rr.getActiveUniform(e,t),o=i.name,s=i.size,l=Xt(o),u=l>0?o.slice(0,l):o,c=e.uniformIdCounter;for(e.uniformIdCounter+=s,a[u]=[s,c],r=0;r<s;++r)n[c]=r,e.uniformArrayNamesById[c++]=u}}(e);var r=e.uniformLocsById,n=0,a=t,i=Xt(t);i>0&&(s=t.slice(i+1),n=parseInt(s)>>>0,a=t.slice(0,i));var o=e.uniformSizeAndIdsByName[a];if(o&&n<o[0]&&(r[n+=o[1]]=r[n]||rr.getUniformLocation(e,t)))return n}else Vt.recordError(1281);var s;return-1},Va:function(e){return rr.isEnabled(e)},U:function(e){var t=Vt.textures[e];return t?rr.isTexture(t):0},T:function(e){rr.lineWidth(e)},S:function(e){e=Vt.programs[e],rr.linkProgram(e),e.uniformLocsById=0,e.uniformSizeAndIdsByName={}},R:function(e,t){3317==e&&(Vt.unpackAlignment=t),rr.pixelStorei(e,t)},Q:function(e,t,r,n,a,i,o){if(Vt.currentContext.version>=2)if(rr.currentPixelPackBufferBinding)rr.readPixels(e,t,r,n,a,i,o);else{var s=Jt(i);rr.readPixels(e,t,r,n,a,i,s,o>>Kt(s))}else{var l=Qt(i,a,r,n,o);l?rr.readPixels(e,t,r,n,a,i,l):Vt.recordError(1280)}},P:function(e,t,r,n){rr.renderbufferStorage(e,t,r,n)},qb:function(e,t,r,n,a){rr.renderbufferStorageMultisample(e,t,r,n,a)},O:function(e,t,r,n){rr.scissor(e,t,r,n)},N:function(e,t,r,n){var a=Vt.getSource(e,t,r,n);rr.shaderSource(Vt.shaders[e],a)},M:function(e,t,r,n,a,i,o,s,l){if(Vt.currentContext.version>=2)if(rr.currentPixelUnpackBufferBinding)rr.texImage2D(e,t,r,n,a,i,o,s,l);else if(l){var u=Jt(s);rr.texImage2D(e,t,r,n,a,i,o,s,u,l>>Kt(u))}else rr.texImage2D(e,t,r,n,a,i,o,s,null);else rr.texImage2D(e,t,r,n,a,i,o,s,l?Qt(s,o,n,a,l):null)},fc:function(e,t,r){rr.texParameterf(e,t,r)},ec:function(e,t,r){var n=S[r>>2];rr.texParameterf(e,t,n)},dc:function(e,t,r){rr.texParameteri(e,t,r)},cc:function(e,t,r){var n=A[r>>2];rr.texParameteri(e,t,n)},bc:function(e,t,r,n,a,i,o,s,l){if(Vt.currentContext.version>=2)if(rr.currentPixelUnpackBufferBinding)rr.texSubImage2D(e,t,r,n,a,i,o,s,l);else if(l){var u=Jt(s);rr.texSubImage2D(e,t,r,n,a,i,o,s,u,l>>Kt(u))}else rr.texSubImage2D(e,t,r,n,a,i,o,s,null);else{var c=null;l&&(c=Qt(s,o,a,i,l)),rr.texSubImage2D(e,t,r,n,a,i,o,s,c)}},ac:function(e,t){rr.uniform1f(Yt(e),t)},$b:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform1fv(Yt(e),S,r>>2,t);else{if(t<=288)for(var n=Zt[t-1],a=0;a<t;++a)n[a]=S[r+4*a>>2];else n=S.subarray(r>>2,r+4*t>>2);rr.uniform1fv(Yt(e),n)}},_b:function(e,t){rr.uniform1i(Yt(e),t)},Zb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform1iv(Yt(e),A,r>>2,t);else{if(t<=288)for(var n=er[t-1],a=0;a<t;++a)n[a]=A[r+4*a>>2];else n=A.subarray(r>>2,r+4*t>>2);rr.uniform1iv(Yt(e),n)}},Yb:function(e,t,r){rr.uniform2f(Yt(e),t,r)},Xb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform2fv(Yt(e),S,r>>2,2*t);else{if(t<=144)for(var n=Zt[2*t-1],a=0;a<2*t;a+=2)n[a]=S[r+4*a>>2],n[a+1]=S[r+(4*a+4)>>2];else n=S.subarray(r>>2,r+8*t>>2);rr.uniform2fv(Yt(e),n)}},Wb:function(e,t,r){rr.uniform2i(Yt(e),t,r)},Vb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform2iv(Yt(e),A,r>>2,2*t);else{if(t<=144)for(var n=er[2*t-1],a=0;a<2*t;a+=2)n[a]=A[r+4*a>>2],n[a+1]=A[r+(4*a+4)>>2];else n=A.subarray(r>>2,r+8*t>>2);rr.uniform2iv(Yt(e),n)}},Ub:function(e,t,r,n){rr.uniform3f(Yt(e),t,r,n)},Tb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform3fv(Yt(e),S,r>>2,3*t);else{if(t<=96)for(var n=Zt[3*t-1],a=0;a<3*t;a+=3)n[a]=S[r+4*a>>2],n[a+1]=S[r+(4*a+4)>>2],n[a+2]=S[r+(4*a+8)>>2];else n=S.subarray(r>>2,r+12*t>>2);rr.uniform3fv(Yt(e),n)}},Sb:function(e,t,r,n){rr.uniform3i(Yt(e),t,r,n)},Rb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform3iv(Yt(e),A,r>>2,3*t);else{if(t<=96)for(var n=er[3*t-1],a=0;a<3*t;a+=3)n[a]=A[r+4*a>>2],n[a+1]=A[r+(4*a+4)>>2],n[a+2]=A[r+(4*a+8)>>2];else n=A.subarray(r>>2,r+12*t>>2);rr.uniform3iv(Yt(e),n)}},Qb:function(e,t,r,n,a){rr.uniform4f(Yt(e),t,r,n,a)},Pb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform4fv(Yt(e),S,r>>2,4*t);else{if(t<=72){var n=Zt[4*t-1],a=S;r>>=2;for(var i=0;i<4*t;i+=4){var o=r+i;n[i]=a[o],n[i+1]=a[o+1],n[i+2]=a[o+2],n[i+3]=a[o+3]}}else n=S.subarray(r>>2,r+16*t>>2);rr.uniform4fv(Yt(e),n)}},Ob:function(e,t,r,n,a){rr.uniform4i(Yt(e),t,r,n,a)},Mb:function(e,t,r){if(Vt.currentContext.version>=2)rr.uniform4iv(Yt(e),A,r>>2,4*t);else{if(t<=72)for(var n=er[4*t-1],a=0;a<4*t;a+=4)n[a]=A[r+4*a>>2],n[a+1]=A[r+(4*a+4)>>2],n[a+2]=A[r+(4*a+8)>>2],n[a+3]=A[r+(4*a+12)>>2];else n=A.subarray(r>>2,r+16*t>>2);rr.uniform4iv(Yt(e),n)}},Lb:function(e,t,r,n){if(Vt.currentContext.version>=2)rr.uniformMatrix2fv(Yt(e),!!r,S,n>>2,4*t);else{if(t<=72)for(var a=Zt[4*t-1],i=0;i<4*t;i+=4)a[i]=S[n+4*i>>2],a[i+1]=S[n+(4*i+4)>>2],a[i+2]=S[n+(4*i+8)>>2],a[i+3]=S[n+(4*i+12)>>2];else a=S.subarray(n>>2,n+16*t>>2);rr.uniformMatrix2fv(Yt(e),!!r,a)}},Kb:function(e,t,r,n){if(Vt.currentContext.version>=2)rr.uniformMatrix3fv(Yt(e),!!r,S,n>>2,9*t);else{if(t<=32)for(var a=Zt[9*t-1],i=0;i<9*t;i+=9)a[i]=S[n+4*i>>2],a[i+1]=S[n+(4*i+4)>>2],a[i+2]=S[n+(4*i+8)>>2],a[i+3]=S[n+(4*i+12)>>2],a[i+4]=S[n+(4*i+16)>>2],a[i+5]=S[n+(4*i+20)>>2],a[i+6]=S[n+(4*i+24)>>2],a[i+7]=S[n+(4*i+28)>>2],a[i+8]=S[n+(4*i+32)>>2];else a=S.subarray(n>>2,n+36*t>>2);rr.uniformMatrix3fv(Yt(e),!!r,a)}},Jb:function(e,t,r,n){if(Vt.currentContext.version>=2)rr.uniformMatrix4fv(Yt(e),!!r,S,n>>2,16*t);else{if(t<=18){var a=Zt[16*t-1],i=S;n>>=2;for(var o=0;o<16*t;o+=16){var s=n+o;a[o]=i[s],a[o+1]=i[s+1],a[o+2]=i[s+2],a[o+3]=i[s+3],a[o+4]=i[s+4],a[o+5]=i[s+5],a[o+6]=i[s+6],a[o+7]=i[s+7],a[o+8]=i[s+8],a[o+9]=i[s+9],a[o+10]=i[s+10],a[o+11]=i[s+11],a[o+12]=i[s+12],a[o+13]=i[s+13],a[o+14]=i[s+14],a[o+15]=i[s+15]}}else a=S.subarray(n>>2,n+64*t>>2);rr.uniformMatrix4fv(Yt(e),!!r,a)}},Ib:function(e){e=Vt.programs[e],rr.useProgram(e),rr.currentProgram=e},Hb:function(e,t){rr.vertexAttrib1f(e,t)},Gb:function(e,t){rr.vertexAttrib2f(e,S[t>>2],S[t+4>>2])},Fb:function(e,t){rr.vertexAttrib3f(e,S[t>>2],S[t+4>>2],S[t+8>>2])},Eb:function(e,t){rr.vertexAttrib4f(e,S[t>>2],S[t+4>>2],S[t+8>>2],S[t+12>>2])},Db:function(e,t,r,n,a,i){rr.vertexAttribPointer(e,t,r,!!n,a,i)},Cb:function(e,t,r,n){rr.viewport(e,t,r,n)},pb:function(e,t,r,n){rr.waitSync(Vt.syncs[e],t,(r>>>0)+4294967296*n)},gb:function(e){var t,r,n=P.length,a=2147483648;if((e>>>=0)>a)return!1;for(var i=1;i<=4;i*=2){var o=n*(1+.2/i);if(o=Math.min(o,e+100663296),tr(Math.min(a,((t=Math.max(e,o))%(r=65536)>0&&(t+=r-t%r),t))))return!0}return!1},Ya:function(e,t){return function(e,t){var r=t>>2,n=A[r+6],a={alpha:!!A[r+0],depth:!!A[r+1],stencil:!!A[r+2],antialias:!!A[r+3],premultipliedAlpha:!!A[r+4],preserveDrawingBuffer:!!A[r+5],powerPreference:nr[n],failIfMajorPerformanceCaveat:!!A[r+7],majorVersion:A[r+8],minorVersion:A[r+9],enableExtensionsByDefault:A[r+10],explicitSwapControl:A[r+11],proxyContextToMainThread:A[r+12],renderViaOffscreenBackBuffer:A[r+13]},i=Ht(e);return i?a.explicitSwapControl?0:Vt.createContext(i,a):0}(e,t)},D:function(e){Vt.currentContext==e&&(Vt.currentContext=0),Vt.deleteContext(e)},o:function(){return Vt.currentContext?Vt.currentContext.handle:0},Za:function(e){for(var t=e>>2,r=0;r<14;++r)A[t+r]=0;A[t+0]=A[t+1]=A[t+3]=A[t+4]=A[t+8]=A[t+10]=1},i:function(e){return Vt.makeContextCurrent(e)?0:-5},G:function(e){try{var t=le.getStreamFromFD(e);return se.close(t),0}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return e.errno}},hb:function(e,t,r,n){try{var a=le.getStreamFromFD(e),i=le.doReadv(a,t,r);return A[n>>2]=i,0}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return e.errno}},db:function(e,t,r,n,a){try{var i=le.getStreamFromFD(e),o=4294967296*r+(t>>>0),s=9007199254740992;return o<=-s||o>=s?-61:(se.llseek(i,o,n),H=[i.position>>>0,(U=i.position,+Math.abs(U)>=1?U>0?(0|Math.min(+Math.floor(U/4294967296),4294967295))>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],A[a>>2]=H[0],A[a+4>>2]=H[1],i.getdents&&0===o&&0===n&&(i.getdents=null),0)}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return e.errno}},H:function(e,t,r,n){try{var a=le.getStreamFromFD(e),i=le.doWritev(a,t,r);return A[n>>2]=i,0}catch(e){if(void 0===se||!(e instanceof se.ErrnoError))throw e;return e.errno}},s:function(e){}};!function(){var e={a:fr};function t(e,t){var r,a=e.exports;a=yt.instrumentWasmExports(a),n.asm=a,j((f=n.asm.gc).buffer),n.asm.ic,r=n.asm.hc,W.unshift(r),K()}function a(e){t(e.instance)}function i(t){return(c||"function"!=typeof fetch?Promise.resolve().then((function(){return Z($)})):fetch($,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+$+"'";return e.arrayBuffer()})).catch((function(){return Z($)}))).then((function(t){return WebAssembly.instantiate(t,e)})).then((function(e){return e})).then(t,(function(e){h("failed to asynchronously prepare wasm: "+e),Q(e)}))}if(J(),n.instantiateWasm)try{var o=n.instantiateWasm(e,t);return o=yt.instrumentWasmExports(o)}catch(e){return h("Module.instantiateWasm callback failed with error: "+e),!1}(c||"function"!=typeof WebAssembly.instantiateStreaming||Y($)||"function"!=typeof fetch?i(a):fetch($,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(a,(function(e){return h("wasm streaming compile failed: "+e),h("falling back to ArrayBuffer instantiation"),i(a)}))}))).catch(r)}(),n.___wasm_call_ctors=function(){return(n.___wasm_call_ctors=n.asm.hc).apply(null,arguments)};var dr=n._free=function(){return(dr=n._free=n.asm.jc).apply(null,arguments)},hr=n._malloc=function(){return(hr=n._malloc=n.asm.kc).apply(null,arguments)},mr=n.___getTypeName=function(){return(mr=n.___getTypeName=n.asm.lc).apply(null,arguments)};n.___embind_register_native_and_builtin_types=function(){return(n.___embind_register_native_and_builtin_types=n.asm.mc).apply(null,arguments)};var pr=n.___errno_location=function(){return(pr=n.___errno_location=n.asm.nc).apply(null,arguments)};n.dynCall_ii=function(){return(n.dynCall_ii=n.asm.oc).apply(null,arguments)};var vr=n.dynCall_vi=function(){return(vr=n.dynCall_vi=n.asm.pc).apply(null,arguments)};n.dynCall_ji=function(){return(n.dynCall_ji=n.asm.qc).apply(null,arguments)},n.dynCall_iij=function(){return(n.dynCall_iij=n.asm.rc).apply(null,arguments)},n.dynCall_viiij=function(){return(n.dynCall_viiij=n.asm.sc).apply(null,arguments)},n.dynCall_vii=function(){return(n.dynCall_vii=n.asm.tc).apply(null,arguments)},n.dynCall_viij=function(){return(n.dynCall_viij=n.asm.uc).apply(null,arguments)},n.dynCall_fij=function(){return(n.dynCall_fij=n.asm.vc).apply(null,arguments)},n.dynCall_viiii=function(){return(n.dynCall_viiii=n.asm.wc).apply(null,arguments)},n.dynCall_iii=function(){return(n.dynCall_iii=n.asm.xc).apply(null,arguments)},n.dynCall_fii=function(){return(n.dynCall_fii=n.asm.yc).apply(null,arguments)},n.dynCall_viii=function(){return(n.dynCall_viii=n.asm.zc).apply(null,arguments)};var yr,wr=n.dynCall_v=function(){return(wr=n.dynCall_v=n.asm.Ac).apply(null,arguments)};function gr(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function _r(e){function r(){yr||(yr=!0,n.calledRun=!0,p||(n.noFSInit||se.init.initialized||se.init(),se.ignorePermissions=!1,ee(W),t(n),n.onRuntimeInitialized&&n.onRuntimeInitialized(),function(){if(n.postRun)for("function"==typeof n.postRun&&(n.postRun=[n.postRun]);n.postRun.length;)e=n.postRun.shift(),z.unshift(e);var e;ee(z)}()))}q>0||(function(){if(n.preRun)for("function"==typeof n.preRun&&(n.preRun=[n.preRun]);n.preRun.length;)e=n.preRun.shift(),V.unshift(e);var e;ee(V)}(),q>0||(n.setStatus?(n.setStatus("Running..."),setTimeout((function(){setTimeout((function(){n.setStatus("")}),1),r()}),1)):r()))}if(n.dynCall_fif=function(){return(n.dynCall_fif=n.asm.Bc).apply(null,arguments)},n.dynCall_vij=function(){return(n.dynCall_vij=n.asm.Cc).apply(null,arguments)},n.dynCall_vijiii=function(){return(n.dynCall_vijiii=n.asm.Dc).apply(null,arguments)},n.dynCall_viiiii=function(){return(n.dynCall_viiiii=n.asm.Ec).apply(null,arguments)},n.dynCall_iiiiii=function(){return(n.dynCall_iiiiii=n.asm.Fc).apply(null,arguments)},n.dynCall_iiii=function(){return(n.dynCall_iiii=n.asm.Gc).apply(null,arguments)},n.dynCall_iiiii=function(){return(n.dynCall_iiiii=n.asm.Hc).apply(null,arguments)},n.dynCall_iiiiiiii=function(){return(n.dynCall_iiiiiiii=n.asm.Ic).apply(null,arguments)},n.dynCall_iiiff=function(){return(n.dynCall_iiiff=n.asm.Jc).apply(null,arguments)},n.dynCall_viiiiii=function(){return(n.dynCall_viiiiii=n.asm.Kc).apply(null,arguments)},n.dynCall_viif=function(){return(n.dynCall_viif=n.asm.Lc).apply(null,arguments)},n.dynCall_viiff=function(){return(n.dynCall_viiff=n.asm.Mc).apply(null,arguments)},n.dynCall_viiffff=function(){return(n.dynCall_viiffff=n.asm.Nc).apply(null,arguments)},n.dynCall_iiiiiii=function(){return(n.dynCall_iiiiiii=n.asm.Oc).apply(null,arguments)},n.dynCall_iiifii=function(){return(n.dynCall_iiifii=n.asm.Pc).apply(null,arguments)},n.dynCall_jii=function(){return(n.dynCall_jii=n.asm.Qc).apply(null,arguments)},n.dynCall_jij=function(){return(n.dynCall_jij=n.asm.Rc).apply(null,arguments)},n.dynCall_fi=function(){return(n.dynCall_fi=n.asm.Sc).apply(null,arguments)},n.dynCall_jijf=function(){return(n.dynCall_jijf=n.asm.Tc).apply(null,arguments)},n.dynCall_iiiij=function(){return(n.dynCall_iiiij=n.asm.Uc).apply(null,arguments)},n.dynCall_i=function(){return(n.dynCall_i=n.asm.Vc).apply(null,arguments)},n.dynCall_vif=function(){return(n.dynCall_vif=n.asm.Wc).apply(null,arguments)},n.dynCall_di=function(){return(n.dynCall_di=n.asm.Xc).apply(null,arguments)},n.dynCall_vid=function(){return(n.dynCall_vid=n.asm.Yc).apply(null,arguments)},n.dynCall_iiiifii=function(){return(n.dynCall_iiiifii=n.asm.Zc).apply(null,arguments)},n.dynCall_iiiffi=function(){return(n.dynCall_iiiffi=n.asm._c).apply(null,arguments)},n.dynCall_vifffffffff=function(){return(n.dynCall_vifffffffff=n.asm.$c).apply(null,arguments)},n.dynCall_iifffffffff=function(){return(n.dynCall_iifffffffff=n.asm.ad).apply(null,arguments)},n.dynCall_iiff=function(){return(n.dynCall_iiff=n.asm.bd).apply(null,arguments)},n.dynCall_iif=function(){return(n.dynCall_iif=n.asm.cd).apply(null,arguments)},n.dynCall_viff=function(){return(n.dynCall_viff=n.asm.dd).apply(null,arguments)},n.dynCall_viffffff=function(){return(n.dynCall_viffffff=n.asm.ed).apply(null,arguments)},n.dynCall_dii=function(){return(n.dynCall_dii=n.asm.fd).apply(null,arguments)},n.dynCall_viid=function(){return(n.dynCall_viid=n.asm.gd).apply(null,arguments)},n.dynCall_iiiiffi=function(){return(n.dynCall_iiiiffi=n.asm.hd).apply(null,arguments)},n.dynCall_fiii=function(){return(n.dynCall_fiii=n.asm.id).apply(null,arguments)},n.dynCall_viiif=function(){return(n.dynCall_viiif=n.asm.jd).apply(null,arguments)},n.dynCall_viifffffffff=function(){return(n.dynCall_viifffffffff=n.asm.kd).apply(null,arguments)},n.dynCall_viiffffff=function(){return(n.dynCall_viiffffff=n.asm.ld).apply(null,arguments)},n.dynCall_viifff=function(){return(n.dynCall_viifff=n.asm.md).apply(null,arguments)},n.dynCall_viiifii=function(){return(n.dynCall_viiifii=n.asm.nd).apply(null,arguments)},n.dynCall_viiiiiii=function(){return(n.dynCall_viiiiiii=n.asm.od).apply(null,arguments)},n.dynCall_viffff=function(){return(n.dynCall_viffff=n.asm.pd).apply(null,arguments)},n.dynCall_vifff=function(){return(n.dynCall_vifff=n.asm.qd).apply(null,arguments)},n.dynCall_iiffi=function(){return(n.dynCall_iiffi=n.asm.rd).apply(null,arguments)},n.dynCall_iiif=function(){return(n.dynCall_iiif=n.asm.sd).apply(null,arguments)},n.dynCall_iiiiij=function(){return(n.dynCall_iiiiij=n.asm.td).apply(null,arguments)},n.dynCall_iijj=function(){return(n.dynCall_iijj=n.asm.ud).apply(null,arguments)},n.dynCall_vffff=function(){return(n.dynCall_vffff=n.asm.vd).apply(null,arguments)},n.dynCall_viiiiiiii=function(){return(n.dynCall_viiiiiiii=n.asm.wd).apply(null,arguments)},n.dynCall_vf=function(){return(n.dynCall_vf=n.asm.xd).apply(null,arguments)},n.dynCall_viiiiiiiii=function(){return(n.dynCall_viiiiiiiii=n.asm.yd).apply(null,arguments)},n.dynCall_viiiiiiiiii=function(){return(n.dynCall_viiiiiiiiii=n.asm.zd).apply(null,arguments)},n.dynCall_viifi=function(){return(n.dynCall_viifi=n.asm.Ad).apply(null,arguments)},n.dynCall_fiifiii=function(){return(n.dynCall_fiifiii=n.asm.Bd).apply(null,arguments)},n.dynCall_iiifiii=function(){return(n.dynCall_iiifiii=n.asm.Cd).apply(null,arguments)},n.dynCall_viiifiii=function(){return(n.dynCall_viiifiii=n.asm.Dd).apply(null,arguments)},n.dynCall_vifii=function(){return(n.dynCall_vifii=n.asm.Ed).apply(null,arguments)},n.dynCall_viifd=function(){return(n.dynCall_viifd=n.asm.Fd).apply(null,arguments)},n.dynCall_viddi=function(){return(n.dynCall_viddi=n.asm.Gd).apply(null,arguments)},n.dynCall_viiiiiffii=function(){return(n.dynCall_viiiiiffii=n.asm.Hd).apply(null,arguments)},n.dynCall_jiiii=function(){return(n.dynCall_jiiii=n.asm.Id).apply(null,arguments)},n.dynCall_jiji=function(){return(n.dynCall_jiji=n.asm.Jd).apply(null,arguments)},n.dynCall_iidiiii=function(){return(n.dynCall_iidiiii=n.asm.Kd).apply(null,arguments)},n._asyncify_start_unwind=function(){return(n._asyncify_start_unwind=n.asm.Ld).apply(null,arguments)},n._asyncify_stop_unwind=function(){return(n._asyncify_stop_unwind=n.asm.Md).apply(null,arguments)},n._asyncify_start_rewind=function(){return(n._asyncify_start_rewind=n.asm.Nd).apply(null,arguments)},n._asyncify_stop_rewind=function(){return(n._asyncify_stop_rewind=n.asm.Od).apply(null,arguments)},n.GL=Vt,n.Asyncify=yt,X=function e(){yr||_r(),yr||(X=e)},n.run=_r,n.preInit)for("function"==typeof n.preInit&&(n.preInit=[n.preInit]);n.preInit.length>0;)n.preInit.pop()();return _r(),e.ready});class it{constructor(){this.executing=!1,this.queue=[]}exec(e,t,...r){return new Promise((n=>{this.queue.push({fn:async()=>{if(!e)return void n(null);const a=await e.call(t,...r);n(a)}}),this.executing||this.execNextTask()}))}execNextTask(){if(this.queue.length<1)return void(this.executing=!1);this.executing=!0;this.queue.shift().fn().then((()=>{this.execNextTask()})).catch((()=>{this.execNextTask()}))}}e.PAGInit=(e={})=>at(e).then((e=>((e=>{rt=e,e.PAG=e,e.PAGFile=H,e.PAGPlayer=K,e.PAGView=we,e.PAGFont=Ce,e.PAGImage=te,e.PAGLayer=B,e.PAGComposition=O,e.PAGSurface=z,e.PAGTextLayer=Je,e.PAGImageLayer=Ye,e.PAGSolidLayer=tt,e.VideoReader=Fe,e.NativeImage=Q,e.ScalerContext=Ve,e.WebMask=We,e.GlobalCanvas=ze,e.BackendContext=ce,e.Matrix=I,e.traceImage=function(e,t){const r=document.createElement("canvas");r.width=e.width,r.height=e.height;const n=r.getContext("2d"),a=new ImageData(new Uint8ClampedArray(t),r.width,r.height);n.putImageData(a,0,0),document.body.appendChild(r)},e.registerSoftwareDecoderFactory=function(t=null){e._registerSoftwareDecoderFactory(t)},e.SDKVersion=function(){return e._SDKVersion()}})(e),e.webAssemblyQueue=new it,e.globalCanvas=new e.GlobalCanvas,e.PAGFont.registerFallbackFontNames(),e))).catch((e=>{throw console.error(e),new Error("PAGInit fail! Please check .wasm file path valid.")})),e.types=x,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=libpag.min.js.map
