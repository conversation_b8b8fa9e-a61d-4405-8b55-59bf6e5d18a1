/**
 * videojs-flvjs
 * @version 0.2.0
 * @copyright 2018 mister-ben <****************>
 * @license Apache-2.0
 */
(function (f) { if (typeof exports === "object" && typeof module !== "undefined") { module.exports = f() } else if (typeof define === "function" && define.amd) { define([], f) } else { var g; if (typeof window !== "undefined") { g = window } else if (typeof global !== "undefined") { g = global } else if (typeof self !== "undefined") { g = self } else { g = this } g.videojsFlvjs = f() } })(function () {
    var define, module, exports; return (function e(t, n, r) { function s(o, u) { if (!n[o]) { if (!t[o]) { var a = typeof require == "function" && require; if (!u && a) return a(o, !0); if (i) return i(o, !0); var f = new Error("Cannot find module '" + o + "'"); throw f.code = "MODULE_NOT_FOUND", f } var l = n[o] = { exports: {} }; t[o][0].call(l.exports, function (e) { var n = t[o][1][e]; return s(n ? n : e) }, l, l.exports, e, t, n, r) } return n[o].exports } var i = typeof require == "function" && require; for (var o = 0; o < r.length; o++)s(r[o]); return s })({
        1: [function (require, module, exports) {
            (function (global) {
                'use strict';

                Object.defineProperty(exports, "__esModule", {
                    value: true
                });

                var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

                var _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if ("value" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };

                var _video = (typeof window !== "undefined" ? window['videojs'] : typeof global !== "undefined" ? global['videojs'] : null);

                var _video2 = _interopRequireDefault(_video);

                function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

                function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

                function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

                function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } /**
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                * @file plugin.js
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                */

                var Html5 = _video2.default.getTech('Html5');
                var mergeOptions = _video2.default.mergeOptions || _video2.default.util.mergeOptions;
                var defaults = {
                    mediaDataSource: {
                        isLive: true,
                        cors: true,
                        withCredentials: false,
                    },
                    config: {
                        isLive: true,
                        enableStashBuffer: false,
                        fixAudioTimestampGap: false,
                    }
                };

                var Flvjs = function (_Html) {
                    _inherits(Flvjs, _Html);

                    /**
                     * Create an instance of this Tech.
                     *
                     * @param {Object} [options]
                     *        The key/value store of player options.
                     *
                     * @param {Component~ReadyCallback} ready
                     *        Callback function to call when the `Flvjs` Tech is ready.
                     */
                    function Flvjs(options, ready) {
                        _classCallCheck(this, Flvjs);

                        options = mergeOptions(defaults, options);
                        return _possibleConstructorReturn(this, (Flvjs.__proto__ || Object.getPrototypeOf(Flvjs)).call(this, options, ready));
                    }

                    /**
                     * A getter/setter for the `Flvjs` Tech's source object.
                     *
                     * @param {Tech~SourceObject} [src]
                     *        The source object you want to set on the `Flvjs` techs.
                     *
                     * @return {Tech~SourceObject|undefined}
                     *         - The current source object when a source is not passed in.
                     *         - undefined when setting
                     */


                    _createClass(Flvjs, [{
                        key: 'setSrc',
                        value: function setSrc(src) {
                            if (this.flvPlayer) {
                                // Is this necessary to change source?
                                this.flvPlayer.detachMediaElement();
                                this.flvPlayer.destroy();
                            }

                            var mediaDataSource = this.options_.mediaDataSource;
                            var config = this.options_.config;

                            mediaDataSource.type = mediaDataSource.type === undefined ? 'flv' : mediaDataSource.type;
                            mediaDataSource.url = src;
                            this.flvPlayer = window.flvjs.createPlayer(mediaDataSource, config);
                            this.flvPlayer.attachMediaElement(this.el_);
                            this.flvPlayer.load();
                        }

                        /**
                         * Dispose of flvjs.
                         */

                    }, {
                        key: 'dispose',
                        value: function dispose() {
                            if (this.flvPlayer) {
                                this.flvPlayer.detachMediaElement();
                                this.flvPlayer.destroy();
                            }
                            _get(Flvjs.prototype.__proto__ || Object.getPrototypeOf(Flvjs.prototype), 'dispose', this).call(this);
                        }
                    }]);

                    return Flvjs;
                }(Html5);

                /**
                 * Check if the Flvjs tech is currently supported.
                 *
                 * @return {boolean}
                 *          - True if the Flvjs tech is supported.
                 *          - False otherwise.
                 */


                Flvjs.isSupported = function () {

                    return window.flvjs && window.flvjs.isSupported();
                };

                /**
                 * Flvjs supported mime types.
                 *
                 * @constant {Object}
                 */
                Flvjs.formats = {
                    'video/flv': 'FLV',
                    'video/x-flv': 'FLV'
                };

                /**
                 * Check if the tech can support the given type
                 *
                 * @param {string} type
                 *        The mimetype to check
                 * @return {string} 'probably', 'maybe', or '' (empty string)
                 */
                Flvjs.canPlayType = function (type) {
                    if (Flvjs.isSupported() && type in Flvjs.formats) {
                        return 'maybe';
                    }

                    return '';
                };

                /**
                 * Check if the tech can support the given source
                 * @param {Object} srcObj
                 *        The source object
                 * @param {Object} options
                 *        The options passed to the tech
                 * @return {string} 'probably', 'maybe', or '' (empty string)
                 */
                Flvjs.canPlaySource = function (srcObj, options) {
                    return Flvjs.canPlayType(srcObj.type);
                };

                // Include the version number.
                Flvjs.VERSION = '0.2.0';

                _video2.default.registerTech('Flvjs', Flvjs);

                exports.default = Flvjs;
            }).call(this, typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
        }, {}]
    }, {}, [1])(1)
});
