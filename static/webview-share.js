/* eslint-disable */
!(function (window) {

    var MC_PROTOCOL_VERSION;


    var split = function (text, lineSplitter, fieldSplitter) {
        var list = {};
        if (text) {
            lineSplitter = lineSplitter || "\n";
            fieldSplitter = fieldSplitter || "\t";
            var pairs = text.split(lineSplitter);
            for (var i = 0; i < pairs.length; i++) {
                var parts = pairs[i].split(fieldSplitter);
                var keyname = parts[0], keyvalue = decodeURIComponent(parts[1]);
                if (typeof list[keyname] === "undefined") {
                    list[keyname] = keyvalue;
                } else if (typeof list[keyname] === "string") {
                    var arr = [list[keyname], keyvalue];
                    list[keyname] = arr;
                } else if (keyname) {
                    list[keyname].push(keyvalue || '');
                }
            }
        }
        return list;
    };

    var searchParams = function () {
        return split((window.location.search.replace(/^\?/ig, '')), '&', '=');
    };

    var getSearch = function (key) {
        return searchParams()[key];
    };

    MC_PROTOCOL_VERSION = getSearch('_mcProtocol');
    if (MC_PROTOCOL_VERSION) {
        MC_PROTOCOL_VERSION = parseFloat(MC_PROTOCOL_VERSION);
    } else {
        MC_PROTOCOL_VERSION = 4.1;   //默认为最新版本
    }

    // 输出日志
    var log = function (group, methods, params, url) {
        console.log('[' + group + '.' + methods + ']\n\n' + url + '\n\n' + (params && (url + '?' + params)) + '\n\n' + decodeURIComponent(params) + '\n\n');
    };

    // 获取平台
    var getSystem = function () {

        var i;
        var ua = window.navigator.userAgent.toLocaleLowerCase();
        var system = ['iphone', 'android', 'windows', 'ipad', 'mac'];

        for (i = 0; i < system.length; i++) {
            if (ua.indexOf(system[i]) > -1) {
                return system[i];
            }
        }

        return 'other';

    };

    // if (getSystem() == 'ipad') {
    //     var iframe = document.createElement('iframe');
    //     iframe.style.display = 'none';
    //     document.body.appendChild(iframe);
    //     iframe.src = "";
    // }

    // 获取回调名称
    var getCallbackName = function (params) {

        var e;

        if (params) {
            for (e in params) {
                if (e === 'callbackName') {
                    return params[e];
                }
            }
        }

        return null;

    };

    // 获取回调
    var getCallback = function (params) {

        var e;

        if (params) {
            for (e in params) {
                if (e === 'callback') {
                    return params[e];
                }
            }
        }

        return null;

    };

    // 拆分对象
    var objectToParams = function (params) {

        var paramString = [];
        var e;
        var paramsType;

        for (e in params) {

            paramsType = typeof params[e];

            if (paramsType === 'string' || paramsType === 'number') {
                paramString.push(e + '=' + encodeURIComponent(params[e]));
            } else {
                paramString.push(e + '=' + encodeURIComponent(JSON.stringify(params[e])));
            }

        }

        return paramString;

    };

    // 解析params
    var parseParams = function (params) {

        var paramString = [];
        var e;

        if (params) {

            for (e in params) {

                switch (e) {

                    case 'config':
                        paramString = paramString.concat(objectToParams(params[e]));
                        break;
                    case 'callback':
                    case 'callbackName':
                        break;
                    default:
                        if (typeof params[e] === 'object') {
                            paramString.push(e + '=' + encodeURIComponent(JSON.stringify(params[e])));
                        } else {
                            paramString.push(e + '=' + encodeURIComponent(params[e]));
                        }

                }

            }

            return paramString.join('&');

        }

        return '';

    };

    // 创建回调函数
    var buildCallback = function (callback, callbackName) {

        // 如果自定义回调名称则不做销毁处理
        if (callbackName) {
            window.webview.callbacks[callbackName] = function () {
                callback && callback.apply(callback, arguments);
            };
        } else {
            callbackName = 'webviewCallback_' + Math.random().toString(32).substring(2);
            window.webview.callbacks[callbackName] = function () {
                callback && callback.apply(callback, arguments);
                delete window.webview.callbacks[callbackName];
            };
        }

        return callbackName;

    };

    // 创建URL
    var buildUrl = function (group, methods) {
        if (MC_PROTOCOL_VERSION > 4) {
            return 'http://core.luban.mucang.cn/' + group + '/' + methods;
        } else if (MC_PROTOCOL_VERSION == 4 && (group != 'user' && group != 'app')) {
            return 'http://core.luban.mucang.cn/' + group + '/' + methods;
        } else {
            return 'mc-web://' + group + '/' + methods;
        }
    };

    // ios请求
    var iosExecute = function (url, params, callback, async, callbackName) {
        if (didFinished) {

            if (async) {
                return window.getMucangIOSWebViewData(url + (params ? ('?' + params) : ''), buildCallback(callback, callbackName));
            }

            return window.getMucangIOSWebViewData(url + (params ? ('?' + params) : ''));
        }
        iosExceList.push({
            url: url,
            params: params,
            callback: callback,
            async: async,
            callbackName: callbackName
        });

    };


    // ipad请求
    var iosExceList = [];
    //ios的webview在页面加载完成时执行的方法
    var didFinished = false;
    window.didFinishLoad = function () {
        didFinished = true;
        while (iosExceList.length > 0) {
            var item = iosExceList[0];
            ipadExecute(item.url, item.params, item.callback, item.async, item.callbackName)
            iosExceList.shift();
        }
    };

    setTimeout(function () {
        window.didFinishLoad();
    }, 2000)

    var ipadExecute = function (url, params, callback, async, callbackName) {

        // if (didFinished) {
        return iosExecute(url, params, callback, async, callbackName);
        // }
        // iosExceList.push({
        //     url: url,
        //     params: params,
        //     callback: callback,
        //     async: async,
        //     callbackName: callbackName
        // });
    };

    // android请求
    var androidExecute = function (url, params, callback, async, callbackName) {
        if (async) {
            return window.mcAndroidWebview1.getMucangWebViewData(url + (params ? ('?' + params) : ''), buildCallback(callback, callbackName));
        }

        return window.mcAndroidWebview1.getMucangWebViewData(url + (params ? ('?' + params) : ''));

    };

    // 执行
    var execute = function (group, methods, userParams, async) {
        // url
        var url = buildUrl(group, methods, userParams);
        // 参数
        var params = parseParams(userParams);
        // callback
        var callback = getCallback(userParams);
        // callbackName
        var callbackName = getCallbackName(userParams);

        // 判断平台
        switch (getSystem()) {

            case 'iphone':
                return iosExecute(url, params, callback, async, callbackName);
            case 'ipad':
                return ipadExecute(url, params, callback, async, callbackName);
            case 'android':
                return androidExecute(url, params, callback, async, callbackName);

            default:
                if (!window.webview.debug) {
                    log(group, methods, params, url);
                }

        }

        if (window.webview.debug) {
            log(group, methods, params, url);
        }

        return undefined;

    };

    (function () {

        var handle;

        switch (getSystem()) {

            case 'android':

                handle = function () {

                    var value = (window.mcAndroidWebview2.getMucangWebViewCallbackData());
                    if (typeof value == 'string') {
                        value = JSON.parse(value);
                    }
                    window.webview.callbacks[value.callback] && window.webview.callbacks[value.callback](JSON.parse(value.data));

                };

                window.addEventListener('online', handle, false);

                window.addEventListener('offline', handle, false);


                break;

            case 'iphone':
                window.mucangIOSWebViewCallback = function (callbackName, data) {
                    window.webview.callbacks[callbackName] && window.webview.callbacks[callbackName](JSON.parse(data));
                };

                break;
            case 'ipad':
                window.mucangIOSWebViewCallback = function (callbackName, data) {
                    window.webview.callbacks[callbackName] && window.webview.callbacks[callbackName](JSON.parse(data));
                };

                break;

            default:

        }

    })();

    window.webview = window.webview || {};

    window.webview.debug = false;

    window.webview.callbacks = {};

    window.webview.core = {

        // 异步执行程序
        async: function (group, methods, params) {
            return execute(group, methods, params, true);
        },

        // 执行请求
        execute: function (group, methods, params) {
            return execute(group, methods, params, false);
        }

    };


    window.MCJSBridge = function () {


        this.callFn = function (nameSpace, fnname, params, callback) {
            nameSpace = nameSpace.toLowerCase();
            var url = 'http://' + nameSpace + '.luban.mucang.cn/' + fnname;
            var params = parseParams(params);
            var callbackName = buildCallback(callback, getCallbackName(params));

            switch (getSystem()) {
                case 'android':
                    // window.mcAndroidWebview1&&window.mcAndroidWebview1.getMucangWebViewData(url, callbackName);
                    androidExecute(url, params, callback, true, callbackName);
                    break;
                case 'ipad':
                    // setTimeout(function () {
                    //     iframe.src = (url + '&callbackName=' + callbackName);
                    // }, 300);
                    ipadExecute(url, params, callback, true, callbackName);
                    break;
                case 'iphone':
                    // window.getMucangIOSWebViewData&&window.getMucangIOSWebViewData(url, callbackName);
                    iosExecute(url, params, callback, true, callbackName);
                    break;
            }
        };

        this.regFn = function (fnname, callback) {
            //fnname: 方法名，
            switch (getSystem()) {
                case 'android':
                    var url = 'http://listener.luban.mucang.cn/' + fnname;
                    androidExecute(url, '', callback, true, fnname);

                    // window.webview.callbacks[fnname] = function () {
                    //     callback && callback.apply(callback, arguments);
                    // };
                    // window.mcAndroidWebview1&&window.mcAndroidWebview1.getMucangWebViewData(url, fnname);
                    break;
                case 'iphone':
                case 'ipad':
                    window[fnname] = function (data) {
                        callback && callback(JSON.parse(data));
                    };
                    break;
            }
        };
    }

    window.mcJSBridge = new MCJSBridge();
})(window);
!(function (self) {

    var getSystem = function () {

        var i;
        var ua = window.navigator.userAgent.toLocaleLowerCase();
        var system = ['iphone', 'android', 'windows', 'ipad', 'mac'];

        for (i = 0; i < system.length; i++) {
            if (ua.indexOf(system[i]) > -1) {
                return system[i];
            }
        }

        return 'other';

    };

    self.webview = self.webview || {};

    self.webview['native'] = {

        // 打开相册, 选择图片
        album: function (config, callback) {

            config.callback = function (data) {
                callback && callback(data.data);
            };

            return self.webview.core.async('native', 'album', config);

        },

        // 新的打开相册, 选择图片
        photo: function (config, callback) {

            config.callback = function (data) {
                callback && callback(data.data);
            };

            return self.webview.core.async('native', 'photo', config);

        },

        // 保存图片到相册
        saveImage: function (data, callback) {
            return self.webview.core.async('native', 'saveImage', {
                data: data,
                callback: callback
            });
        },

        uploadImage: function (config, callback) {
            config.callback = function (data) {
                callback && callback(data);
            };

            return self.webview.core.async('native', 'uploadImage', config);
        },

        // 选择视频
        selectVideo: function (callback) {

            return self.webview.core.async('native', 'selectVideo', {
                callback: function (data) {
                    callback(data);
                }
            });

        }

    };

    self.webview.web = {

        // 打开页面
        open: function (url, config) {
            return self.webview.core.async('web', 'open', {
                url: url,
                config: config
            });
        },

        // 设置
        setting: function (config) {
            return self.webview.core.async('web', 'setting', {
                config: config
            });
        },

        // 回退
        back: function () {
            return self.webview.core.async('web', 'back');
        },

        // 关闭
        close: function () {
            return self.webview.core.async('web', 'close');
        },

        // 打开菜单
        menu: function () {
            return self.webview.core.async('web', 'menu');
        }

    };

    self.webview.share = {

        // 打开某一个渠道的分享菜单
        open: function (channel) {
            return self.webview.core.async('share', 'open', {
                channel: channel
            });
        },

        // 设置
        setting: function (config, callback) {
            return self.webview.core.async('share', 'setting', {
                config: config,
                callback: function (data) {
                    callback&&callback(data)
                }
            });
        },

        // 监听
        on: function (callback) {

            if(getSystem() == 'iphone'){
                return self.webview.core.async('share', 'on', {
                    callbackName: 'shareOn',
                    callback: function (data) {
                        callback(data.data.channel, data.data.success);
                    }
                });
            }else if(getSystem() == 'android'){
                window.mcJSBridge.regFn('share', function (data) {
                    callback(data.data.channel, data.data.success);
                })
            }
        }

    };

    self.webview.app = {

        // 安装
        install: function (appId, pkg, url, version, callback) {
            return self.webview.core.async('app', 'install', {
                appId: appId,
                pkg: pkg,
                url: url,
                version: version,
                callback: function (data) {
                    callback && callback(data);
                }
            });
        },

        // 检查APP
        check: function (pkg, callback) {
            return self.webview.core.async('app', 'check', {
                pkg: JSON.stringify(pkg),
                callback: function (data) {
                    callback && callback(data);
                }
            });
        },

        // 打开app
        open: function (appid, async) {

            typeof async === 'undefined' && (async = true);

            return self.webview.core.async('app', 'open', {
                appId: appid,
                async: async
            });

        }

    };

    self.webview.system = {

        // 打点统计
        stat: function (evtId, evtName, properties) {
            var config = {
                eventId: evtId,
                eventName: evtName,
                eventLabel: evtName
            };
            if(properties){
                config.properties = {
                    common: properties
                }
            }
            return self.webview.core.async('system', 'stat', config);
        },

        // 获取缓存数据
        getcache: function (config, callback) {
            config.callback = function (data) {
                callback && callback(data.data);
            };
            return self.webview.core.async('system', 'getcache', config);
        },

        // 设置缓存数据
        setcache: function (config, callback) {
            return self.webview.core.async('system', 'setcache', config);
        },

        // 版本
        version: function (callback) {
            return self.webview.core.async('system', 'version', {
                callback: callback
            });
        },

        // 返回基础参数
        info: function (callback) {
            return self.webview.core.async('system', 'info', {
                callback: function (data) {
                    if(typeof data == 'object'){
                        callback((data));
                    }else if(typeof data == 'string'){
                        callback(JSON.parse(data));
                    }
                }
            });
        },

        // 拨打电话
        call: function (source, group, label, phone, title) {

            var config = {
                source: source,
                group: group,
                label: label,
                phone: JSON.stringify(phone)
            };

            if (title) {
                config.title = title;
            }

            return self.webview.core.async('system', 'call', config);

        },

        // 输出日志
        log: function (tag, message) {
            return self.webview.core.async('system', 'log', {
                tag: tag,
                message: message
            });
        },

        // 显示提示
        toast: function (message) {
            return self.webview.core.async('system', 'toast', {
                message: message
            });
        },

        // 警告
        alert: function (message, title) {
            return self.webview.core.async('system', 'alert', {
                message: message,
                title: title || ''
            });
        },

        // 确认
        confirm: function (message, callback, config) {

            config || (config = {});
            config.message = message;
            config.callback = function (data) {
                callback(data.data);
            };

            return self.webview.core.async('system', 'confirm', config);

        },

        // 剪切板
        copy: function () {

            var args = arguments;

            return self.webview.core.async('system', 'copy', {
                text: typeof args[0] === 'string' ? args[0] : '',
                callback: typeof args[0] === 'function' ? args[0] : args[1]
            });

        },

        // 添加图标
        addLink: function (config) {

            var a = document.createElement('a');

            a.href = config.icon;
            config.icon = a.href;

            return self.webview.core.async('system', 'addLink', config);

        },

        // 移除图标
        removeLink: function (name) {
            return self.webview.core.async('system', 'removeLink', {
                name: name
            });
        }

    };

    self.webview.http = {

        // get
        get: function (url, config) {
            var callback = config.callback;

            config || (config = {});
            config.url = url;
            config.callback = function () {
                callback(arguments[0]);
            };

            return self.webview.core.async('http', 'get', config);

        },

        // post
        post: function (url, config) {

            var callback = config.callback;

            config || (config = {});
            config.url = url;
            config.callback = function () {
                callback(arguments[0]);
            };

            return self.webview.core.async('http', 'post', config);

        },

        // abort
        abort: function (httpId) {
            return self.webview.core.async('http', 'abort', {
                httpId: httpId
            });
        }

    };

    self.webview.user = {

        // 获取用户信息
        get: function (callback) {
            return self.webview.core.async('user', 'get', {
                callback: function (data) {
                    if(typeof data == 'object'){
                        callback((data));
                    }else if(typeof data == 'string'){
                        callback(JSON.parse(data));
                    }
                }
            });
        },

        // 登陆
        /*
        * config:{
        * skipAuthRealName: Boolean  是否跳过实名认证页面 true表示跳过 false表示不跳过  默认不跳过
        * pageType: String  登录类型。有一下几种
        *   1、'third' 第三方登录  （默认登录方式）
        *   2、'sms'  短信登录
        *   3、'pwd' 账号密码登录
        *   4、'quickLogin' 一键快捷登录
        * }
        * */
        login: function (from, callback, config) {

            var that = this;
            var config = config || {};
            config.from = from;
            config.callback = function () {
                callback && that.get(callback)
            };

            return self.webview.core.async('user', 'login', config);

            // return self.webview.core.async('user', 'login', {
            //     from: from,
            //     callback: function () {
            //         callback && that.get(callback);
            //     }
            // });

        },

        // 登陆
        loginSkipAuth: function (from, skipAuthRealName, callback, pageType) {

            var that = this;

            return self.webview.core.async('user', 'login', {
                from: from,
                skipAuthRealName: skipAuthRealName,
                pageType: pageType,
                callback: function () {
                    callback && that.get(callback);
                }
            });

        },

        // 退出
        logout: function () {

            self.webview.core.async('user', 'logout');

            return this.get();

        },

        // 注册
        register: function (from, callback) {

            var that = this;

            return self.webview.core.async('user', 'register', {
                from: from,
                callback: function () {
                    callback && that.get(callback);
                }
            });

        }

    };


})(self);
/*
 * JavaScript MD5 1.0.1
 * https://github.com/blueimp/JavaScript-MD5
 *
 * Copyright 2011, Sebastian Tschan
 * https://blueimp.net
 *
 * Licensed under the MIT license:
 * http://www.opensource.org/licenses/MIT
 *
 * Based on
 * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message
 * Digest Algorithm, as defined in RFC 1321.
 * Version 2.2 Copyright (C) Paul Johnston 1999 - 2009
 * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet
 * Distributed under the BSD License
 * See http://pajhome.org.uk/crypt/md5 for more info.
 */

/*jslint bitwise: true */
/*global unescape, define */

(function ($) {
    'use strict';

    /*
    * Add integers, wrapping at 2^32. This uses 16-bit operations internally
    * to work around bugs in some JS interpreters.
    */
    function safe_add(x, y) {
        var lsw = (x & 0xFFFF) + (y & 0xFFFF),
            msw = (x >> 16) + (y >> 16) + (lsw >> 16);
        return (msw << 16) | (lsw & 0xFFFF);
    }

    /*
    * Bitwise rotate a 32-bit number to the left.
    */
    function bit_rol(num, cnt) {
        return (num << cnt) | (num >>> (32 - cnt));
    }

    /*
    * These functions implement the four basic operations the algorithm uses.
    */
    function md5_cmn(q, a, b, x, s, t) {
        return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
    }
    function md5_ff(a, b, c, d, x, s, t) {
        return md5_cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }
    function md5_gg(a, b, c, d, x, s, t) {
        return md5_cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }
    function md5_hh(a, b, c, d, x, s, t) {
        return md5_cmn(b ^ c ^ d, a, b, x, s, t);
    }
    function md5_ii(a, b, c, d, x, s, t) {
        return md5_cmn(c ^ (b | (~d)), a, b, x, s, t);
    }

    /*
    * Calculate the MD5 of an array of little-endian words, and a bit length.
    */
    function binl_md5(x, len) {
        /* append padding */
        x[len >> 5] |= 0x80 << (len % 32);
        x[(((len + 64) >>> 9) << 4) + 14] = len;

        var i, olda, oldb, oldc, oldd,
            a =  1732584193,
            b = -271733879,
            c = -1732584194,
            d =  271733878;

        for (i = 0; i < x.length; i += 16) {
            olda = a;
            oldb = b;
            oldc = c;
            oldd = d;

            a = md5_ff(a, b, c, d, x[i],       7, -680876936);
            d = md5_ff(d, a, b, c, x[i +  1], 12, -389564586);
            c = md5_ff(c, d, a, b, x[i +  2], 17,  606105819);
            b = md5_ff(b, c, d, a, x[i +  3], 22, -1044525330);
            a = md5_ff(a, b, c, d, x[i +  4],  7, -176418897);
            d = md5_ff(d, a, b, c, x[i +  5], 12,  1200080426);
            c = md5_ff(c, d, a, b, x[i +  6], 17, -1473231341);
            b = md5_ff(b, c, d, a, x[i +  7], 22, -45705983);
            a = md5_ff(a, b, c, d, x[i +  8],  7,  1770035416);
            d = md5_ff(d, a, b, c, x[i +  9], 12, -1958414417);
            c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
            b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
            a = md5_ff(a, b, c, d, x[i + 12],  7,  1804603682);
            d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
            c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
            b = md5_ff(b, c, d, a, x[i + 15], 22,  1236535329);

            a = md5_gg(a, b, c, d, x[i +  1],  5, -165796510);
            d = md5_gg(d, a, b, c, x[i +  6],  9, -1069501632);
            c = md5_gg(c, d, a, b, x[i + 11], 14,  643717713);
            b = md5_gg(b, c, d, a, x[i],      20, -373897302);
            a = md5_gg(a, b, c, d, x[i +  5],  5, -701558691);
            d = md5_gg(d, a, b, c, x[i + 10],  9,  38016083);
            c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
            b = md5_gg(b, c, d, a, x[i +  4], 20, -405537848);
            a = md5_gg(a, b, c, d, x[i +  9],  5,  568446438);
            d = md5_gg(d, a, b, c, x[i + 14],  9, -1019803690);
            c = md5_gg(c, d, a, b, x[i +  3], 14, -187363961);
            b = md5_gg(b, c, d, a, x[i +  8], 20,  1163531501);
            a = md5_gg(a, b, c, d, x[i + 13],  5, -1444681467);
            d = md5_gg(d, a, b, c, x[i +  2],  9, -51403784);
            c = md5_gg(c, d, a, b, x[i +  7], 14,  1735328473);
            b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);

            a = md5_hh(a, b, c, d, x[i +  5],  4, -378558);
            d = md5_hh(d, a, b, c, x[i +  8], 11, -2022574463);
            c = md5_hh(c, d, a, b, x[i + 11], 16,  1839030562);
            b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
            a = md5_hh(a, b, c, d, x[i +  1],  4, -1530992060);
            d = md5_hh(d, a, b, c, x[i +  4], 11,  1272893353);
            c = md5_hh(c, d, a, b, x[i +  7], 16, -155497632);
            b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
            a = md5_hh(a, b, c, d, x[i + 13],  4,  681279174);
            d = md5_hh(d, a, b, c, x[i],      11, -358537222);
            c = md5_hh(c, d, a, b, x[i +  3], 16, -722521979);
            b = md5_hh(b, c, d, a, x[i +  6], 23,  76029189);
            a = md5_hh(a, b, c, d, x[i +  9],  4, -640364487);
            d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
            c = md5_hh(c, d, a, b, x[i + 15], 16,  530742520);
            b = md5_hh(b, c, d, a, x[i +  2], 23, -995338651);

            a = md5_ii(a, b, c, d, x[i],       6, -198630844);
            d = md5_ii(d, a, b, c, x[i +  7], 10,  1126891415);
            c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
            b = md5_ii(b, c, d, a, x[i +  5], 21, -57434055);
            a = md5_ii(a, b, c, d, x[i + 12],  6,  1700485571);
            d = md5_ii(d, a, b, c, x[i +  3], 10, -1894986606);
            c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
            b = md5_ii(b, c, d, a, x[i +  1], 21, -2054922799);
            a = md5_ii(a, b, c, d, x[i +  8],  6,  1873313359);
            d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
            c = md5_ii(c, d, a, b, x[i +  6], 15, -1560198380);
            b = md5_ii(b, c, d, a, x[i + 13], 21,  1309151649);
            a = md5_ii(a, b, c, d, x[i +  4],  6, -145523070);
            d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
            c = md5_ii(c, d, a, b, x[i +  2], 15,  718787259);
            b = md5_ii(b, c, d, a, x[i +  9], 21, -343485551);

            a = safe_add(a, olda);
            b = safe_add(b, oldb);
            c = safe_add(c, oldc);
            d = safe_add(d, oldd);
        }
        return [a, b, c, d];
    }

    /*
    * Convert an array of little-endian words to a string
    */
    function binl2rstr(input) {
        var i,
            output = '';
        for (i = 0; i < input.length * 32; i += 8) {
            output += String.fromCharCode((input[i >> 5] >>> (i % 32)) & 0xFF);
        }
        return output;
    }

    /*
    * Convert a raw string to an array of little-endian words
    * Characters >255 have their high-byte silently ignored.
    */
    function rstr2binl(input) {
        var i,
            output = [];
        output[(input.length >> 2) - 1] = undefined;
        for (i = 0; i < output.length; i += 1) {
            output[i] = 0;
        }
        for (i = 0; i < input.length * 8; i += 8) {
            output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << (i % 32);
        }
        return output;
    }

    /*
    * Calculate the MD5 of a raw string
    */
    function rstr_md5(s) {
        return binl2rstr(binl_md5(rstr2binl(s), s.length * 8));
    }

    /*
    * Calculate the HMAC-MD5, of a key and some data (raw strings)
    */
    function rstr_hmac_md5(key, data) {
        var i,
            bkey = rstr2binl(key),
            ipad = [],
            opad = [],
            hash;
        ipad[15] = opad[15] = undefined;
        if (bkey.length > 16) {
            bkey = binl_md5(bkey, key.length * 8);
        }
        for (i = 0; i < 16; i += 1) {
            ipad[i] = bkey[i] ^ 0x36363636;
            opad[i] = bkey[i] ^ 0x5C5C5C5C;
        }
        hash = binl_md5(ipad.concat(rstr2binl(data)), 512 + data.length * 8);
        return binl2rstr(binl_md5(opad.concat(hash), 512 + 128));
    }

    /*
    * Convert a raw string to a hex string
    */
    function rstr2hex(input) {
        var hex_tab = '0123456789abcdef',
            output = '',
            x,
            i;
        for (i = 0; i < input.length; i += 1) {
            x = input.charCodeAt(i);
            output += hex_tab.charAt((x >>> 4) & 0x0F) +
                hex_tab.charAt(x & 0x0F);
        }
        return output;
    }

    /*
    * Encode a string as utf-8
    */
    function str2rstr_utf8(input) {
        return unescape(encodeURIComponent(input));
    }

    /*
    * Take string arguments and return either raw or hex encoded strings
    */
    function raw_md5(s) {
        return rstr_md5(str2rstr_utf8(s));
    }
    function hex_md5(s) {
        return rstr2hex(raw_md5(s));
    }
    function raw_hmac_md5(k, d) {
        return rstr_hmac_md5(str2rstr_utf8(k), str2rstr_utf8(d));
    }
    function hex_hmac_md5(k, d) {
        return rstr2hex(raw_hmac_md5(k, d));
    }

    function md5(string, key, raw) {
        if (!key) {
            if (!raw) {
                return hex_md5(string);
            }
            return raw_md5(string);
        }
        if (!raw) {
            return hex_hmac_md5(key, string);
        }
        return raw_hmac_md5(key, string);
    }

    if (typeof define === 'function' && define.amd) {
        define(function () {
            return md5;
        });
    } else {
        $.md5 = md5;
    }
}(window));

!function (window, undefined) {
    window.mcShare = window.mcShare || {};
    window.mcShare.request = window.mcShare.request || {};
    var wxCon = {regAppId: 'wxee24b820c98aec79', authAppId: 'wx39ddb2e3b2dcb858'};

    if (typeof window.wxConfig == 'object') {
        if (typeof window.wxConfig.length == 'number') {
            window.wxConfig = wxCon;
        } else {
            window.wxConfig.regAppId = window.wxConfig.regAppId || wxCon.regAppId;
            window.wxConfig.authAppId = window.wxConfig.authAppId || wxCon.authAppId;
        }
    } else {
        window.wxConfig = wxCon;
    }

    if (!Date.prototype.format) {
        Date.prototype.format = function (fmt) {
            var o = {
                "M+": this.getMonth() + 1,
                "d+": this.getDate(),
                "h+": this.getHours() % 12,
                "H+": this.getHours(),
                "m+": this.getMinutes(),
                "s+": this.getSeconds(),
                "q+": Math.floor((this.getMonth() + 3) / 3),
                "S": this.getMilliseconds()
            };
            if (/(y+)/.test(fmt))
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt))
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        }
    }

    var split = function (text, lineSplitter, fieldSplitter) {
        var list = {};
        if (text) {
            lineSplitter = lineSplitter || "\n";
            fieldSplitter = fieldSplitter || "\t";
            var pairs = text.split(lineSplitter);
            for (var i = 0; i < pairs.length; i++) {
                var parts = pairs[i].split(fieldSplitter);
                var keyname = parts[0];
                try {
                    var keyvalue = decodeURIComponent(parts[1]);
                } catch (e) {
                    keyvalue = parts[1];
                }

                if (typeof list[keyname] === "undefined") {
                    list[keyname] = keyvalue;
                } else if (typeof list[keyname] === "string") {
                    var arr = [list[keyname], keyvalue];
                    list[keyname] = arr;
                } else if (keyname) {
                    list[keyname].push(keyvalue || '');
                }
            }
        }
        return list;
    };

    var params = function (data) {
        var arr = [];
        for (var i in data) {
            arr.push(encodeURIComponent(i) + '=' + encodeURIComponent(data[i]));
        }
        return arr.join('&');
    }

    window.mcShare.Utils = {
        getPlatform: function () {
            var platform = window.mcShare.platform || this.request.getSearch('platform');
            if (platform == undefined || platform == 'undefined' || platform == 'null') {
                if (window.navigator.userAgent.indexOf('Android') !== -1) {
                    platform = 'Android';
                } else if (window.navigator.userAgent.indexOf('iPhone') !== -1) {
                    platform = 'iPhone';
                } else if (window.navigator.userAgent.indexOf('Windows') !== -1) {
                    platform = 'Windows';
                } else if (window.navigator.userAgent.indexOf('iPad') !== -1) {
                    platform = 'iPad';
                } else {
                    platform = '';
                }
            }
            return platform;
        },
        getAbsPath: function (url) {
            var a = document.createElement('a');
            a.href = url;
            var href = a.href;
            a = null;
            return href;
        },
        format: {
            date: function (date, format) {
                if (date) {
                    return new Date(date).format(format);
                } else {
                    return '';
                }
            }
        },
        object2param: function (obj) {
            var arr = [];
            for (var key in obj) {
                if (key) {
                    arr.push(key + '=' + encodeURIComponent(obj[key]));
                }
            }
            return arr.join('&');
        },
        request: {
            getHash: function (key) {
                return decodeURIComponent(this.hashParams()[key]);
            },
            setHash: function () {
                var args = arguments;
                var params = this.hashParams();
                if (args.length === 1) {
                    for (var e in args[0]) {
                        params[e] = args[0][e];
                    }
                }
                if (args.length === 2) {
                    params[args[0]] = args[1];
                }

                var isAndroid = navigator.userAgent.indexOf('Android') > -1; //android终端
                var isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
                var hash = '#' + window.mcShare.Utils.object2param(params)
                if (isiOS) {
                    window.location.replace(hash);
                } else {
                    window.history.replaceState({}, '', hash)
                }
            },

            delHash: function (key) {
                var params = this.hashParams();
                if (params[key]) {
                    delete params[key];
                    window.location.replace('#' + window.mcShare.Utils.object2param(params));
                }
            },
            hashParams: function () {
                return split((window.location.hash.replace(/^[#\/]+/ig, '')), '&', '=');
            },
            getSearch: function (key) {
                return (this.searchParams()[key]);
            },
            searchParams: function () {
                return split((window.location.search.replace(/^\?/ig, '')), '&', '=');
            }
        },
        stringToObj: function (str, split1, split2) {
            return split(str, split1, split2)
        },
        getChannel: function () {
            var channels = ['Qzone', 'MicroMessenger', 'QQ', 'Weibo', 'MuCang'];
            var userAgent = window.navigator.userAgent;
            var flag = false;
            for (var i = 0; i < channels.length; i++) {
                if (userAgent.indexOf(channels[i]) > -1) {
                    flag = true;
                    if (channels[i] == 'MicroMessenger') {
                        return 'weixin';
                    } else {
                        return channels[i].toLowerCase();
                    }
                }
            }
            if (!flag) {
                return 'other';
            }
        },
        ajax: function (obj) {
            var xhr = new XMLHttpRequest();


            if (typeof obj.data == 'object' && obj.contentType !== false) {
                obj.data = params(obj.data);
            }

            if (obj.method == 'get') {
                obj.url = obj.url.indexOf('?') === -1 ? obj.url + '?' + obj.data : obj.url + '&' + obj.data;
            }
            if (obj.async === true) {
                xhr.onreadystatechange = function () {
                    if (xhr.readyState === 4) {

                        callback();
                    }
                }
            }
            xhr.open(obj.method, obj.url, true);
            xhr.withCredentials = true;
            xhr.setRequestHeader('X-Data-Type', obj.dataType);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            if (obj.contentType !== false) {
                xhr.setRequestHeader("Content-Type", obj.contentType || "application/x-www-form-urlencoded");
            }
            if (obj.method === 'post') {
                xhr.send(obj.data);
            } else {
                xhr.send(null)
            }

            if (obj.async === false) {
                callback();
            }

            function callback() {
                if (xhr.status === 200) {
                    try {
                        obj.success(JSON.parse(xhr.responseText));
                    } catch (e) {
                        console.log(e)
                    }

                } else {
                    obj.error(xhr.status, xhr.statusText)
                }
            }

        },
        //可视化日期
        niceTime: function () {
            var date;
            if (typeof arguments[0] == 'number' || typeof arguments[0] == 'string') {
                date = new Date(arguments[0]);
            } else {
                date = arguments[0];
            }

            var now = new Date();

            var deltaTime = (now.getTime() - date.getTime()) / 1000;
            if (deltaTime < 60) {
                return '刚刚';
            } else if (deltaTime >= 60 && deltaTime < 3600) {
                return parseInt(deltaTime / 60) + '分钟前';
            } else if (deltaTime >= 3600 && this.format.date(date, 'yyyy-MM-dd') == this.format.date(now, 'yyyy-MM-dd')) {
                return this.format.date(date, 'MM-dd HH:mm');
            } else if (date.getFullYear() == now.getFullYear()) {
                return this.format.date(date, 'MM-dd HH:mm');
            } else {
                return this.format.date(date, 'yyyy-MM-dd');
            }
        },
        compareVersion: function (vi1, vi2) {
            vi1 = vi1.toString().split('.');
            vi2 = vi2.toString().split('.');
            for (var i = 0; i < Math.max(vi1.length, vi2.length); i++) {
                if (vi1[i] && vi2[i] && vi1[i] !== vi2[i])
                    return vi1[i] > vi2[i];
                if (!vi1[i])
                    return 0 > vi2[i];
                if (!vi2[i])
                    return vi1[i] > 0;
            }
            return -1;
        },
        sign: eval(function (p, a, c, k, e, d) {
            e = function (c) {
                return c.toString(36)
            };
            if (!''.replace(/^/, String)) {
                while (c--) {
                    d[c.toString(a)] = k[c] || c.toString(a)
                }
                k = [function (e) {
                    return d[e]
                }];
                e = function () {
                    return '\\w+'
                };
                c = 1
            }
            ;
            while (c--) {
                if (k[c]) {
                    p = p.replace(new RegExp('\\b' + e(c) + '\\b', 'g'), k[c])
                }
            }
            return p
        }('o.n=7(a){2 c=9.p(8(q m().r()*9.i()*k)).5();2 d=0;l(2 b=0;b<c.4;b++){d+=8(c[b])}2 e=7(f){6 7(g,h){6(0>=(h-""+g.4))?g:(f[h]||(f[h]=j(h+1).s(0)))+g}}([]);d+=c.4;d=e(d,3-d.5().4);6 a.5()+c+d};', 29, 29, '||var||length|toString|return|function|parseInt|Math|||||||||random|Array|10000|for|Date|sign|window|abs|new|getTime|join'.split('|'), 0, {}))
    }

    function isEqualParams(obj1, obj2) {//排序的函数
        for (var k in obj1) {
            if (obj2[k] !== obj1[k]) {
                return false;
            }
        }

        for (var j in obj2) {
            if (obj1[j] !== obj2[j]) {
                return false;
            }
        }

        return true;
    }

    window.mcShare.getConfig = function (keys, baseParams, callback) {
        var keyArr = keys.split(',');
        baseParams = baseParams || {};

        var retData = {};
        var needRequestKeys = [];
        for (var i = 0; i < keyArr.length; i++) {
            var k = keyArr[i];
            if (window.localStorage.getItem(k)
                && new Date().getTime() < parseInt(window.localStorage.getItem(k + '-checkTime'))
                && isEqualParams(baseParams, JSON.parse(window.localStorage.getItem(k + '-baseParams')))
            ) {
                retData[k] = window.localStorage.getItem(k);
            }else{
                needRequestKeys.push(k)
            }
        }

        if(needRequestKeys.length === 0){
            return callback(retData);
        }


        var data = JSON.parse(JSON.stringify(baseParams));
        data.keys = needRequestKeys.join(',');

        window.mcShare.Utils.ajax({
            url: 'https://songge.ttt.mucang.cn/api/web/config/get.htm',
            data: data,
            method: 'get',
            dataType: 'json',
            async: true,
            success: function (res) {
                if(res.success){
                    for (var i = 0; i < needRequestKeys.length; i++) {
                        var iData = needRequestKeys[i];
                        retData[iData] = res.data[iData];
                        window.localStorage.setItem(iData, res.data[iData])
                        window.localStorage.setItem(iData + '-checkTime', new Date().getTime() + res.checkTime*1000)
                        window.localStorage.setItem(iData + '-baseParams', JSON.stringify(baseParams))
                    }
                    callback(retData);
                }else{
                    console.log('get config fail')
                    callback(false)
                }

            },
            error: function () {
                console.log('get config error')
                callback(false)
            }
        })
    }

    window.mcShare.pay = {

        wxBridgeReady: function (data, callback) {
            WeixinJSBridge.invoke(
                'getBrandWCPayRequest', {
                    "appId": data.appId,            //公众号名称，由商户传入
                    "timeStamp": data.timeStamp,      //时间戳，自1970年以来的秒数
                    "nonceStr": data.nonceStr,      //随机串
                    "package": data.package,
                    "signType": data.signType,      //微信签名方式：
                    "paySign": data.paySign,        //微信签名
                },
                function (res) {
                    callback && callback(res);
                    if (res.err_msg == "get_brand_wcpay_request：ok") {
                    }
                    // 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回    ok，但并不保证它绝对可靠。
                }
            );
        },

        weixin: function (url, params, callback) {
            var _this = this;
            var code = window.mcShare.Utils.request.getSearch('code');
            params.weixinCode = params.weixinCode || params.code || code;
            window.mcShare.Utils.ajax({
                url: url,
                data: params,
                method: 'post',
                dataType: 'json',
                async: true,
                success: function (ret) {
                    var retData = ret.data;
                    var payContent = retData.content;
                    if (typeof payContent == 'string') {
                        payContent = JSON.parse(payContent);
                    }
                    _this.wxBridgeReady(payContent);
                    callback && callback('success', retData);
                },
                error: function () {
                    alert('请求服务器出错！');
                    callback && callback('error');
                }
            })
        }
    }


}(window, undefined);
!function (window) {

    window.mcShare = window.mcShare || {};

    // 百度变量声明
    window._hmt = window._hmt || [];

    // 工具类
    var Utils = window.mcShare.Utils;
    var requestData = window.mcShare.request;

    var shareKey = window.mcShare.shareKey || Utils.request.getSearch('shareKey');
    if (shareKey == undefined || shareKey == 'undefined' || shareKey == 'null') {
        shareKey = '';
    }
    requestData.shareKey = shareKey;

    var placeKey = window.mcShare.placeKey || Utils.request.getSearch('placeKey');
    if (placeKey == undefined || placeKey == 'undefined' || placeKey == 'null') {
        placeKey = '';
    }
    requestData.placeKey = placeKey || shareKey;


    var channel = Utils.getChannel();
    requestData.channel = channel;

    var shareWordsId = window.mcShare.shareWordsId || Utils.request.getSearch('shareWordsId');
    if (!(channel !== "mucang" && shareWordsId)) {
        shareWordsId = '';
    }

    var platform = Utils.getPlatform();

    var shareProduct = window.mcShare.shareProduct || Utils.request.getSearch('shareProduct') ? Utils.request.getSearch('shareProduct') : '';
    requestData.shareProduct = shareProduct;

    var shareData = window.mcShare.shareData || Utils.request.getSearch('shareData') ? Utils.request.getSearch('shareData') : '';

    var root = window.location.pathname.replace(/\/([\w-]+\.\w+)?$/ig, '/');

    window.mcShare.isShare = function () {
        return shareKey ? true : false;
    };

    var debugStatus = false;

    var fingerTouch = function (config) {
        var options = {
            finger: config && config.finger || 1,
            startTime: config && config.startTime || 5,
            endTime: config && config.endTime || 15
        }
        var oldTime, count = true;
        document.addEventListener('touchstart', function (event) {
            if (event.targetTouches.length === options.finger) {
                count = false;//锁关关闭
                oldTime = Math.round(new Date().getTime() / 1000);
            } else {
                count = true;//锁关开启
            }
        }, false);
        document.addEventListener('touchmove', function (event) {
            // if(event.targetTouches.length !== options.finger){
            count = true;//锁关开启
            // }
        }, false);
        document.addEventListener('touchend', function (event) {
            //如果这个元素的位置内只有没有手指且锁关关闭时记录当前结束时间
            if (!count && event.targetTouches.length === 0) {
                count = true;//锁关开启
                var difTime = Math.round(new Date().getTime() / 1000) - oldTime;//两指按下相差的时间
                if (difTime >= options.startTime && difTime <= options.endTime) {
                    debugStatus = true;
                }
            }
        }, false);
    }

    // fingerTouch();

    var fingerTouch2 = {
        count: 0,//点击次数
        time:3000,//定时器间隔时间(用来重置count)
        lastNode:'',
        lastHref:'',
        createDialog:function () {
            var dialog = document.createElement('div');
            var content = document.createElement('div');
            var mask = document.createElement('div');
            var input = document.createElement('input');
            var button = document.createElement('button');
            dialog.setAttribute('id', 'ml_dialog');
            var dialog_style =  'position: fixed;left: 0;right: 0;top: 0;bottom: 0;';
            dialog.setAttribute('style',dialog_style);
            var content_style = 'position: absolute;top: 40%;background-color: #fff;z-index: 10001;left: 10%;width:80%;border-radius: 5px;'
            content.setAttribute('style',content_style);
            var mask_style = 'position: absolute;top: 0;bottom: 0;left: 0;right: 0;background-color: rgba(11,11,11,0.6);z-index: 10000;';
            mask.setAttribute('style', mask_style);
            mask.setAttribute('class','mask');
            var input_style = 'display: block;width: 60%;margin: 0 auto;outline: none;height: 25px;margin-top: 10px;border-radius: 5px;border: 1px solid #ddd;text-indent: 5px;';
            input.setAttribute('style',input_style);
            var button_style = 'display: block;width: 40%;height: 30px;margin: 10px auto;background-color: #0C73AD;border: none;color: #fff;border-radius: 8px;font-weight: 500;font-size: 15px;outline: none;cursor: pointer;';
            button.setAttribute('style',button_style);
            button.innerText = '芝麻开门';
            content.appendChild(input);
            content.appendChild(button);
            dialog.appendChild(content);
            dialog.appendChild(mask);
            return dialog;
        },//生成弹框
        createRq:function () {
            var y = new Date().getFullYear() + '';
            var m = new Date().getMonth() + 1 + '';
            var d = new Date().getDate() + '';
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            return y + m + d;
        },//生成当日时间(格式:20171110)
        init:function () {
            var me = this;
            document.addEventListener('click', function (e) {
                me.timer && clearTimeout(me.timer);//每次点击将定时器清除
                var ml_dialog = document.getElementById('ml_dialog');//获取弹框节点
                if(ml_dialog){
                    if (e.target.tagName === 'BUTTON' && e.target.innerHTML === '芝麻开门') {
                        var val = e.target.previousElementSibling.value;
                        var arr = val.split('.');
                        var rq = me.createRq();
                        var sum = 0;
                        for (var i = 0; i < rq.length; i++) {
                            sum += Number(rq[i]);
                        }
                        if (arr.length === 2 && arr[0] === rq && Number(arr[1]) === sum) {
                            ml_dialog.parentNode.removeChild(ml_dialog);
                            me.callback && me.callback();
                        }
                    }else if(e.target.tagName === 'DIV' && e.target.className === 'mask'){
                        ml_dialog.parentNode.removeChild(ml_dialog);
                    }
                    return;
                }//判断是否存在弹框
                if(e.target === me.lastNode && location.href === me.lastHref){//不存在弹框时,且每次点击的节点一致时，点击次数+1
                    me.count++;
                }else{
                    me.count = 1;
                }
                me.lastNode = e.target;
                me.lastHref = location.href;
                if (me.count >= 30) {//当点击次数到达10次后创建弹框并重置count,不在创建定时器
                    document.body.appendChild(me.createDialog());
                    me.count = 0;
                    return;
                }
                //创建定时器将count重置
                me.timer = setTimeout(function () {
                    me.count = 0;
                }, me.time);
            }, false);
        },//初始化操作
        callback:function () {
            alert('回调成功！');
            debugStatus = true;
        }//验证通过后的回调
    };

    // fingerTouch2.init();

    window.mcShare.Statistic = {

        view: function () {
            if (shareKey) {
                Utils.ajax({
                    method: 'get',
                    data: {
                        shareProduct: shareProduct,
                        sharePlatform: platform,
                        shareChannel: channel,
                        shareKey: shareKey,
                        _r: Utils.sign(1)
                    },
                    url: window.location.protocol + '//report-share.kakamobi.cn/api/open/activity/count-pv.htm',
                    // url: 'http://report.share.kakamobi.cn/api/open/activity/count-pv.htm',
                    dataType: 'json',
                    // async: true,
                    success: function (ret) {
                    },
                    error: function () {
                    }
                })
            }
        },

        event: function (shareEvent) {
            if (shareKey) {
                Utils.ajax({
                    method: 'get',
                    data: {
                        shareProduct: shareProduct,
                        sharePlatform: platform,
                        shareChannel: channel,
                        shareKey: shareKey,
                        shareEvent: shareEvent,
                        _r: Utils.sign(1)
                    },
                    url: window.location.protocol + '//report-share.kakamobi.cn/api/open/activity/count-event.htm',
                    // url: 'http://report.share.kakamobi.cn/api/open/activity/count-event.htm',
                    dataType: 'json',
                    async: true,
                    success: function (ret) {
                    },
                    error: function () {
                    }
                })
            }
        },

        shipper: function (eventName, properties, extraParams) {
            // if (!window._statPageName || !window._statAppName) {
            //     return;
            // }
            if (!window._statAppName) {
                return;
            }
            var appUser = window.localStorage.getItem('_appUser');
            if (!appUser) {
                appUser = md5(new Date().getTime() + Math.random(0, 100000) * 100000);
                window.localStorage.setItem('_appUser', appUser);
            }
            var params = '_plaform=' + (platform && platform.toLocaleLowerCase()) + '&_appName=' + window._statAppName + '&_appUser=' + appUser + '&_deviceId=' + appUser + '&_r=' + Utils.sign(1);

            params = Utils.object2param(extraParams) + '&' + params;

            Utils.ajax({
                method: 'post',
                data: JSON.stringify([
                    {
                        group: 'h5',
                        // event: window._statPageName + '_' + eventName,
                        event: window._statPageName? window._statPageName + '_' + eventName : eventName,
                        timestamp: new Date().getTime(),
                        properties: {
                            common: properties ? properties : {}
                        }
                    }
                ]),
                contentType: 'application/json; charset=UTF-8',
                url: window.location.protocol + '//oort-shipper.kakamobi.cn/api/h5/receiver/send.htm?' + params,
                dataType: 'json',
                async: true,
                success: function (ret) {
                },
                error: function () {
                }
            });
        },

        // shipper1: function (shareEvent, properties) {
        //     this.event(shareEvent);
        //     if (shareKey && channel == 'mucang') {
        //         var hashObj = Utils.request.hashParams();
        //         var searchObj = Utils.request.searchParams();
        //         var baseParams = '';
        //         if (hashObj._appinfo) {
        //             var appInfo = JSON.parse(decodeURIComponent(hashObj._appinfo));
        //             for (var k in appInfo) {
        //                 baseParams = k + '=' + appInfo[k] + '&';
        //             }
        //         } else {
        //             if (searchObj._appName && searchObj._platform && searchObj._product) {
        //                 for (var n in searchObj) {
        //                     baseParams = n + '=' + searchObj[n] + '&';
        //                 }
        //             }
        //         }
        //         if (!baseParams) {
        //             return;
        //         }
        //         Utils.ajax({
        //             method: 'post',
        //             data: [{
        //                 group: 'h5',
        //                 shareProduct: shareProduct,
        //                 sharePlatform: platform,
        //                 shareChannel: channel,
        //                 shareKey: shareKey,
        //                 event: shareEvent,
        //                 properties: properties,
        //                 timestamp: new Date().getTime(),
        //                 duration: 0,
        //                 _r: Utils.sign(1)
        //             }],
        //             url: window.location.protocol + '//oort-shipper.kakamobi.cn/api/open/receiver/send.htm?' + baseParams,
        //             dataType: 'json',
        //             async: true,
        //             success: function (ret) {
        //             },
        //             error: function () {
        //             }
        //         })
        //     }
        // },

        stat: function (eventName, properties, flag) {
            var extraParams = {};
            // window._statPageName = window._statPageName || document.title;
            var channel = Utils.getChannel();
            if(channel == 'qzone'){
                channel = 'q_zone';
            }else if(channel == 'weibo'){
                channel = 'sina';
            }

            //PV事件默认添加渠道
            if(eventName ==  'PV'){
                eventName = eventName + '_' + channel;
            }

            //flag: 如果flag是boolean类型，则判断事件名称后是否需要添加渠道
            if(typeof flag === 'boolean'){
                if(flag === true){
                    eventName = eventName + '_' + channel;
                }
            }else if(typeof flag === 'object'){
                //flag: 如果是对象类型，可以添加一些额外的参数
                extraParams = flag;
                if(extraParams.flag === true){
                    eventName = eventName + '_' + channel;
                    delete extraParams.flag;
                }
            }


            // if (window._statPageName && window._statAppName) {
            if (window._statAppName) {
                if ( channel != 'mucang') {
                    if (debugStatus) {
                        // alert(window._statPageName + '_' + eventName);
                        alert( eventName);
                    }
                    window.mcShare.Statistic.shipper(eventName, properties, extraParams);
                } else {
                    if (window.webview && debugStatus) {
                        // alert(window._statPageName + '_' + eventName);
                        alert( eventName);
                    }
                    window.webview && window.webview.system.stat(window._statAppName, (window._statPageName ? window._statPageName + '_' + eventName : eventName), properties);
                }
            }

        }


    }

    // 自动统计展示渠道
    window.mcShare.Statistic.view();
    setTimeout(function () {
        window.mcShare.Statistic.stat('PV');
    }, 300);


}(window);

!function (window, undefined) {
    window.mcShare = window.mcShare || {};
    var Utils = window.mcShare.Utils;
    var Statistic = window.mcShare.Statistic;
    window.mcShare.WX_READY = false;
    window.mcShare.wxReady = [];
    var wxReadyPush = window.mcShare.wxReady.push;

    window.mcShare.wxReady.push = function (callback) {

        if (window.mcShare.WX_READY) {
            callback();
        } else {
            wxReadyPush.apply(this, arguments);
        }
    }

    window.mcShare.getJSSignObj = function (callback) {
        Utils.ajax({
            method: 'get',
            data: {
                url: window.location.href.replace(/#.*/ig, ''),
                '_r': sign(1),
                weixinApp: window.wxConfig.regAppId
            },
            url: window.location.protocol + '//api-share-m.kakamobi.com/api/open/sign/jssign.htm',
            // url: 'http://api.share.m.kakamobi.com/api/open/sign/jssign.htm',
            dataType: 'json',
            async: true,
            success: function (ret) {
                callback(ret.data);
            }
        })
    }

    var placeChannel = Utils.request.getSearch('placeChannel');
    var placeChannels = {
        weixin_moment: 'menuItem:share:timeline',
        weixin_friend: 'menuItem:share:appMessage',
        sina: '',
        qq: 'menuItem:share:qq',
        q_zone: 'menuItem:share:QZone',
        qq_weibo: 'menuItem:share:weiboApp'
    };
    if (window.mcShare.Utils.getChannel() === 'weixin') {
        window.mcShare.getJSSignObj(function (obj) {
            var signObj = obj;
            if (!wx) {
                return;
            }
            wx.config({
                debug: false,
                appId: window.wxConfig.regAppId,
                timestamp: signObj.timestamp,
                nonceStr: signObj.noncestr,
                signature: signObj.signature,
                jsApiList: [
                    'checkJsApi',
                    'onMenuShareTimeline',
                    'onMenuShareAppMessage',
                    'onMenuShareQQ',
                    'onMenuShareWeibo',
                    'hideMenuItems',
                    'showMenuItems',
                    'hideAllNonBaseMenuItem',
                    'showAllNonBaseMenuItem',
                    'translateVoice',
                    'startRecord',
                    'stopRecord',
                    'onRecordEnd',
                    'playVoice',
                    'pauseVoice',
                    'stopVoice',
                    'uploadVoice',
                    'downloadVoice',
                    'chooseImage',
                    'previewImage',
                    'uploadImage',
                    'downloadImage',
                    'getNetworkType',
                    'openLocation',
                    'getLocation',
                    'hideOptionMenu',
                    'showOptionMenu',
                    'closeWindow',
                    'scanQRCode',
                    'chooseWXPay',
                    'openProductSpecificView',
                    'addCard',
                    'chooseCard',
                    'openCard'
                ]
            });

            wx.ready(function () {
                window.mcShare.WX_READY = true;
                try {
                    window.mcShare.ON_WX_READY()
                } catch (e) {
                }
                if (placeChannel && placeChannel !== 'undefined') {
                    wx.hideOptionMenu();
                    wx.hideMenuItems({
                        menuList: ["menuItem:share:appMessage", "menuItem:share:timeline", "menuItem:share:qq", "menuItem:share:weiboApp", "menuItem:share:facebook", "menuItem:share:QZone"] // 要隐藏的菜单项，只能隐藏“传播类”和“保护类”按钮，所有menu项见附录3
                    });
                    wx.showMenuItems({
                        menuList: [placeChannels[placeChannel]] // 要显示的菜单项，所有menu项见附录3
                    });
                    wx.showOptionMenu();
                }
                wx.onMenuShareAppMessage({
                    title: document.title || window.location.href,
                    desc: document.title || window.location.href,
                    link: window.location.href,
                    trigger: function (res) {
                    },
                    success: function (res) {
                        Statistic.event('weixin-friend-second-share');
                    },
                    cancel: function (res) {
                        Statistic.event('weixin-friend-second-share');
                    },
                    fail: function (res) {
                        Statistic.event('weixin-friend-second-share');
                    }
                });
                wx.onMenuShareTimeline({
                    title: document.title || window.location.href,
                    desc: document.title || window.location.href,
                    link: window.location.href,
                    trigger: function (res) {
                    },
                    success: function (res) {
                        Statistic.event('weixin-moment-second-share');
                    },
                    cancel: function (res) {
                        Statistic.event('weixin-moment-second-share');
                    },
                    fail: function (res) {
                        Statistic.event('weixin-moment-second-share');
                    }
                });
                wx.onMenuShareQQ({
                    title: document.title || window.location.href,
                    desc: document.title || window.location.href,
                    link: window.location.href,
                    trigger: function (res) {
                    },
                    complete: function (res) {
                    },
                    success: function (res) {
                        Statistic.event('qq-second-share');
                    },
                    cancel: function (res) {
                        Statistic.event('qq-second-share');
                    },
                    fail: function (res) {
                        Statistic.event('qq-second-share');
                    }
                });
                wx.onMenuShareWeibo({
                    title: document.title || window.location.href,
                    desc: document.title || window.location.href,
                    link: window.location.href,
                    trigger: function (res) {
                    },
                    complete: function (res) {
                    },
                    success: function (res) {
                        Statistic.event('weibo-second-share');
                    },
                    cancel: function (res) {
                        Statistic.event('weibo-second-share');
                    },
                    fail: function (res) {
                        Statistic.event('weibo-second-share');
                    }
                });
                for (var i = 0; i < window.mcShare.wxReady.length; i++) {
                    window.mcShare.wxReady[i]();
                }
            });
        });
    } else {

    }
}(window, undefined);
!function (window, undefined) {

    //weixin_moment(微信朋友圈),weixin_friend(微信好友),sina(新浪微博),qq(qq好友),q_zone(QQ空间),qq_weibo(腾讯微博)
    var channels = ['weixinMoment', 'weixinFriend', 'weibo', 'qq', 'qzone'];
    window.mcShare = window.mcShare || {};
    var requestData = window.mcShare.request;
    var Utils = window.mcShare.Utils;
    var Statistic = window.mcShare.Statistic;
    var pageTitle = document.title;
    var shareObj = {};
    var transferChannelName = function (channel) {
        switch (channel) {
            case 'weixin_moment':
                return 'weixinMoment';
            case 'weixin_friend':
                return 'weixinFriend';
            case 'sina':
                return 'weibo';
            case 'qq':
                return 'qq';
            case 'q_zone':
                return 'qzone';
            case 'qq_weibo':
                return 'qqweibo';
            default :
                return 'other';
        }
    };

    var bindWXApi = function (obj, callback) {
        var title = obj.shareWords || document.title || window.location.href;
        var desc = obj.description || document.title || window.location.href;
        var link = obj.url || window.location.href;
        var imageUrl = obj.iconUrl || '';

        var userAgent = window.navigator.userAgent;
        if (link.length > 1024 && userAgent.indexOf('Mac') > -1) {
            var arr = link.split('?');
            var params = Utils.stringToObj(arr[1], '&', '=');
            if (params.shareData) {
                delete params.shareData;
            }
            link = arr[0] + '?' + Utils.object2param(params);
        }

        var configWXFriend = {
            title: title,
            desc: desc,
            link: link,
            trigger: function (res) {
            },
            success: function (res) {
                callback && callback('success', 'friend');
                Statistic.event('weixin-friend-second-share')
            },
            cancel: function (res) {
                callback && callback('error', 'friend');
                Statistic.event('weixin-friend-second-share')
            },
            fail: function (res) {

            }
        }
        var configWXMoment = {
            title: obj.moment ? desc : title,
            desc: desc,
            link: link,
            trigger: function (res) {
            },
            success: function (res) {
                callback && callback('success', 'moment');
                Statistic.event('weixin-moment-second-share')
            },
            cancel: function (res) {
                callback && callback('error', 'moment');
                Statistic.event('weixin-moment-second-share')
            },
            fail: function (res) {

            }
        }
        var configWXqq = {
            title: title,
            desc: desc,
            link: link,
            trigger: function (res) {

            },
            success: function (res) {
                callback && callback('success', 'qq');
                Statistic.event('qq-second-share')
            },
            cancel: function (res) {
                callback && callback('error', 'qq');
                Statistic.event('qq-second-share')
            },
            fail: function (res) {

            }
        }
        var configWXweibo = {
            title: title,
            desc: desc,
            link: link,
            trigger: function (res) {
            },
            success: function (res) {
                callback && callback('success', 'weibo');
                Statistic.event('weibo-second-share')
            },
            cancel: function (res) {
                callback && callback('error', 'weibo');
                Statistic.event('weibo-second-share')
            },
            fail: function (res) {

            }
        }
        if (imageUrl) {
            if (imageUrl.indexOf('http') != 0) {
                imageUrl = Utils.getAbsPath(imageUrl);
            }
            configWXFriend.imgUrl = imageUrl;
            configWXMoment.imgUrl = imageUrl;
            configWXqq.imgUrl = imageUrl;
            configWXweibo.imgUrl = imageUrl;
        }

        if (!window.mcShare.WX_READY) {
//            document.title = title;
//
//            window.mcShare.wxReady.push(function () {
//                document.title = pageTitle;
//            });

            window.mcShare.wxReady.push(wx.onMenuShareAppMessage.bind(wx.onMenuShareAppMessage, configWXFriend));

            window.mcShare.wxReady.push(wx.onMenuShareTimeline.bind(wx.onMenuShareTimeline, configWXMoment));

            window.mcShare.wxReady.push(wx.onMenuShareQQ.bind(wx.onMenuShareQQ, configWXqq));

            window.mcShare.wxReady.push(wx.onMenuShareWeibo.bind(wx.onMenuShareWeibo, configWXweibo));
        } else {
//            document.title = pageTitle;
            wx.onMenuShareAppMessage(configWXFriend);
            wx.onMenuShareTimeline(configWXMoment);
            wx.onMenuShareQQ(configWXqq);
            wx.onMenuShareWeibo(configWXweibo);
        }

    };

    var handleShare = function (channel, shareObj, shareData, callback) {
        switch (channel) {
            case 'weixin':
                bindWXApi(shareObj, callback);
                break;
            case 'mucang':
                /*webview的一个bug，在某些版本的android机器上，暂定4.4版本以下，宿主是监听不到url的改变，需要触发一个点击事件*/
                var div = document.createElement('div');
                var event = document.createEvent('HTMLEvents');
                div.addEventListener('click', function () {
                    Utils.request.setHash({shareData: shareData});
                }, false);
                event.initEvent('click', false, true);
                setTimeout(function () {
                    div.dispatchEvent(event);
                }, 1000);
                break;
            default:
                if (shareObj.shareWords) {
                    document.title = shareObj.shareWords;
                }
                break;
        }
    };

    window.onMucangShare = function (event, data, channel, type) {
        var channel = transferChannelName(channel);
        switch (event) {
            case 'click':
                window.Message.trigger('click', false, data);
                break;
            case 'channel':
                window.Message.trigger('channel', false, data, channel);
                break;
            case 'success':
                if (type === 'data') {
                    window.Message.trigger('dataSuccess', false, data);
                } else if (type === 'channel') {
                    window.Message.trigger(channel + 'Success', false, data);
                    window.Message.trigger('channelSuccess', false, data, channel);
                }
                break;
            case 'error':
                if (type === 'data') {
                    window.Message.trigger('dataError', false, data);
                } else if (type === 'channel') {
                    window.Message.trigger(channel + 'Error', false, data);
                    window.Message.trigger('channelError', false, data, channel);
                }
                break;
            case 'cancel':
                window.Message.trigger('shareCancel', false, data, channel);
                break;
        }
    };


    window.mcShare.getAppInfo = function () {
        var channel = Utils.getChannel();
        var userAgent = window.navigator.userAgent;
        var appInfo = {
            appName: '',
            version: '',
            isMucang: false
        };
        var matchArr = [];
        switch (channel) {
            case 'mucang':
                appInfo.isMucang = true;
                matchArr = /(MuCang)\s*\((\w+);\s*([\d\.]+);\s*\w+\s*\)/ig.exec(userAgent);
                if (matchArr) {
                    appInfo.appName = matchArr[2];
                    appInfo.version = matchArr[3];
                } else {
                    matchArr = /(MuCang)\s*(\w+)\s*/ig.exec(userAgent);
                    if (matchArr) {
                        appInfo.appName = matchArr[2];
                    }
                }
                break;
            case 'qq':
                matchArr = /(QQ)\/([\d\.]+)\s./ig.exec(userAgent);
                if (matchArr) {
                    appInfo.appName = matchArr[1];
                    appInfo.version = matchArr[2];
                }
                break;
            case 'weixin':
                matchArr = /(MicroMessenger)\/([\d\.]+)[\s_]/ig.exec(userAgent);
                if (matchArr) {
                    appInfo.appName = matchArr[1];
                    appInfo.version = matchArr[2];
                }
                break;
            case 'weibo':
                matchArr = /__(weibo)__([\d\.]+)__/g.exec(userAgent);
                if (matchArr) {
                    appInfo.appName = matchArr[1];
                    appInfo.version = matchArr[2];
                }
                break;
            case 'qzone':
                matchArr = /(Qzone)\/\w+_([\d\.]+)_/g.exec(userAgent);
                if (matchArr) {
                    appInfo.appName = matchArr[1];
                    appInfo.version = matchArr[2];
                }
                break;
            default:
                break;
        }
        appInfo.shareKey = window.mcShare.request.shareKey;
        appInfo.product = window.mcShare.request.shareProduct;
        return appInfo;
    };

    window.mcShare.weixinAuthBase = function (callback, appId) {

        var code = Utils.request.getSearch('code');
        var tokenData = window.sessionStorage.getItem('tokenData');
        if(tokenData){
            callback(JSON.parse(tokenData))
            return;
        }
        appId = appId || window.wxConfig.authAppId;
        if (!code || code == 'undefined') {
            var params = Utils.request.searchParams();
            var searchStr = '';
            for(var k in params){
                if(Object.prototype.toString.call(params[k]) == '[object Array]'){
                    params[k] = params[k][0];
                }

                if(k == 'shareData'){
                    var shareData = params[k];
                    try{
                        shareData = JSON.parse(shareData);
                        shareData.url = encodeURIComponent(shareData.url);
                        shareData.iconUrl =  encodeURIComponent(shareData.iconUrl);
                        params[k] = JSON.stringify(shareData);
                    }catch (e){
                        params[k] = '';
                    }
                }
                searchStr += (k + '='+ (params[k]) + '&');
            }
            searchStr = searchStr.substr(0, searchStr.length-1);
            var url = encodeURIComponent(window.location.protocol + "//"+ window.location.host + window.location.pathname + "?")+(encodeURIComponent(searchStr));
            var hashstr = decodeURIComponent(window.location.hash);
            var authObj = {
                appid: appId,
                redirect_uri: url.replace(hashstr, ''),
                response_type: 'code',
                scope: 'snsapi_base',
                state: hashstr.replace('#', '') 
            };
            window.location = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + authObj.appid + '&redirect_uri=' + (authObj.redirect_uri) + '&response_type=' + authObj.response_type + '&scope=' + authObj.scope + '&state=' + encodeURIComponent(authObj.state) + '#wechat_redirect';

        } else {
            window.location.hash = Utils.request.getSearch('state');
            Utils.ajax({
                method: 'get',
                data: {
                    code: code,
                    weixinApp: appId,
                    _r: sign(1)
                },
                url: window.location.protocol + '//api-share-m.kakamobi.com/api/open/sign/token.htm',
                dataType: 'json',
                async: true,
                success: function (ret) {
                    window.sessionStorage.setItem('tokenData', JSON.stringify(ret.data))
                    callback(ret.data);
                },
                error: function () {
                    callback('error');
                }
            })
        }
    }

    // window.mcShare.weixinAuthBase = function (callback, appId) {
    //
    //     var code = Utils.request.getSearch('code');
    //
    //     if (!code || code == 'undefined') {
    //         var url = decodeURIComponent(window.location.href);
    //         var hashstr = decodeURIComponent(window.location.hash);
    //         var authObj = {
    //             appid: appId || 'wx39ddb2e3b2dcb858',
    //             redirect_uri: url.replace(hashstr, ''),
    //             response_type: 'code',
    //             scope: 'snsapi_base',
    //             state: hashstr.replace('#', '')
    //         };
    //         window.location = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + authObj.appid + '&redirect_uri=' + encodeURIComponent(authObj.redirect_uri) + '&response_type=' + authObj.response_type + '&scope=' + authObj.scope + '&state=' + encodeURIComponent(authObj.state) + '#wechat_redirect';
    //
    //     } else {
    //         window.location.hash = Utils.request.getSearch('state');
    //         Utils.ajax({
    //             method: 'get',
    //             data: {
    //                 code: code,
    //                 weixinApp: authObj.appid,
    //                 _r: sign(1)
    //             },
    //             // url: 'http://api.share.m.kakamobi.com/api/open/sign/token.htm',
    //             url: window.location.protocol + '//api-share-m.kakamobi.com/api/open/sign/token.htm',
    //             dataType: 'json',
    //             async: true,
    //             success: function (ret) {
    //                 callback(ret.data);
    //             },
    //             error: function () {
    //                 callback('error');
    //             }
    //         })
    //     }
    // }

    window.mcShare.weixinAuth = function (callback, appId, store) {
        appId = appId || window.wxConfig.authAppId;

        var code = Utils.request.getSearch('code');

        if (!code || code == 'undefined') {
            var url = decodeURIComponent(window.location.href);
            var hashstr = decodeURIComponent(window.location.hash);
            var authObj = {
                appid: appId,
                redirect_uri: url.replace(hashstr, ''),
                response_type: 'code',
                scope: 'snsapi_userinfo',
                state: hashstr.replace('#', '')
            };
            window.location = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + authObj.appid + '&redirect_uri=' + encodeURIComponent(authObj.redirect_uri) + '&response_type=' + authObj.response_type + '&scope=' + authObj.scope + '&state=' + encodeURIComponent(authObj.state) + '#wechat_redirect';

        } else {
            window.location.hash = Utils.request.getSearch('state');
            Utils.ajax({
                method: 'get',
                data: {
                    code: code,
                    weixinApp: appId,
                    _r: sign(1)
                },
                // url: store || 'http://api.share.m.kakamobi.com/api/open/sign/read-user-info.htm',
                url: store || window.location.protocol + '//api-share-m.kakamobi.com/api/open/sign/read-user-info.htm',
                dataType: 'json',
                async: true,
                success: function (ret) {
                    callback(ret.data);
                },
                error: function () {
                    callback('error');
                }
            })
        }
    }


    window.mcShare.shareWord = function (shareWords, obj, callback) {
        if (typeof shareWords === 'object') {
            if (requestData.channel !== 'mucang' && requestData.channel == 'weixin') {
                // var flag = false;
                var placeKey = shareWords.shareKey || shareWords.placeKey || requestData.placeKey;
                // var storageData = window.localStorage.getItem(placeKey);
                // if(storageData){
                //     storageData = JSON.parse(storageData);
                //     var curTime = new Date().getTime();
                //     if(curTime < (storageData.time+24*60*60*1000)){
                //         handleShare(requestData.channel, storageData.shareObj, JSON.stringify(storageData.shareObj), callback)
                //     }else{
                //         flag = true;
                //     }
                // }
                // if(!storageData || flag){
                Utils.ajax({
                    url: window.location.protocol+'//share.kakamobi.cn/api/h5/new-share/get-share.htm',
                    data: {
                        shareData: JSON.stringify(shareWords),
                        _productCategory: shareWords.shareProduct || requestData.shareProduct,
                        channel: shareWords.channel || requestData.channel,
                        placeKey: placeKey
                    },
                    method: 'post',
                    dataType: 'json',
                    async: true,
                    success: function (ret) {
                        if(ret.data){
                            shareObj = ret.data;
                            // window.localStorage.setItem(placeKey, JSON.stringify({shareObj: shareObj, time: new Date().getTime()}));
                        }
                        handleShare(requestData.channel, shareObj, JSON.stringify(shareObj), callback);
                    },
                    error: function () {
                        console.log(arguments)
                    }
                });
                // }
            } else {
                pageTitle = document.title;
                shareObj = shareWords;
                handleShare(requestData.channel, shareObj, JSON.stringify(shareObj), callback)
            }
        } else {
            pageTitle = document.title;
            shareObj = obj || shareObj;
            shareObj.shareWords = shareWords;
            if (shareObj.iconUrl && shareObj.iconUrl.indexOf('http') != 0) {
                shareObj.iconUrl = Utils.getAbsPath(shareObj.iconUrl);
            }

            handleShare(requestData.channel, shareObj, JSON.stringify(shareObj), callback)
        }
    };

    window.mcShare.recoverTitle = function () {
        document.title = pageTitle;
    };

}(window, undefined);

!function (window, undefined) {
    var GLOBAL_NAMESPACE_TITLE = 'Share-Message-global';

    // 获取名字空间
    var parseName = function (str) {

        var namespace = '', name = '';

        str = str.split('.');

        if (str.length > 1) {
            namespace = str.pop();

            name = str.splice(0, str.length > 1 ? str.length - 1 : 1).join('.');
        } else {
            name = str.join('.');
        }

        return {
            namespace: namespace || GLOBAL_NAMESPACE_TITLE,
            name: name
        }

    }

    var Message = function () {
        this.events = {};
        this.history = [];
    };

    Message.prototype = {

        on: function (eventStr, callback) {

            eventStr = eventStr.split(/\s/g);

            for (var i = 0; i < eventStr.length; i++) {

                var event = parseName(eventStr[i]);

                if (this.events[event.namespace]) {
                    if (this.events[event.namespace][event.name]) {
                        this.events[event.namespace][event.name].push(callback);
                    } else {
                        this.events[event.namespace][event.name] = [callback];
                    }
                } else {
                    this.events[event.namespace] = {};
                    this.events[event.namespace][event.name] = [callback];
                }

                var history = this.history.indexOf(eventStr[i]);

                if (history > -1) {
                    this.trigger(this.history[history]);
                    for (var i = 0; i < this.history.length; i++) {
                        if (eventStr[i] === this.history[i]) {
                            this.history.splice(i, 1);
                            i--;
                        }
                    }
                }

            }

            return this;

        },

        off: function (eventStr, callback) {

            eventStr = eventStr.split(/\s/g);

            for (var i = 0; i < eventStr.length; i++) {

                var event = parseName(eventStr[i]);

                if (event.name) {
                    if (callback) {
                        if (this.events[event.namespace]) {
                            var funs = this.events[event.namespace][event.name];
                            if (funs) {
                                for (var i = 0; i < funs.length; i++) {
                                    if (funs === callback) {
                                        this.events[event.namespace][event.name].splice(i, 1);
                                    }
                                }
                            }
                        }
                    } else {
                        if (this.events[event.namespace]) {
                            delete this.events[event.namespace][event.name];
                        }
                    }
                } else {
                    delete this.events[event.namespace];
                }

            }

            return this;

        },

        has: function (eventStr, callback) {

            eventStr = eventStr.split(/\s/g);

            for (var i = 0; i < eventStr.length; i++) {

                var event = parseName(eventStr[i]);

                if (event.name) {
                    if (callback) {
                        if (this.events[event.namespace]) {
                            var funs = this.events[event.namespace][event.name];
                            if (funs) {
                                for (var i = 0; i < funs.length; i++) {
                                    if (funs === callback) {
                                        return true;
                                    }
                                }
                            }
                        }
                    } else {
                        if (this.events[event.namespace]) {
                            return true;
                        }
                    }
                } else {
                    return true;
                }

            }

            return false;

        },

        trigger: function (eventStr, history) {

            eventStr = eventStr.split(/\s/g);

            for (var i = 0; i < eventStr.length; i++) {

                var args = arguments;
                var params = [];
                var event = parseName(eventStr[i]);
                history || (history = false);

                for (var i = 2; i < args.length; i++) {
                    params.push(args[i]);
                }

                if (event.name) {
                    if (this.events[event.namespace]) {
                        var fun = this.events[event.namespace][event.name];
                        if (fun) {
                            for (var i = 0; i < fun.length; i++) {
                                fun[i].apply(fun[i], params);
                            }
                        } else {
                            history && this.history.push(eventStr[i]);
                        }
                    } else {
                        history && this.history.push(eventStr[i]);
                    }
                } else {
                    var namespace = this.events[event.namespace];
                    for (var e in namespace) {
                        var fun = namespace[e];
                        if (fun.length > 0) {
                            for (var i = 0; i < fun.length; i++) {
                                fun[i].apply(fun[i], params);
                            }
                        } else {
                            history && this.history.push(eventStr[i]);
                        }
                    }
                }

            }

            return this;

        },

        constructorNameSpace: 'Simple.core.Message'

    };

    Message.prototype.constructor = Message;

    window.Message = new Message();
}(window, undefined);
