/* eslint-disable */
!(function (window) {

    var wxCon = {regAppId: 'wxee24b820c98aec79', authAppId: 'wx39ddb2e3b2dcb858'};

    var split = function (text, lineSplitter, fieldSplitter) {
        var list = {};
        if (text) {
            lineSplitter = lineSplitter || "\n";
            fieldSplitter = fieldSplitter || "\t";
            var pairs = text.split(lineSplitter);
            for (var i = 0; i < pairs.length; i++) {
                var parts = pairs[i].split(fieldSplitter);
                var keyname = parts[0];
                try {
                    var keyvalue = decodeURIComponent(parts[1]);
                } catch (e) {
                    keyvalue = parts[1];
                }

                if (typeof list[keyname] === "undefined") {
                    list[keyname] = keyvalue;
                } else if (typeof list[keyname] === "string") {
                    var arr = [list[keyname], keyvalue];
                    list[keyname] = arr;
                } else if (keyname) {
                    list[keyname].push(keyvalue || '');
                }
            }
        }
        return list;
    };
    var params = function (data) {
        var arr = [];
        for (var i in data) {
            arr.push(encodeURIComponent(i) + '=' + encodeURIComponent(data[i]));
        }
        return arr.join('&');
    }
    var getSearch = function (key) {
        return searchParams()[key];
    };
    var searchParams = function () {
        return split((window.location.search.replace(/^\?/ig, '')), '&', '=');
    }

    var sign = eval(function (p, a, c, k, e, d) {
        e = function (c) {
            return c.toString(36)
        };
        if (!''.replace(/^/, String)) {
            while (c--) {
                d[c.toString(a)] = k[c] || c.toString(a)
            }
            k = [function (e) {
                return d[e]
            }];
            e = function () {
                return '\\w+'
            };
            c = 1
        }
        ;
        while (c--) {
            if (k[c]) {
                p = p.replace(new RegExp('\\b' + e(c) + '\\b', 'g'), k[c])
            }
        }
        return p
    }('o.n=7(a){2 c=9.p(8(q m().r()*9.i()*k)).5();2 d=0;l(2 b=0;b<c.4;b++){d+=8(c[b])}2 e=7(f){6 7(g,h){6(0>=(h-""+g.4))?g:(f[h]||(f[h]=j(h+1).s(0)))+g}}([]);d+=c.4;d=e(d,3-d.5().4);6 a.5()+c+d};', 29, 29, '||var||length|toString|return|function|parseInt|Math|||||||||random|Array|10000|for|Date|sign|window|abs|new|getTime|join'.split('|'), 0, {}))

    
    var ajax = function (obj) {
        var xhr = new XMLHttpRequest();


        if (typeof obj.data == 'object' && obj.contentType !== false) {
            obj.data = params(obj.data);
        }

        if (obj.method == 'get') {
            obj.url = obj.url.indexOf('?') === -1 ? obj.url + '?' + obj.data : obj.url + '&' + obj.data;
        }
        if (obj.async === true) {
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {

                    callback();
                }
            }
        }
        xhr.open(obj.method, obj.url, true);
        xhr.withCredentials = true;
        xhr.setRequestHeader('X-Data-Type', obj.dataType);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        if (obj.contentType !== false) {
            xhr.setRequestHeader("Content-Type", obj.contentType || "application/x-www-form-urlencoded");
        }
        if (obj.method === 'post') {
            xhr.send(obj.data);
        } else {
            xhr.send(null)
        }

        if (obj.async === false) {
            callback();
        }

        function callback() {
            if (xhr.status === 200) {
                try {
                    obj.success(JSON.parse(xhr.responseText));
                } catch (e) {
                    console.log(e)
                }

            } else {
                obj.error(xhr.status, xhr.statusText)
            }
        }

    }

    window.wxActive = window.wxActive || {}

    window.wxActive.weixinAuth = function (callback, appId) {

        var code = getSearch('code');
        var tokenData = window.sessionStorage.getItem('tokenData');
        if(tokenData){
            callback(JSON.parse(tokenData))
            return;
        }
        if (!code || code == 'undefined') {
            var params = searchParams();
            var searchStr = '';
            for(var k in params){
                if(Object.prototype.toString.call(params[k]) == '[object Array]'){
                    params[k] = params[k][0];
                }

                if(k == 'shareData'){
                    var shareData = params[k];
                    try{
                        shareData = JSON.parse(shareData);
                        shareData.url = encodeURIComponent(shareData.url);
                        shareData.iconUrl =  encodeURIComponent(shareData.iconUrl);
                        params[k] = JSON.stringify(shareData);
                    }catch (e){
                        params[k] = '';
                    }
                }
                searchStr += (k + '='+ (params[k]) + '&');
            }
            searchStr = searchStr.substr(0, searchStr.length-1);
            var url = encodeURIComponent(window.location.protocol + "//"+ window.location.host + window.location.pathname + "?")+(encodeURIComponent(searchStr));
            var hashstr = decodeURIComponent(window.location.hash);
            var authObj = {
                appid: appId || wxCon.authAppId,
                redirect_uri: url.replace(hashstr, ''),
                response_type: 'code',
                scope: 'snsapi_base',
                state: hashstr.replace('#', '') 
            };
            window.location = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + authObj.appid + '&redirect_uri=' + (authObj.redirect_uri) + '&response_type=' + authObj.response_type + '&scope=' + authObj.scope + '&state=' + encodeURIComponent(authObj.state) + '#wechat_redirect';

        } else {
            window.location.hash = getSearch('state');
            ajax({
                method: 'get',
                data: {
                    code: code,
                    weixinApp: appId || wxCon.authAppId,
                    _r: sign(1)
                },
                url: window.location.protocol + '//api-share-m.kakamobi.com/api/open/sign/token.htm',
                dataType: 'json',
                async: true,
                success: function (ret) {
                    window.sessionStorage.setItem('tokenData', JSON.stringify(ret.data))
                    callback(ret.data);
                },
                error: function () {
                    callback('error');
                }
            })
        }
    }

    window.wxActive.wxConfig = function(appId, callback) {
        ajax({
            method: 'get',
            data: {
                url: window.location.href.replace(/#.*/ig, ''),
                '_r': sign(1),
                weixinApp: appId || wxCon.regAppId
            },
            url: window.location.protocol + '//api-share-m.kakamobi.com/api/open/sign/jssign.htm',
            dataType: 'json',
            async: true,
            success: function (ret) {
                var signObj = ret.data
                wx.config({
                    debug: false,
                    appId: appId || wxCon.regAppId,
                    timestamp: signObj.timestamp,
                    nonceStr: signObj.noncestr,
                    signature: signObj.signature,
                    jsApiList: [
                        'checkJsApi',
                        'hideMenuItems',
                        'showMenuItems',
                        'hideAllNonBaseMenuItem',
                        'showAllNonBaseMenuItem',
                        'updateAppMessageShareData',
                        'updateTimelineShareData',
                        'translateVoice',
                        'startRecord',
                        'stopRecord',
                        'onRecordEnd',
                        'playVoice',
                        'pauseVoice',
                        'stopVoice',
                        'uploadVoice',
                        'downloadVoice',
                        'chooseImage',
                        'previewImage',
                        'uploadImage',
                        'downloadImage',
                        'getNetworkType',
                        'openLocation',
                        'getLocation',
                        'hideOptionMenu',
                        'showOptionMenu',
                        'closeWindow',
                        'scanQRCode',
                        'chooseWXPay',
                        'openProductSpecificView',
                        'addCard',
                        'chooseCard',
                        'openCard'
                    ],
                    openTagList: [
                        'wx-open-launch-app',
                        'wx-open-launch-weapp',
                        'wx-open-subscribe',
                        'wx-open-audio'
                    ]
                });
                callback && callback();
            }
        })
    }
})(window);
