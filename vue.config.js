var path = require('path')
var glob = require('glob')
var pkg = require('./package.json')
var ZipPlugin = require('zip-webpack-plugin')
var HtmlWebpackPlugin = require('html-webpack-plugin')
var WeixinShareWebpackPlugin = require('./webpack/weixin-share-webpack-plugin')
var VconsoleWebpackPlugin = require('./webpack/vconsole-webpack-plugin')
var AsyncEntryWebpackPlugin = require('./webpack/async-entry-webpack-plugin')

process.env.VUE_APP_MODE = process.env.VUE_APP_MODE || null

try {
    var baseWidth = pkg.projectConfig.style.baseWidth
} catch (error) {
    baseWidth = 750
}
var _static = pkg.projectConfig.static
var copyList = _static.map(function(item) {
    return {
        from: item,
        to: item,
    }
})
function generatePages() {
    // 默认查询多页面地址
    var PATH_ENTRY = path.resolve(__dirname, './src/pages')
    // 约定构建出的页面用folder名字，默认入口为每个页面的main.js
    var entryFilePaths = glob.sync(PATH_ENTRY + '/**/main.js')
    var pages = []

    entryFilePaths.forEach(function(filePath) {
        var page = filePath.match(/([^/]+)\/main\.js$/)[1]
        pages.push(page)
    })

    return pages
}
var pageList = generatePages()
function generateEntries() {
    var entry = {}
    pageList.forEach(function(chunk) {
        let template = 'index'
        // let chunks = ['chunk-vendors', 'chunk-common', chunk]
        if (
            chunk === 'index' ||
            chunk === 'pure-live' ||
            chunk === 'vip-live' ||
            chunk === 'third-anchor-live'
        ) {
            template = 'video'
            // chunks = ['chunk-vendors', 'chunk-common', 'chunk-player', chunk]
        }
        entry[chunk] = {
            entry: 'src/pages/' + chunk + '/main.js',
            template: 'public/' + template + '.html',
            filename: chunk + '.html',
            // 占位，防止vue-cli自动添加chunks值，导致vendors不会注入到html
            chunks: [chunk],
            isNeedOpenid: ['index'].indexOf(chunk) > -1,
            isNeedOpenTag: ['third-anchor-live'].indexOf(chunk) > -1,
        }
    })
    return entry
}

module.exports = {
    publicPath: './',
    outputDir: './build' + (process.env.NODE_ENV === 'production' ? '/dist' : '/dev') + '/web',
    // 默认情况下 babel-loader 会忽略所有 node_modules 中的文件。如果你想要通过 Babel 显式转译一个依赖，可以在这个选项中列出来。
    transpileDependencies: ['vue-virtual-scroller', 'swiper', 'dom7'],
    productionSourceMap: false,
    pages: generateEntries(),
    chainWebpack: config => {
        config.optimization.splitChunks({
            maxInitialRequests: Infinity,
            cacheGroups: {
                vendors: {
                    name: 'chunk-vendors',
                    //     test: /[\\/]node_modules[\\/]/,
                    test: module => {
                        return /vue|vuex|core-js|simplex/.test(module.context)
                    },
                    priority: -10,
                    chunks: 'initial',
                },
                common: {
                    name: 'chunk-common',
                    minChunks: 2,
                    priority: -20,
                    chunks: 'initial',
                    reuseExistingChunk: true,
                },
                player: {
                    name: 'chunk-player',
                    test: module => {
                        return /pages\\index|tcPlayer|aliPlayer|videoPlayer|mqttws31/.test(
                            module.resource
                        )
                    },
                    priority: -15,
                    chunks: 'initial',
                },
            },
        })

        config.resolve.alias
            .set('@simplex/simple-mcprotocol', path.resolve(__dirname, './node_modules/@simplex/simple-mcprotocol'))

        config.output
            .filename(
                process.env.NODE_ENV === 'production'
                    ? 'js/[name]-[chunkhash:5].js'
                    : 'js/[name].js?[chunkhash:5]'
            )
            .chunkFilename(
                process.env.NODE_ENV === 'production'
                    ? '[name]-[chunkhash:5].js'
                    : '[name].js?[chunkhash:5]'
            )

        config.plugin('extract-css').tap(args => {
            return [
                {
                    filename:
                        process.env.NODE_ENV === 'production'
                            ? 'css/[name]-[contenthash:5].css'
                            : 'css/[name].css?[contenthash:5]',
                },
            ]
        })
        config.plugin('define').tap(args => {
            args[0] = Object.assign(args[0], {
                BASE_WIDTH: baseWidth,
            })
            return args
        })

        config.plugin('copy').tap(args => {
            return [
                copyList,
                {
                    ignore: ['.*'],
                },
            ]
        })
        if (
            (process.env.VUE_APP_MODE === 'test' && process.env.NODE_ENV === 'production') ||
            process.env.npm_config_vconsole
        ) {
            config.plugin('vconsole').use(VconsoleWebpackPlugin)
        }

        config.plugin('async-entry').use(AsyncEntryWebpackPlugin)
        config.plugin('weixin-share').use(WeixinShareWebpackPlugin)

        config.when(process.env.NODE_ENV === 'production', config => {
            config.plugin('zip').use(ZipPlugin, [
                {
                    path: path.join(__dirname, '../', path.basename(__dirname) + '-bundle'),
                    filename: 'project.zip',
                },
            ])

            if (process.env.npm_config_report) {
                config
                    .plugin('webpack-bundle-analyzer')
                    .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
            }
        })

        pageList.forEach(function(chunk) {
            config.plugins.delete('preload-' + chunk)
            config.plugins.delete('prefetch-' + chunk)
        })

        // var rules = ['css', 'postcss', 'scss', 'sass', 'less', 'stylus']
        // var uses = ['vue-modules', 'vue', 'normal-modules', 'normal']
        // rules.forEach(function(rule){
        //     uses.forEach(function(use){
        //         config.module.rule(rule)
        //         .oneOf(use).use('extract-css-loader')
        //         .options({ publicPath: '../../' });
        //     })
        // })

        config.module.rule('images').uses.clear()
        config.module.rule('svg').uses.clear()
        config.module.rule('media').uses.clear()
        config.module.rule('fonts').uses.clear()
        config.module
            .rule('files')
            .test(/\.(png|jpg|jpeg|gif|webp|pag|ttf|eot|otf|woff|svg)$/)
            .use('file')
            .loader('file-loader')
            .options({
                name: 'files/[hash:8].[ext]',
            })
        pageList.forEach(page => {
            config.plugin('html-' + page).init((Plugin, args) => {
                return new HtmlWebpackPlugin(...args)
            })
        })
        return config
    },
    css: {
        requireModuleExtension: true,
        extract: true,
    },
}
