const HtmlWebpackPlugin = require('html-webpack-plugin')
function AsyncEntryWebpackPlugin(options) {}

AsyncEntryWebpackPlugin.prototype.apply = function(compiler) {
    compiler.hooks.compilation.tap('AsyncEntryWebpackPlugin', compilation => {
        // Static Plugin interface |compilation |HOOK NAME | register listener
        HtmlWebpackPlugin.getHooks(compilation).afterTemplateExecution.tapAsync(
            'AsyncEntryWebpackPlugin',
            (data, cb) => {
                let len = data.bodyTags.length
                data.bodyTags[len-1].attributes.async = true
                // Tell webpack to move on
                cb(null, data)
            }
        )
    })
}

module.exports = AsyncEntryWebpackPlugin
