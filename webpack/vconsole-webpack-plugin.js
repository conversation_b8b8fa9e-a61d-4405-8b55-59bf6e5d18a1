const HtmlWebpackPlugin = require('html-webpack-plugin')
function VconsoleWebpackPlugin(options) {}

VconsoleWebpackPlugin.prototype.apply = function(compiler) {
    compiler.hooks.compilation.tap('VconsoleWebpackPlugin', compilation => {
        // Static Plugin interface |compilation |HOOK NAME | register listener
        HtmlWebpackPlugin.getHooks(compilation).beforeEmit.tapAsync(
            'VconsoleWebpackPlugin',
            (data, cb) => {
                // Manipulate the content
                data.html = data.html.replace(
                    '</title>',
                    '</title>' +
                        `
                    <script>
                        var ua = window.navigator.userAgent.toLowerCase()
                        var isAndroid = ua.indexOf('android') > -1
                        var isHarmony = ua.indexOf('harmony') > -1
                        function loadScript(url) {
                            document.write(
                                '<scr' + 'ipt type="text/javascript" src="' + url + '"></sc' + 'ript>'
                            )
                        }
                        if (isHarmony) {
                            loadScript('static/vconsole-3.15.0.min.js')
                        } else if (isAndroid) {
                            var match = ua.match(/android\\s([0-9\.]*)/)
                            var androidVersion = match ? match[1] : null
                            androidVersion = androidVersion.split('.')
                            if (androidVersion.length > 2) {
                                androidVersion.length = 2
                            }
                            androidVersion = androidVersion.join('.')
                            androidVersion = +androidVersion
                            if (androidVersion >= 9) {
                                loadScript('static/vconsole-3.15.0.min.js')
                            } else {
                                loadScript('static/vconsole-3.1.0.min.js')
                            }
                        } else {
                            var IOSVersion = ua.match(/cpu iphone os (.*?) like mac os/)
                            IOSVersion = IOSVersion && IOSVersion[1]
                            IOSVersion = IOSVersion && IOSVersion.split('_')
                            IOSVersion && (IOSVersion.length = Math.min(2, IOSVersion.length))
                            IOSVersion = IOSVersion && +IOSVersion.join('.')
                            if (IOSVersion >= 14) {
                                loadScript('static/vconsole-3.15.0.min.js')
                            } else {
                                loadScript('static/vconsole-3.1.0.min.js')
                            }
                        }
                    </script>
                    <script>
                        localStorage.setItem('vConsole_switch_y', 60)
                        var myPlugin = new VConsole.VConsolePlugin('my_plugin', 'Other');
                        myPlugin.on('renderTab', function(callback) {
                            callback('');
                        });
                        myPlugin.on('addTool', function(callback) {
                            var button = {
                                name: 'Reload',
                                onClick: function(event) {
                                    location.reload();
                                }
                            };
                            callback([button]);
                        });
                        window.vConsole = new VConsole()
                        vConsole.addPlugin(myPlugin);
                    </script>
                `
                )
                // Tell webpack to move on
                cb(null, data)
            }
        )
    })
}

module.exports = VconsoleWebpackPlugin
