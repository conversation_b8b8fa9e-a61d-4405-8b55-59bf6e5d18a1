const HtmlWebpackPlugin = require('html-webpack-plugin')
function WeixinShareWebpackPlugin(options) {}

WeixinShareWebpackPlugin.prototype.apply = function(compiler) {
    compiler.hooks.compilation.tap('WeixinShareWebpackPlugin', compilation => {
        // Static Plugin interface |compilation |HOOK NAME | register listener
        HtmlWebpackPlugin.getHooks(compilation).beforeEmit.tapAsync(
            'WeixinShareWebpackPlugin',
            (data, cb) => {
                let isNeedOpenid, isNeedOpenTag
                try {
                    isNeedOpenid = data.plugin.options.isNeedOpenid
                    isNeedOpenTag = data.plugin.options.isNeedOpenTag
                } catch (error) {}
                // Manipulate the content
                data.html = data.html.replace(
                    '</title>',
                    '</title>' +
                    `
                        <script>
                            var ua = window.navigator.userAgent.toLowerCase()
                            var isWeixin = ua.indexOf('micromessenger') > -1
                            function loadScript(url) {
                                document.write(
                                    '<scr' + 'ipt type="text/javascript" src="' + url + '"></sc' + 'ript>'
                                )
                            }
                            if (isWeixin) {
                                loadScript('static/wxActive.js')
                                loadScript('https://res.wx.qq.com/open/js/jweixin-1.6.0.js')
                            }
                        </script>
                    ` +
                    `
                    <script>
                        if (isWeixin) {` +
                            (isNeedOpenTag ? `window.wxActive.wxConfig('wxa8f00655069a17ae')` : `window.wxActive.wxConfig('wx39ddb2e3b2dcb858')`) +
                        `}
                    </script>` +
                    (isNeedOpenid ? `
                        <script>
                            if (isWeixin) {
                                window.wxActive.weixinAuth(function() {})
                            }
                        </script>
                    ` : '')
                )
                // Tell webpack to move on
                cb(null, data)
            }
        )
    })
}

module.exports = WeixinShareWebpackPlugin
